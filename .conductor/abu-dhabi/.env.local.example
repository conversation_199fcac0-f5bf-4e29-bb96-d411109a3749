# 本地开发环境配置示例
# 复制此文件为 .env 并填入实际值

# ===========================================
# 基本应用配置
# ===========================================

# 运行环境 (development, production, test)
NODE_ENV=development

# 服务器端口
PORT=3456

# ===========================================
# MySQL 数据库配置
# ===========================================

# 数据库主机
DB_HOST=localhost

# 数据库端口
DB_PORT=3669

# 数据库名称
DB_NAME=wolf_fun_db

# 数据库用户名
DB_USER=root

# 数据库密码
DB_PASS=root

# 数据库方言（固定为mysql）
DB_DIALECT=mysql

# ===========================================
# Redis 配置
# ===========================================

# Redis 主机
REDIS_HOST=localhost

# Redis 端口
REDIS_PORT=6257

# Redis 密码（可选）
REDIS_PASSWORD=

# ===========================================
# JWT 认证配置
# ===========================================

# JWT 密钥（请使用强随机字符串）
JWT_SECRET=your-super-secure-jwt-secret-here-change-this-in-production

# JWT 过期时间（默认24小时）
JWT_EXPIRES_IN=24h

# ===========================================
# 迁移脚本配置
# ===========================================

# 是否显示SQL日志
DEBUG=false

# 迁移失败后是否继续执行
CONTINUE_ON_ERROR=false

# 确认危险操作（reset, fresh等）
FORCE=false

# ===========================================
# 其他配置
# ===========================================

# 时区设置
TZ=Asia/Shanghai

# 日志级别 (debug, info, warn, error)
LOG_LEVEL=info