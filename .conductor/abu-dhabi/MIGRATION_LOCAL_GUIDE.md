# 本地数据库迁移脚本使用指南

## 概述
`run_local_migrations.js` 是一个用于管理本地数据库迁移的命令行工具，可以自动读取本地环境配置并执行迁移文件。

## 快速开始

### 1. 配置环境变量
```bash
# 复制环境配置示例文件
cp .env.local.example .env

# 编辑 .env 文件，填入你的数据库配置
# 主要配置项：
# - DB_HOST: 数据库主机地址
# - DB_PORT: 数据库端口（默认3669）
# - DB_NAME: 数据库名称
# - DB_USER: 数据库用户名
# - DB_PASS: 数据库密码
```

### 2. 基本使用

#### 查看迁移状态
```bash
node run_local_migrations.js status
```

#### 执行所有待迁移文件
```bash
node run_local_migrations.js up
# 或
node run_local_migrations.js migrate
```

#### 回滚迁移
```bash
# 回滚最后一个迁移
node run_local_migrations.js down

# 回滚最后3个迁移
node run_local_migrations.js rollback 3
```

#### 重置所有迁移（危险操作）
```bash
# 需要设置 FORCE=true 环境变量
FORCE=true node run_local_migrations.js reset
```

#### 重新执行所有迁移（危险操作）
```bash
# 回滚并重新执行所有迁移
FORCE=true node run_local_migrations.js fresh
```

## 命令详解

### `up` / `migrate`
执行所有未运行的迁移文件。脚本会：
1. 检查 `SequelizeMeta` 表中的记录
2. 找出未执行的迁移文件
3. 按文件名顺序执行迁移
4. 记录执行成功的迁移

### `down` / `rollback [n]`
回滚指定数量的迁移（默认1个）。脚本会：
1. 找出最近执行的n个迁移
2. 按倒序执行它们的 `down` 方法
3. 从 `SequelizeMeta` 表中删除记录

### `status`
显示当前迁移状态，包括：
- 已执行的迁移列表
- 待执行的迁移列表

### `reset`
回滚所有已执行的迁移。**注意：这是危险操作，需要设置 `FORCE=true`**

### `fresh`
回滚所有迁移后重新执行。**注意：这是危险操作，需要设置 `FORCE=true`**

## 环境变量

### 数据库配置
- `DB_HOST`: 数据库主机地址（默认: localhost）
- `DB_PORT`: 数据库端口（默认: 3669）
- `DB_NAME`: 数据库名称（默认: wolf_fun_db）
- `DB_USER`: 数据库用户名（默认: root）
- `DB_PASS`: 数据库密码（默认: root）

### 脚本行为控制
- `DEBUG=true`: 显示详细的SQL日志
- `CONTINUE_ON_ERROR=true`: 迁移失败后继续执行其他迁移
- `FORCE=true`: 确认执行危险操作（reset, fresh）
- `NODE_ENV`: 运行环境（development/production/test）

## 特性

### 事务支持
每个迁移都在事务中执行，失败时自动回滚，确保数据一致性。

### 重复执行保护
脚本会自动处理以下情况：
- 重复的字段名（Duplicate column name）
- 重复的索引名（Duplicate key name）
- 已存在的表（already exists）

当检测到这些错误时，脚本会跳过该迁移并标记为已执行。

### 彩色输出
脚本使用不同颜色标识不同类型的信息：
- 🔵 蓝色：执行步骤
- 🟢 绿色：成功信息
- 🟡 黄色：警告信息
- 🔴 红色：错误信息
- 🟣 紫色：标题信息
- 🔷 青色：一般信息

## 示例

### 开发环境初始化
```bash
# 1. 配置环境变量
cp .env.local.example .env
vim .env  # 编辑数据库配置

# 2. 查看迁移状态
node run_local_migrations.js status

# 3. 执行所有迁移
node run_local_migrations.js up

# 4. 验证执行结果
node run_local_migrations.js status
```

### 调试模式执行
```bash
# 显示详细SQL日志
DEBUG=true node run_local_migrations.js up

# 迁移失败后继续执行
CONTINUE_ON_ERROR=true node run_local_migrations.js up
```

### 生产环境迁移
```bash
# 使用生产环境配置
NODE_ENV=production node run_local_migrations.js up
```

## 故障排除

### 1. 数据库连接失败
检查 `.env` 文件中的数据库配置是否正确：
- 确认数据库服务已启动
- 确认主机和端口正确
- 确认用户名和密码正确
- 确认数据库名称存在

### 2. 迁移执行失败
- 查看详细错误信息
- 使用 `DEBUG=true` 查看SQL日志
- 检查迁移文件语法是否正确
- 确认数据库用户有足够权限

### 3. 重复字段/索引错误
这通常表示迁移已经部分执行过，脚本会自动处理并跳过。

### 4. 回滚失败
- 确认迁移文件包含 `down` 方法
- 检查 `down` 方法的逻辑是否正确
- 某些操作可能无法回滚（如数据删除）

## 迁移文件规范

迁移文件应放在 `migrations/` 目录下，命名格式：
```
YYYYMMDDHHMMSS-描述.js
```

迁移文件结构：
```javascript
module.exports = {
  up: async (queryInterface, Sequelize) => {
    // 执行迁移的逻辑
  },
  
  down: async (queryInterface, Sequelize) => {
    // 回滚迁移的逻辑（可选）
  }
};
```

## 安全建议

1. **备份数据库**：在执行迁移前，特别是在生产环境，务必备份数据库
2. **测试环境验证**：先在测试环境执行迁移，验证无误后再在生产环境执行
3. **逐步执行**：对于重要迁移，建议逐个执行并验证
4. **保留日志**：使用 `DEBUG=true` 保存执行日志，便于问题排查

## 与 Sequelize CLI 的对比

| 特性 | run_local_migrations.js | Sequelize CLI |
|-----|------------------------|---------------|
| 环境配置 | 自动读取 .env | 需要 config.js |
| 错误处理 | 自动处理重复错误 | 需手动处理 |
| 彩色输出 | ✅ 支持 | ✅ 支持 |
| 事务支持 | ✅ 每个迁移独立事务 | ✅ 支持 |
| 批量操作 | ✅ 支持继续执行 | ❌ 失败即停止 |
| 状态显示 | ✅ 详细状态 | ✅ 基本状态 |

## 许可证
MIT