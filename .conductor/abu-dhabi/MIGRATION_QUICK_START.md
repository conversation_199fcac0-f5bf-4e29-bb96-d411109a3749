# 数据库迁移快速开始指南

## 🚀 快速开始（3步）

### 1️⃣ 配置数据库连接
```bash
# 复制环境配置文件
cp .env.local.example .env

# 编辑配置文件（修改数据库连接信息）
vim .env
```

### 2️⃣ 查看迁移状态
```bash
npm run migrate:local:status
```

### 3️⃣ 执行迁移
```bash
npm run migrate:local
```

---

## 📋 常用命令

### 使用 npm 脚本（推荐）
```bash
# 查看状态
npm run migrate:local:status

# 执行迁移
npm run migrate:local

# 回滚最后一个迁移
npm run migrate:local:rollback

# 重置所有迁移（危险）
npm run migrate:local:reset

# 重新执行所有迁移（危险）
npm run migrate:local:fresh

# 打开交互式菜单
npm run migrate:local:menu
```

### 直接使用脚本
```bash
# 查看状态
node run_local_migrations.js status

# 执行迁移
node run_local_migrations.js up

# 回滚迁移
node run_local_migrations.js down

# 交互式菜单
./migrate.sh
```

---

## 🎯 典型使用场景

### 场景1：首次初始化数据库
```bash
# 1. 配置环境
cp .env.local.example .env
vim .env

# 2. 执行所有迁移
npm run migrate:local

# 3. 验证结果
npm run migrate:local:status
```

### 场景2：更新到最新迁移
```bash
# 1. 查看待执行的迁移
npm run migrate:local:status

# 2. 执行迁移
npm run migrate:local
```

### 场景3：回滚错误的迁移
```bash
# 回滚最后一个迁移
npm run migrate:local:rollback

# 或回滚多个
node run_local_migrations.js rollback 3
```

### 场景4：调试迁移问题
```bash
# 显示详细SQL日志
DEBUG=true npm run migrate:local

# 失败后继续执行
CONTINUE_ON_ERROR=true npm run migrate:local
```

---

## ⚙️ 环境配置示例

`.env` 文件最小配置：
```env
# 数据库配置
DB_HOST=localhost
DB_PORT=3669
DB_NAME=wolf_fun_db
DB_USER=root
DB_PASS=yourpassword

# JWT密钥（必需）
JWT_SECRET=your-secret-key
```

---

## ❓ 常见问题

### Q: 数据库连接失败？
A: 检查 `.env` 文件中的数据库配置，确保数据库服务已启动。

### Q: 提示"Duplicate column name"错误？
A: 这表示字段已存在，脚本会自动跳过，无需担心。

### Q: 如何完全重置数据库？
A: 使用 `npm run migrate:local:fresh`（需要确认）

### Q: 如何查看SQL执行日志？
A: 使用 `DEBUG=true npm run migrate:local`

---

## 📚 更多信息

详细文档请查看：[MIGRATION_LOCAL_GUIDE.md](./MIGRATION_LOCAL_GUIDE.md)