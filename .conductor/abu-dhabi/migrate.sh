#!/bin/bash

# 本地数据库迁移快速执行脚本

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
MAGENTA='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${CYAN}ℹ${NC}  $1"
}

print_success() {
    echo -e "${GREEN}✓${NC}  $1"
}

print_warning() {
    echo -e "${YELLOW}⚠${NC}  $1"
}

print_error() {
    echo -e "${RED}✗${NC}  $1"
}

print_title() {
    echo -e "\n${MAGENTA}═══ $1 ═══${NC}\n"
}

# 检查 Node.js 是否安装
check_node() {
    if ! command -v node &> /dev/null; then
        print_error "Node.js 未安装，请先安装 Node.js"
        exit 1
    fi
    
    NODE_VERSION=$(node -v)
    print_info "Node.js 版本: $NODE_VERSION"
}

# 检查环境文件
check_env() {
    if [ ! -f ".env" ]; then
        if [ -f ".env.local.example" ]; then
            print_warning ".env 文件不存在，从示例文件创建..."
            cp .env.local.example .env
            print_info "请编辑 .env 文件配置数据库连接信息"
            print_info "使用命令: vim .env"
            exit 0
        else
            print_error ".env 文件不存在，请先配置环境变量"
            exit 1
        fi
    fi
}

# 检查迁移脚本
check_migration_script() {
    if [ ! -f "run_local_migrations.js" ]; then
        print_error "迁移脚本 run_local_migrations.js 不存在"
        exit 1
    fi
}

# 显示菜单
show_menu() {
    print_title "数据库迁移工具"
    echo "请选择操作:"
    echo "  1) 查看迁移状态"
    echo "  2) 执行所有待迁移文件"
    echo "  3) 回滚最后一个迁移"
    echo "  4) 回滚指定数量的迁移"
    echo "  5) 重置所有迁移 (危险)"
    echo "  6) 重新执行所有迁移 (危险)"
    echo "  7) 调试模式执行迁移"
    echo "  8) 退出"
    echo
}

# 确认危险操作
confirm_dangerous() {
    print_warning "这是一个危险操作！"
    read -p "请输入 'yes' 确认执行: " confirmation
    if [ "$confirmation" != "yes" ]; then
        print_info "操作已取消"
        return 1
    fi
    return 0
}

# 主函数
main() {
    print_title "本地数据库迁移工具"
    
    # 检查环境
    check_node
    check_env
    check_migration_script
    
    # 如果提供了命令行参数，直接执行
    if [ $# -gt 0 ]; then
        case "$1" in
            status)
                node run_local_migrations.js status
                ;;
            up|migrate)
                node run_local_migrations.js up
                ;;
            down|rollback)
                if [ -n "$2" ]; then
                    node run_local_migrations.js down "$2"
                else
                    node run_local_migrations.js down
                fi
                ;;
            reset)
                if confirm_dangerous; then
                    FORCE=true node run_local_migrations.js reset
                fi
                ;;
            fresh)
                if confirm_dangerous; then
                    FORCE=true node run_local_migrations.js fresh
                fi
                ;;
            *)
                print_error "未知命令: $1"
                echo "可用命令: status, up, migrate, down, rollback, reset, fresh"
                exit 1
                ;;
        esac
        exit 0
    fi
    
    # 交互式菜单
    while true; do
        show_menu
        read -p "请选择 (1-8): " choice
        
        case $choice in
            1)
                print_title "迁移状态"
                node run_local_migrations.js status
                ;;
            2)
                print_title "执行迁移"
                node run_local_migrations.js up
                ;;
            3)
                print_title "回滚迁移"
                node run_local_migrations.js down
                ;;
            4)
                read -p "请输入要回滚的数量: " count
                if [[ "$count" =~ ^[0-9]+$ ]]; then
                    node run_local_migrations.js rollback "$count"
                else
                    print_error "请输入有效的数字"
                fi
                ;;
            5)
                print_title "重置迁移"
                if confirm_dangerous; then
                    FORCE=true node run_local_migrations.js reset
                fi
                ;;
            6)
                print_title "重新执行所有迁移"
                if confirm_dangerous; then
                    FORCE=true node run_local_migrations.js fresh
                fi
                ;;
            7)
                print_title "调试模式执行迁移"
                DEBUG=true CONTINUE_ON_ERROR=true node run_local_migrations.js up
                ;;
            8)
                print_success "再见！"
                exit 0
                ;;
            *)
                print_error "无效的选择，请输入 1-8"
                ;;
        esac
        
        echo
        read -p "按回车键继续..."
    done
}

# 执行主函数
main "$@"