'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    // 检查 productId 列是否已存在
    const tableDescription = await queryInterface.describeTable('iap_products');
    
    // 如果 productId 列不存在，则添加该列
    if (!tableDescription.productId) {
      await queryInterface.addColumn('iap_products', 'productId', {
        type: Sequelize.STRING,
        allowNull: false,
        unique: true,
        after: 'id'
      });
    }
  },

  down: async (queryInterface, Sequelize) => {
    // 检查 productId 列是否存在
    const tableDescription = await queryInterface.describeTable('iap_products');
    
    // 如果 productId 列存在，则删除该列
    if (tableDescription.productId) {
      await queryInterface.removeColumn('iap_products', 'productId');
    }
  }
};