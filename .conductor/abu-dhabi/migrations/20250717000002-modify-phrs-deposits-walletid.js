'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    // 检查表是否存在
    const tables = await queryInterface.showAllTables();
    if (!tables.includes('phrs_deposits')) {
      console.log('phrs_deposits 表不存在，跳过迁移');
      return;
    }

    // 检查字段是否存在
    const tableDescription = await queryInterface.describeTable('phrs_deposits');
    if (!tableDescription.walletId) {
      console.log('walletId 字段不存在，跳过修改');
      return;
    }

    // 修改 walletId 字段，允许为空
    await queryInterface.changeColumn('phrs_deposits', 'walletId', {
      type: Sequelize.INTEGER.UNSIGNED,
      allowNull: true, // 允许为空
      references: {
        model: 'user_wallets',
        key: 'id'
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL' // 当用户钱包被删除时，设置为NULL而不是删除记录
    });
  },

  down: async (queryInterface, Sequelize) => {
    // 检查表是否存在
    const tables = await queryInterface.showAllTables();
    if (!tables.includes('phrs_deposits')) {
      console.log('phrs_deposits 表不存在，跳过回滚');
      return;
    }

    // 检查字段是否存在
    const tableDescription = await queryInterface.describeTable('phrs_deposits');
    if (!tableDescription.walletId) {
      console.log('walletId 字段不存在，跳过回滚');
      return;
    }

    // 回滚：将 walletId 字段改回不允许为空
    // 注意：这可能会失败，如果表中有NULL值
    await queryInterface.changeColumn('phrs_deposits', 'walletId', {
      type: Sequelize.INTEGER.UNSIGNED,
      allowNull: false,
      references: {
        model: 'user_wallets',
        key: 'id'
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE'
    });
  }
};
