'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();

    try {
      console.log('开始将 phrsBalance 字段精度从 3 位改为 18 位...');

      // 检查表是否存在
      const tables = await queryInterface.showAllTables();
      
      if (!tables.includes('user_wallets')) {
        console.log('⚠️  user_wallets 表不存在，跳过修复');
        await transaction.commit();
        return;
      }

      // 检查字段是否存在
      const tableDescription = await queryInterface.describeTable('user_wallets');
      
      if (!tableDescription.phrsBalance) {
        console.log('⚠️  phrsBalance 字段不存在，跳过修复');
        await transaction.commit();
        return;
      }

      // 1. 备份当前数据（可选，但建议在生产环境中执行）
      console.log('1. 检查当前 phrsBalance 字段配置...');
      console.log(`   当前字段类型: ${tableDescription.phrsBalance.type}`);
      console.log(`   允许为空: ${tableDescription.phrsBalance.allowNull}`);
      console.log(`   默认值: ${tableDescription.phrsBalance.defaultValue}`);

      // 2. 检查是否有数据需要保护
      const [results] = await queryInterface.sequelize.query(
        'SELECT COUNT(*) as count, MAX(phrsBalance) as maxBalance FROM user_wallets WHERE phrsBalance > 0',
        { transaction }
      );
      
      const dataInfo = results[0];
      console.log(`2. 数据检查结果:`);
      console.log(`   有余额的用户数: ${dataInfo.count}`);
      console.log(`   最大余额: ${dataInfo.maxBalance || '0'}`);

      // 3. 修改字段精度
      console.log('3. 修改 phrsBalance 字段精度从 DECIMAL(65,3) 到 DECIMAL(65,18)...');
      
      await queryInterface.changeColumn('user_wallets', 'phrsBalance', {
        type: Sequelize.DECIMAL(65, 18), // 修改为18位精度
        allowNull: false,
        defaultValue: 0,
        comment: 'PHRS代币余额（18位精度）'
      }, { transaction });

      console.log('✅ phrsBalance 字段精度修改成功');

      // 4. 验证修改结果
      console.log('4. 验证修改结果...');
      const updatedTableDescription = await queryInterface.describeTable('user_wallets');
      console.log(`   新字段类型: ${updatedTableDescription.phrsBalance.type}`);

      // 5. 验证数据完整性
      const [verifyResults] = await queryInterface.sequelize.query(
        'SELECT COUNT(*) as count, MAX(phrsBalance) as maxBalance FROM user_wallets WHERE phrsBalance > 0',
        { transaction }
      );
      
      const verifyInfo = verifyResults[0];
      console.log(`5. 数据完整性验证:`);
      console.log(`   有余额的用户数: ${verifyInfo.count}`);
      console.log(`   最大余额: ${verifyInfo.maxBalance || '0'}`);
      
      if (dataInfo.count === verifyInfo.count) {
        console.log('✅ 数据完整性验证通过');
      } else {
        console.warn('⚠️  数据完整性验证失败，用户数量不匹配');
      }

      await transaction.commit();
      console.log('🎉 phrsBalance 字段精度修改完成！');
      console.log('📊 修改内容：');
      console.log('   - phrsBalance 字段：精度从 3 位小数改为 18 位小数');
      console.log('   - 保持字段约束：NOT NULL, DEFAULT 0');
      console.log('   - 数据完整性：已验证');

    } catch (error) {
      await transaction.rollback();
      console.error('❌ phrsBalance 字段精度修改失败:', error);
      throw error;
    }
  },

  async down(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();

    try {
      console.log('开始回滚 phrsBalance 字段精度修改...');

      // 检查表是否存在
      const tables = await queryInterface.showAllTables();
      
      if (!tables.includes('user_wallets')) {
        console.log('⚠️  user_wallets 表不存在，跳过回滚');
        await transaction.commit();
        return;
      }

      // 检查字段是否存在
      const tableDescription = await queryInterface.describeTable('user_wallets');
      
      if (!tableDescription.phrsBalance) {
        console.log('⚠️  phrsBalance 字段不存在，跳过回滚');
        await transaction.commit();
        return;
      }

      // 1. 检查数据是否会丢失精度
      console.log('1. 检查数据精度丢失风险...');
      const [results] = await queryInterface.sequelize.query(
        `SELECT COUNT(*) as count 
         FROM user_wallets 
         WHERE phrsBalance > 0 
         AND CAST(phrsBalance AS DECIMAL(65,3)) != phrsBalance`,
        { transaction }
      );
      
      const riskCount = results[0].count;
      if (riskCount > 0) {
        console.warn(`⚠️  警告: 有 ${riskCount} 条记录可能会丢失精度`);
        console.warn('   建议不要执行回滚操作，或先备份数据');
      }

      // 2. 回滚字段精度
      console.log('2. 回滚 phrsBalance 字段精度从 DECIMAL(65,18) 到 DECIMAL(65,3)...');
      
      await queryInterface.changeColumn('user_wallets', 'phrsBalance', {
        type: Sequelize.DECIMAL(65, 3), // 回滚到3位精度
        allowNull: false,
        defaultValue: 0,
        comment: 'PHRS代币余额'
      }, { transaction });

      console.log('✅ phrsBalance 字段精度回滚成功');

      // 3. 验证回滚结果
      const updatedTableDescription = await queryInterface.describeTable('user_wallets');
      console.log(`   回滚后字段类型: ${updatedTableDescription.phrsBalance.type}`);

      await transaction.commit();
      console.log('🔄 phrsBalance 字段精度回滚完成！');

    } catch (error) {
      await transaction.rollback();
      console.error('❌ phrsBalance 字段精度回滚失败:', error);
      throw error;
    }
  }
};
