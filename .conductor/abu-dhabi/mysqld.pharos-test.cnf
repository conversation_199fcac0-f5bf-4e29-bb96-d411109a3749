[mysqld]

# 基础设置
port = 3306
bind-address = 0.0.0.0

# 时区设置
default-time_zone = '+8:00'

# 认证插件设置
default_authentication_plugin = caching_sha2_password

# 字符集设置
character-set-server = utf8mb4
collation-server = utf8mb4_unicode_ci

# 性能优化 (适合测试环境)
innodb_buffer_pool_size = 256M
max_connections = 200

# 网络设置
max_allowed_packet = 64M

[mysql]
default-character-set = utf8mb4

[client]
default-character-set = utf8mb4
port = 3306
