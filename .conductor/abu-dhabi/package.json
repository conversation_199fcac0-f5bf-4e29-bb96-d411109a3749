{"name": "wolf_fun", "version": "1.0.0", "main": "dist/app.js", "scripts": {"dev": "cross-env NODE_ENV=development tsnd --respawn src/app.ts", "build": "cross-env NODE_ENV=production tsc", "build:dev": "cross-env NODE_ENV=development tsc", "start": "cross-env NODE_ENV=production node dist/app.js", "start:dev": "cross-env NODE_ENV=development tsnd --respawn src/app.ts", "seed:tasks": "npx sequelize-cli db:migrate && npx sequelize-cli db:seed --seed 20250120073040-add_task.js --config config/config.js && npx sequelize-cli db:seed --seed 20250610000000-add-iap-products.js --config config/config.js", "sync:db": "node sync_database.js", "sync:db:force": "node sync_database_force.js", "sync:models": "node sync_models_auto.js", "sync:models:alter": "node sync_models_auto.js --alter", "sync:models:force": "node sync_models_auto.js --force", "migrate:offline-rewards": "node run_sequelize_migration.js", "migrate:offline-rewards:rollback": "node rollback_sequelize_migration.js", "debug:phrs-monitor": "ts-node src/scripts/debugPhrsMonitor.ts", "check:phrs-service": "ts-node src/scripts/checkPhrsService.ts", "test:phrs-monitoring": "ts-node src/scripts/testPhrsMonitoring.ts", "test:monitoring-service": "ts-node src/scripts/testMonitoringService.ts", "quick:block-check": "ts-node src/scripts/quickBlockCheck.ts", "test:phrs-service": "ts-node src/scripts/testPhrsService.ts", "process:block": "ts-node src/scripts/processBlock.ts", "fix:phrs-table": "ts-node src/scripts/fixPhrsDepositsTable.ts", "check:phrs-deposits": "ts-node src/scripts/checkPhrsDeposits.ts", "diagnose:phrs-monitor": "ts-node src/scripts/diagnosePhrsMonitor.ts", "test:block-range": "ts-node src/scripts/testBlockRangeLimit.ts", "test:historical": "ts-node src/scripts/testHistoricalEvents.ts", "test:historical-skip": "ts-node src/scripts/testHistoricalSkip.ts", "start:historical": "ts-node src/scripts/startWithHistorical.ts", "test:balance-monitor": "ts-node src/scripts/testBalanceMonitor.ts", "test:duplicate-handling": "ts-node src/scripts/testDuplicateHandling.ts", "update:tx-hash-length": "ts-node src/scripts/updateTransactionHashLength.ts", "test:find-or-create": "ts-node src/scripts/testFindOrCreate.ts", "test:concurrent": "ts-node src/scripts/testConcurrentProcessing.ts", "test:robustness": "ts-node src/scripts/testRobustness.ts", "test:transaction-rollback": "ts-node src/scripts/testTransactionRollback.ts", "test:transaction-reuse": "ts-node src/scripts/testTransactionReuse.ts", "test:unregistered-user-fix": "ts-node src/scripts/testUnregisteredUserFix.ts", "check:specific-block": "ts-node src/scripts/checkSpecificBlock.ts", "check:unregistered-deposits": "ts-node src/scripts/checkUnregisteredDeposits.ts", "handle:unregistered-deposits": "ts-node src/scripts/handleUnregisteredDeposits.ts", "handle:specific-deposit": "ts-node src/scripts/handleSpecificDeposit.ts", "migrate:offline-rewards:verify": "node verify_migration.js", "migrate": "npx sequelize-cli db:migrate", "migrate:undo": "npx sequelize-cli db:migrate:undo", "migrate:undo:all": "npx sequelize-cli db:migrate:undo:all", "migrate:local": "node run_local_migrations.js up", "migrate:local:status": "node run_local_migrations.js status", "migrate:local:rollback": "node run_local_migrations.js down", "migrate:local:reset": "FORCE=true node run_local_migrations.js reset", "migrate:local:fresh": "FORCE=true node run_local_migrations.js fresh", "migrate:local:menu": "./migrate.sh", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:phrs": "./scripts/test-phrs-system.sh", "test:contracts": "cd contracts && npm test", "test:api": "jest src/tests --testPathIgnorePatterns=integration", "test:integration": "jest src/tests/integration", "db:pharos-test:start": "./scripts/start-pharos-test-db.sh", "db:pharos-test:stop": "./scripts/stop-pharos-test-db.sh", "db:pharos-test:restart": "npm run db:pharos-test:stop && npm run db:pharos-test:start", "db:pharos-test:init": "node scripts/init-pharos-test-db.js", "db:pharos-test:reset": "npm run db:pharos-test:stop && npm run db:pharos-test:start && npm run db:pharos-test:init", "db:pharos-test:test": "node scripts/test-pharos-db-connection.js", "db:pharos-test:rebuild": "./scripts/rebuild-pharos-test-db.sh", "docker:pharos-test:build": "./scripts/build-pharos-test.sh", "docker:pharos-test:deploy": "./scripts/deploy-pharos-test.sh", "docker:pharos-test:deploy:staging": "./scripts/deploy-pharos-test.sh staging", "docker:pharos-test:deploy:production": "./scripts/deploy-pharos-test.sh production", "pharos-test:deploy": "./deploy-pharos-test.sh", "pharos-test:stop": "./stop-pharos-test.sh", "pharos-test:restart": "./restart-pharos-test.sh", "private-to-address": "ts-node src/scripts/privateKeyToAddress.ts", "farm-config:init": "node scripts/init-farm-config-simple.js", "farm-config:init:force": "node scripts/init-farm-config-simple.js --force"}, "keywords": [], "author": "", "license": "ISC", "description": "", "engines": {"node": ">=22.0.0", "npm": ">=10.0.0"}, "dependencies": {"@ton/core": "^0.59.1", "@ton/crypto": "^3.3.0", "@ton/ton": "^15.1.0", "@tonconnect/ui-react": "^2.0.11", "@types/multer": "^2.0.0", "@types/node-polyglot": "^2.5.0", "ajv": "^8.17.1", "ajv-formats": "^3.0.1", "ajv-i18n": "^4.2.0", "axios": "^1.9.0", "bignumber.js": "^9.3.0", "bullmq": "^5.41.4", "cors": "^2.8.5", "cross-env": "^7.0.3", "dayjs": "^1.11.13", "dotenv": "^16.4.7", "ethers": "^6.14.0", "express": "^4.21.2", "express-rate-limit": "^7.5.0", "grammy": "^1.35.1", "ioredis": "^5.4.2", "jsonwebtoken": "^9.0.2", "multer": "^2.0.2", "mysql2": "^3.12.0", "node-cron": "^3.0.3", "node-polyglot": "^2.6.0", "nodemailer": "^6.10.0", "sequelize": "^6.37.5", "sequelize-cli": "^6.6.2", "uuid": "^11.0.5", "xlsx": "^0.18.5", "zod": "^3.24.1"}, "devDependencies": {"@types/bignumber.js": "^4.0.3", "@types/cors": "^2.8.17", "@types/express": "^5.0.0", "@types/glob": "^8.1.0", "@types/jest": "^29.5.5", "@types/jsonwebtoken": "^9.0.7", "@types/node": "^22.13.9", "@types/node-cron": "^3.0.11", "@types/nodemailer": "^6.4.17", "exceljs": "^4.4.0", "@types/supertest": "^2.0.12", "jest": "^29.7.0", "supertest": "^6.3.3", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "ts-node-dev": "^2.0.0", "typescript": "^5.7.3"}}