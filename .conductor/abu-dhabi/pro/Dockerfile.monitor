# 使用官方 Node.js 镜像
FROM node:20-alpine

ENV TIME_ZONE=Asia/Shanghai

RUN \
  mkdir -p /usr/src/app \
  && apk add --no-cache tzdata \
  && echo "${TIME_ZONE}" > /etc/timezone \
  && ln -sf /usr/share/zoneinfo/${TIME_ZONE} /etc/localtime 

# 创建工作目录
WORKDIR /app-monitor

# 复制 package.json 和 package-lock.json
COPY package*.json ./
COPY .npmrc ./

# 安装项目依赖
RUN npm install

# 复制项目文件到容器中
COPY . .

# 复制环境变量文件
COPY .env_pro .env

# 构建项目
RUN npm run build

ENV NODE_ENV=production

# 容器启动时的命令 - 专门运行监控服务
CMD ["node", "dist/scheduler/checkAccount.js"]