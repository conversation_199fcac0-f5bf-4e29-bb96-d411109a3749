#!/bin/bash

# 设置脚本在遇到错误时退出
set -e

# 构建 Docker 镜像
docker build --no-cache --pull --rm -f ./Dockerfile -t moofun ..

# 停止并删除旧的容器（如果存在）
docker stop moofun-container 2>/dev/null || true
docker rm moofun-container 2>/dev/null || true

docker image prune -f

# 运行新的容器实例
docker run -d -p 9223:3458 --name moofun-container --network moofun moofun

# 等待20秒确保容器和服务完全启动
sleep 20

docker exec moofun-container npm run seed:tasks