/**
 * 快速测试脚本 - 验证测试重置 API 是否正常工作
 * 使用方法：node quick_test.js
 */

const http = require('http');

// 配置
const BASE_URL = 'localhost';
const PORT = 3456;

/**
 * 发送 HTTP 请求的辅助函数
 */
function makeRequest(options, data = null) {
  return new Promise((resolve, reject) => {
    const req = http.request(options, (res) => {
      let body = '';
      res.on('data', (chunk) => {
        body += chunk;
      });
      res.on('end', () => {
        try {
          const jsonBody = JSON.parse(body);
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            body: jsonBody
          });
        } catch (e) {
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            body: body
          });
        }
      });
    });

    req.on('error', (err) => {
      reject(err);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }
    req.end();
  });
}

/**
 * 测试健康检查接口
 */
async function testHealthCheck() {
  console.log('\n=== 测试健康检查接口 ===');
  try {
    const options = {
      hostname: BASE_URL,
      port: PORT,
      path: '/api/test/health',
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    };

    const response = await makeRequest(options);
    
    if (response.statusCode === 200 && response.body.ok) {
      console.log('✅ 健康检查成功');
      console.log(`   环境: ${response.body.data.environment}`);
      console.log(`   开发环境: ${response.body.data.isDevelopment}`);
      console.log(`   运行时间: ${response.body.data.uptime.toFixed(2)}秒`);
      return true;
    } else {
      console.log('❌ 健康检查失败:', response.body);
      return false;
    }
  } catch (error) {
    console.error('❌ 健康检查请求失败:', error.message);
    return false;
  }
}

/**
 * 测试身份验证要求（不带认证）
 */
async function testAuthenticationRequired() {
  console.log('\n=== 测试身份验证要求 ===');
  try {
    const options = {
      hostname: BASE_URL,
      port: PORT,
      path: '/api/test/reset-safety-info',
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    };

    const response = await makeRequest(options);

    if (response.statusCode === 401) {
      console.log('✅ 正确要求身份验证 (401 Unauthorized)');
      return true;
    } else {
      console.log('❌ 意外的响应:', response.statusCode, response.body);
      return false;
    }
  } catch (error) {
    console.error('❌ 身份验证测试失败:', error.message);
    return false;
  }
}

/**
 * 测试重置操作（不带认证）
 */
async function testResetWithoutAuth() {
  console.log('\n=== 测试重置操作（不带认证）===');
  try {
    const options = {
      hostname: BASE_URL,
      port: PORT,
      path: '/api/test/reset-game-state',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      }
    };

    const response = await makeRequest(options, {});

    if (response.statusCode === 401) {
      console.log('✅ 正确要求身份验证 (401 Unauthorized)');
      return true;
    } else {
      console.log('❌ 意外的响应:', response.statusCode, response.body);
      return false;
    }
  } catch (error) {
    console.error('❌ 重置测试失败:', error.message);
    return false;
  }
}

/**
 * 测试 API 路由是否正确集成
 */
async function testApiIntegration() {
  console.log('\n=== 测试 API 路由集成 ===');
  
  // 测试不存在的路由
  try {
    const options = {
      hostname: BASE_URL,
      port: PORT,
      path: '/api/test/nonexistent',
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    };

    const response = await makeRequest(options);
    
    if (response.statusCode === 404) {
      console.log('✅ 不存在的路由正确返回 404');
      return true;
    } else {
      console.log('❌ 意外的响应:', response.statusCode);
      return false;
    }
  } catch (error) {
    console.error('❌ API 集成测试失败:', error.message);
    return false;
  }
}

/**
 * 主测试函数
 */
async function runQuickTests() {
  console.log('🚀 开始快速测试测试重置 API');
  console.log('📍 测试地址: http://localhost:3456');
  console.log('⚠️  注意：这些测试不需要 JWT token，只测试基本功能');

  const results = {
    healthCheck: false,
    authenticationRequired: false,
    resetWithoutAuth: false,
    apiIntegration: false
  };

  // 运行测试
  results.healthCheck = await testHealthCheck();
  results.authenticationRequired = await testAuthenticationRequired();
  results.resetWithoutAuth = await testResetWithoutAuth();
  results.apiIntegration = await testApiIntegration();
  
  // 输出测试结果
  console.log('\n📊 快速测试结果总结:');
  console.log('==================');
  Object.entries(results).forEach(([test, passed]) => {
    const status = passed ? '✅ 通过' : '❌ 失败';
    console.log(`${test}: ${status}`);
  });
  
  const passedCount = Object.values(results).filter(Boolean).length;
  const totalCount = Object.keys(results).length;
  
  console.log(`\n🎯 总体结果: ${passedCount}/${totalCount} 测试通过`);
  
  if (passedCount === totalCount) {
    console.log('🎉 所有基本功能测试都通过了！');
    console.log('\n📝 下一步:');
    console.log('1. 获取有效的 JWT token');
    console.log('2. 运行完整测试: node test_reset_api.js');
    console.log('3. 查看文档: docs/test-reset-api.md');
  } else {
    console.log('⚠️ 部分测试失败，请检查服务器状态');
  }
  
  console.log('\n🔧 服务器信息:');
  console.log(`- 地址: http://localhost:${PORT}`);
  console.log('- 环境: development');
  console.log('- Node.js: v22.12.0');
  console.log('- 状态: 运行中');
}

// 运行测试
if (require.main === module) {
  runQuickTests().catch(error => {
    console.error('💥 测试运行失败:', error);
    process.exit(1);
  });
}

module.exports = {
  testHealthCheck,
  testAuthenticationRequired,
  testResetWithoutAuth,
  testApiIntegration
};
