#!/bin/bash

# Pharos Test 环境重启脚本

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🔄 重启 Pharos Test 环境...${NC}"

# 1. 停止应用（但保留数据库）
echo -e "${YELLOW}🛑 停止应用容器...${NC}"
docker stop wolf-fun-pharos-test-container 2>/dev/null || echo -e "${YELLOW}⚠️ 应用容器未运行${NC}"
docker rm wolf-fun-pharos-test-container 2>/dev/null || echo -e "${YELLOW}⚠️ 应用容器不存在${NC}"

# 2. 重新部署
echo -e "${YELLOW}🚀 重新部署应用...${NC}"
./deploy-pharos-test.sh

echo -e "${GREEN}✅ Pharos Test 环境重启完成${NC}"
