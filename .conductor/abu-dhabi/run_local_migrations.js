#!/usr/bin/env node

/**
 * 本地迁移执行脚本
 * 自动读取本地环境配置并执行所有待迁移的文件
 */

const { Sequelize, DataTypes } = require('sequelize');
const fs = require('fs');
const path = require('path');
const dotenv = require('dotenv');

// 加载环境变量
dotenv.config();

// 颜色输出
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m',
};

// 日志函数
const log = {
  info: (msg) => console.log(`${colors.cyan}ℹ${colors.reset}  ${msg}`),
  success: (msg) => console.log(`${colors.green}✓${colors.reset}  ${msg}`),
  warning: (msg) => console.log(`${colors.yellow}⚠${colors.reset}  ${msg}`),
  error: (msg) => console.log(`${colors.red}✗${colors.reset}  ${msg}`),
  title: (msg) => console.log(`\n${colors.magenta}═══ ${msg} ═══${colors.reset}\n`),
  step: (msg) => console.log(`${colors.blue}➤${colors.reset}  ${msg}`),
};

// 创建 Sequelize 实例
function createSequelizeInstance() {
  const config = {
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '3669'),
    username: process.env.DB_USER || 'root',
    password: process.env.DB_PASS || 'root',
    database: process.env.DB_NAME || 'wolf_fun_db',
    dialect: 'mysql',
    logging: process.env.DEBUG === 'true' ? console.log : false,
    timezone: '+08:00',
    pool: {
      max: 5,
      min: 0,
      acquire: 30000,
      idle: 10000
    },
    dialectOptions: {
      dateStrings: true,
      typeCast: true,
      connectTimeout: 60000
    }
  };

  log.info('数据库配置:');
  log.info(`  主机: ${config.host}:${config.port}`);
  log.info(`  数据库: ${config.database}`);
  log.info(`  用户: ${config.username}`);
  log.info(`  环境: ${process.env.NODE_ENV || 'development'}`);

  return new Sequelize(config);
}

// 创建迁移记录表
async function createMigrationTable(sequelize) {
  const queryInterface = sequelize.getQueryInterface();
  
  try {
    await queryInterface.createTable('SequelizeMeta', {
      name: {
        type: DataTypes.STRING,
        allowNull: false,
        primaryKey: true,
      }
    });
    log.success('迁移记录表创建成功');
  } catch (error) {
    if (error.name === 'SequelizeDatabaseError' && error.original.code === 'ER_TABLE_EXISTS_ERROR') {
      log.info('迁移记录表已存在');
    } else {
      throw error;
    }
  }
}

// 获取已执行的迁移
async function getExecutedMigrations(sequelize) {
  try {
    const [results] = await sequelize.query('SELECT name FROM SequelizeMeta ORDER BY name');
    return results.map(r => r.name);
  } catch (error) {
    log.warning('无法获取已执行的迁移列表，假设没有执行过任何迁移');
    return [];
  }
}

// 获取所有迁移文件
function getAllMigrationFiles() {
  const migrationsDir = path.join(__dirname, 'migrations');
  
  if (!fs.existsSync(migrationsDir)) {
    log.error(`迁移目录不存在: ${migrationsDir}`);
    return [];
  }

  const files = fs.readdirSync(migrationsDir)
    .filter(file => file.endsWith('.js'))
    .sort();
  
  log.info(`找到 ${files.length} 个迁移文件`);
  return files;
}

// 执行单个迁移
async function executeMigration(sequelize, migrationFile) {
  const migrationPath = path.join(__dirname, 'migrations', migrationFile);
  
  try {
    // 清除缓存，确保重新加载迁移文件
    delete require.cache[require.resolve(migrationPath)];
    
    const migration = require(migrationPath);
    
    if (typeof migration.up !== 'function') {
      throw new Error(`迁移文件 ${migrationFile} 缺少 up 方法`);
    }

    log.step(`执行迁移: ${migrationFile}`);
    
    const queryInterface = sequelize.getQueryInterface();
    
    // 开始事务
    const transaction = await sequelize.transaction();
    
    try {
      // 执行迁移
      await migration.up(queryInterface, Sequelize);
      
      // 记录迁移
      await sequelize.query(
        'INSERT INTO SequelizeMeta (name) VALUES (?)',
        {
          replacements: [migrationFile],
          transaction
        }
      );
      
      // 提交事务
      await transaction.commit();
      
      log.success(`✅ 迁移成功: ${migrationFile}`);
      return true;
    } catch (error) {
      // 回滚事务
      await transaction.rollback();
      throw error;
    }
  } catch (error) {
    log.error(`❌ 迁移失败: ${migrationFile}`);
    log.error(`   错误信息: ${error.message}`);
    
    // 检查是否是重复字段/索引错误
    if (error.message && (
      error.message.includes('Duplicate column name') ||
      error.message.includes('Duplicate key name') ||
      error.message.includes('already exists')
    )) {
      log.warning('   该迁移可能已经被部分执行过，跳过...');
      
      // 记录为已执行
      await sequelize.query(
        'INSERT IGNORE INTO SequelizeMeta (name) VALUES (?)',
        {
          replacements: [migrationFile]
        }
      );
      return true;
    }
    
    return false;
  }
}

// 回滚迁移
async function rollbackMigration(sequelize, migrationFile) {
  const migrationPath = path.join(__dirname, 'migrations', migrationFile);
  
  try {
    const migration = require(migrationPath);
    
    if (typeof migration.down !== 'function') {
      log.warning(`迁移文件 ${migrationFile} 没有 down 方法，无法回滚`);
      return false;
    }

    log.step(`回滚迁移: ${migrationFile}`);
    
    const queryInterface = sequelize.getQueryInterface();
    
    // 开始事务
    const transaction = await sequelize.transaction();
    
    try {
      // 执行回滚
      await migration.down(queryInterface, Sequelize);
      
      // 删除迁移记录
      await sequelize.query(
        'DELETE FROM SequelizeMeta WHERE name = ?',
        {
          replacements: [migrationFile],
          transaction
        }
      );
      
      // 提交事务
      await transaction.commit();
      
      log.success(`✅ 回滚成功: ${migrationFile}`);
      return true;
    } catch (error) {
      // 回滚事务
      await transaction.rollback();
      throw error;
    }
  } catch (error) {
    log.error(`❌ 回滚失败: ${migrationFile}`);
    log.error(`   错误信息: ${error.message}`);
    return false;
  }
}

// 主函数
async function main() {
  const args = process.argv.slice(2);
  const command = args[0] || 'up';
  const target = args[1];
  
  log.title('本地数据库迁移工具');
  
  // 创建数据库连接
  const sequelize = createSequelizeInstance();
  
  try {
    // 测试连接
    log.step('测试数据库连接...');
    await sequelize.authenticate();
    log.success('数据库连接成功！');
    
    // 创建迁移记录表
    await createMigrationTable(sequelize);
    
    // 获取已执行的迁移
    const executedMigrations = await getExecutedMigrations(sequelize);
    log.info(`已执行的迁移数量: ${executedMigrations.length}`);
    
    // 获取所有迁移文件
    const allMigrations = getAllMigrationFiles();
    
    switch (command) {
      case 'up':
      case 'migrate': {
        // 执行所有待迁移的文件
        const pendingMigrations = allMigrations.filter(m => !executedMigrations.includes(m));
        
        if (pendingMigrations.length === 0) {
          log.info('没有待执行的迁移');
          break;
        }
        
        log.title(`开始执行 ${pendingMigrations.length} 个迁移`);
        
        let successCount = 0;
        let failCount = 0;
        
        for (const migration of pendingMigrations) {
          const success = await executeMigration(sequelize, migration);
          if (success) {
            successCount++;
          } else {
            failCount++;
            if (!process.env.CONTINUE_ON_ERROR) {
              log.error('迁移失败，停止执行（设置 CONTINUE_ON_ERROR=true 继续执行）');
              break;
            }
          }
        }
        
        log.title('迁移执行完成');
        log.info(`成功: ${successCount} 个`);
        if (failCount > 0) {
          log.warning(`失败: ${failCount} 个`);
        }
        break;
      }
      
      case 'down':
      case 'rollback': {
        // 回滚最后一个迁移或指定数量
        const count = parseInt(target) || 1;
        const toRollback = executedMigrations.slice(-count).reverse();
        
        if (toRollback.length === 0) {
          log.info('没有可回滚的迁移');
          break;
        }
        
        log.title(`准备回滚 ${toRollback.length} 个迁移`);
        
        for (const migration of toRollback) {
          await rollbackMigration(sequelize, migration);
        }
        break;
      }
      
      case 'status': {
        // 显示迁移状态
        log.title('迁移状态');
        
        log.info('已执行的迁移:');
        if (executedMigrations.length === 0) {
          log.info('  （无）');
        } else {
          executedMigrations.forEach(m => log.info(`  ✓ ${m}`));
        }
        
        const pendingMigrations = allMigrations.filter(m => !executedMigrations.includes(m));
        log.info('\n待执行的迁移:');
        if (pendingMigrations.length === 0) {
          log.info('  （无）');
        } else {
          pendingMigrations.forEach(m => log.info(`  ○ ${m}`));
        }
        break;
      }
      
      case 'reset': {
        // 重置所有迁移
        log.title('重置所有迁移');
        log.warning('⚠️  警告：这将回滚所有迁移！');
        
        // 需要确认
        if (process.env.FORCE !== 'true') {
          log.error('需要设置 FORCE=true 环境变量来确认重置操作');
          break;
        }
        
        const toRollback = [...executedMigrations].reverse();
        for (const migration of toRollback) {
          await rollbackMigration(sequelize, migration);
        }
        
        log.success('所有迁移已回滚');
        break;
      }
      
      case 'fresh': {
        // 重置并重新执行所有迁移
        log.title('重新执行所有迁移');
        log.warning('⚠️  警告：这将回滚并重新执行所有迁移！');
        
        // 需要确认
        if (process.env.FORCE !== 'true') {
          log.error('需要设置 FORCE=true 环境变量来确认操作');
          break;
        }
        
        // 先回滚
        const toRollback = [...executedMigrations].reverse();
        for (const migration of toRollback) {
          await rollbackMigration(sequelize, migration);
        }
        
        // 再执行
        for (const migration of allMigrations) {
          await executeMigration(sequelize, migration);
        }
        
        log.success('所有迁移已重新执行');
        break;
      }
      
      default:
        log.error(`未知命令: ${command}`);
        log.info('\n可用命令:');
        log.info('  up/migrate      - 执行所有待迁移的文件');
        log.info('  down/rollback [n] - 回滚最后n个迁移（默认1个）');
        log.info('  status          - 显示迁移状态');
        log.info('  reset           - 回滚所有迁移（需要 FORCE=true）');
        log.info('  fresh           - 重新执行所有迁移（需要 FORCE=true）');
        log.info('\n环境变量:');
        log.info('  DEBUG=true           - 显示SQL日志');
        log.info('  CONTINUE_ON_ERROR=true - 迁移失败后继续执行');
        log.info('  FORCE=true           - 确认危险操作');
    }
    
  } catch (error) {
    log.error('发生错误:');
    log.error(error.message);
    if (process.env.DEBUG === 'true') {
      console.error(error);
    }
    process.exit(1);
  } finally {
    await sequelize.close();
    log.info('\n数据库连接已关闭');
  }
}

// 处理未捕获的异常
process.on('uncaughtException', (error) => {
  log.error('未捕获的异常:');
  log.error(error.message);
  console.error(error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  log.error('未处理的Promise拒绝:');
  log.error(reason);
  process.exit(1);
});

// 运行主函数
if (require.main === module) {
  main().catch(error => {
    log.error('主函数执行失败:');
    console.error(error);
    process.exit(1);
  });
}

module.exports = { main };