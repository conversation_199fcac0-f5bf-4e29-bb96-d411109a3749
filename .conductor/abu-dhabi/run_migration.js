// 执行数据库迁移脚本
const mysql = require('mysql2/promise');
require('dotenv').config();

async function runMigration() {
  console.log('🔧 开始执行数据库迁移...');
  
  // 创建数据库连接
  const connection = await mysql.createConnection({
    host: process.env.DB_HOST,
    port: process.env.DB_PORT,
    user: process.env.DB_USER,
    password: process.env.DB_PASS,
    database: process.env.DB_NAME
  });

  try {
    console.log('✅ 数据库连接成功');
    
    // 检查字段是否已存在
    console.log('🔍 检查字段是否已存在...');
    const [columns] = await connection.execute(`
      SELECT COLUMN_NAME 
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'user_wallets' 
      AND COLUMN_NAME IN ('accumulatedOfflineGems', 'lastOfflineRewardCalculation')
    `, [process.env.DB_NAME]);
    
    const existingColumns = columns.map(row => row.COLUMN_NAME);
    console.log('已存在的字段:', existingColumns);
    
    // 添加 accumulatedOfflineGems 字段
    if (!existingColumns.includes('accumulatedOfflineGems')) {
      console.log('📝 添加 accumulatedOfflineGems 字段...');
      await connection.execute(`
        ALTER TABLE user_wallets 
        ADD COLUMN accumulatedOfflineGems DECIMAL(65,3) DEFAULT 0 COMMENT '累积的离线宝石奖励'
      `);
      console.log('✅ accumulatedOfflineGems 字段添加成功');
    } else {
      console.log('⚠️  accumulatedOfflineGems 字段已存在，跳过');
    }
    
    // 添加 lastOfflineRewardCalculation 字段
    if (!existingColumns.includes('lastOfflineRewardCalculation')) {
      console.log('📝 添加 lastOfflineRewardCalculation 字段...');
      await connection.execute(`
        ALTER TABLE user_wallets 
        ADD COLUMN lastOfflineRewardCalculation DATETIME NULL COMMENT '上次离线奖励计算时间'
      `);
      console.log('✅ lastOfflineRewardCalculation 字段添加成功');
    } else {
      console.log('⚠️  lastOfflineRewardCalculation 字段已存在，跳过');
    }
    
    // 检查索引是否已存在
    console.log('🔍 检查索引是否已存在...');
    const [indexes] = await connection.execute(`
      SELECT INDEX_NAME 
      FROM INFORMATION_SCHEMA.STATISTICS 
      WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'user_wallets' 
      AND INDEX_NAME IN ('idx_user_wallets_accumulated_offline_gems', 'idx_user_wallets_last_offline_calculation')
    `, [process.env.DB_NAME]);
    
    const existingIndexes = indexes.map(row => row.INDEX_NAME);
    console.log('已存在的索引:', existingIndexes);
    
    // 添加索引
    if (!existingIndexes.includes('idx_user_wallets_accumulated_offline_gems')) {
      console.log('📝 添加 accumulatedOfflineGems 索引...');
      await connection.execute(`
        CREATE INDEX idx_user_wallets_accumulated_offline_gems ON user_wallets(accumulatedOfflineGems)
      `);
      console.log('✅ accumulatedOfflineGems 索引添加成功');
    } else {
      console.log('⚠️  accumulatedOfflineGems 索引已存在，跳过');
    }
    
    if (!existingIndexes.includes('idx_user_wallets_last_offline_calculation')) {
      console.log('📝 添加 lastOfflineRewardCalculation 索引...');
      await connection.execute(`
        CREATE INDEX idx_user_wallets_last_offline_calculation ON user_wallets(lastOfflineRewardCalculation)
      `);
      console.log('✅ lastOfflineRewardCalculation 索引添加成功');
    } else {
      console.log('⚠️  lastOfflineRewardCalculation 索引已存在，跳过');
    }
    
    console.log('🎉 数据库迁移完成！');
    
  } catch (error) {
    console.error('❌ 迁移失败:', error);
  } finally {
    await connection.end();
    console.log('🔌 数据库连接已关闭');
  }
}

runMigration();
