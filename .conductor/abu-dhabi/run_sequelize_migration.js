// 运行 Sequelize 迁移脚本
const { Sequelize, QueryInterface } = require('sequelize');
require('dotenv').config();

async function runMigration() {
  console.log('🔧 开始执行 Sequelize 迁移...');
  
  // 创建 Sequelize 实例
  const sequelize = new Sequelize({
    host: process.env.DB_HOST,
    port: process.env.DB_PORT,
    username: process.env.DB_USER,
    password: process.env.DB_PASS,
    database: process.env.DB_NAME,
    dialect: 'mysql',
    logging: console.log
  });

  try {
    // 测试连接
    await sequelize.authenticate();
    console.log('✅ 数据库连接成功');

    // 获取 QueryInterface
    const queryInterface = sequelize.getQueryInterface();

    // 导入迁移文件
    const migration = require('./migrations/20250714-add-accumulated-offline-rewards.js');

    console.log('📝 执行迁移：添加累积离线奖励字段...');
    
    // 执行 up 迁移
    await migration.up(queryInterface, Sequelize);
    
    console.log('🎉 迁移执行成功！');
    console.log('✅ 已添加字段：');
    console.log('   - accumulatedOfflineGems (DECIMAL(65,3))');
    console.log('   - lastOfflineRewardCalculation (DATETIME)');
    console.log('✅ 已添加索引：');
    console.log('   - idx_user_wallets_accumulated_offline_gems');
    console.log('   - idx_user_wallets_last_offline_calculation');

  } catch (error) {
    console.error('❌ 迁移失败:', error);
    
    if (error.message.includes('Duplicate column name')) {
      console.log('⚠️  字段可能已存在，这是正常的');
    } else if (error.message.includes('Duplicate key name')) {
      console.log('⚠️  索引可能已存在，这是正常的');
    }
  } finally {
    await sequelize.close();
    console.log('🔌 数据库连接已关闭');
  }
}

// 如果直接运行此文件，则执行迁移
if (require.main === module) {
  runMigration();
}

module.exports = { runMigration };
