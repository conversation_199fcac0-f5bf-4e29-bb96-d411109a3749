import { sequelize } from '../src/config/db';
import { IapProduct } from '../src/models';

async function addSpecialOffer() {
  try {
    console.log('🚀 添加特殊套餐...\n');

    // 检查是否已存在
    const existing = await IapProduct.findOne({
      where: { productId: 'special_offer_bundle' }
    });

    if (existing) {
      console.log('特殊套餐已存在，跳过创建');
      return;
    }

    // 创建特殊套餐
    const specialOffer = await IapProduct.create({
      productId: 'special_offer_bundle',
      name: 'Special Offer',
      description: '特殊套餐：Time Warp 24hr x2（直接跳转48小时获得收益）+ Speed Boost x4 24hr x2，限购一账号一次',
      type: 'special_offer',
      priceUsd: 19.99,
      priceKaia: 120.6700, // 假设价格
      pricePhrs: 19990, // 19.99 * 1000
      quantity: 1,
      dailyLimit: 1,
      accountLimit: 1,
      config: {
        bundle: [
          { type: 'time_warp', multiplier: 1, duration: 24, quantity: 2, autoUse: true },
          { type: 'speed_boost', multiplier: 4, duration: 24, quantity: 2, autoUse: false }
        ]
      },
      isActive: true
    });

    console.log('✅ 特殊套餐创建成功:');
    console.log(`   ID: ${specialOffer.id}`);
    console.log(`   名称: ${specialOffer.name}`);
    console.log(`   价格: ${specialOffer.pricePhrs} PHRS`);
    console.log(`   配置:`, JSON.stringify(specialOffer.config, null, 2));

  } catch (error) {
    console.error('❌ 创建失败:', error);
  } finally {
    await sequelize.close();
  }
}

addSpecialOffer();
