import { sequelize } from '../src/config/db';
import { IapProduct } from '../src/models';

async function checkSpecialOffer() {
  try {
    console.log('🔍 检查特殊套餐配置...\n');

    const specialOffers = await IapProduct.findAll({
      where: { type: 'special_offer' },
      order: [['id', 'ASC']]
    });

    console.log(`找到 ${specialOffers.length} 个特殊套餐:`);
    
    specialOffers.forEach(offer => {
      console.log(`\n📦 ${offer.name} (ID: ${offer.id})`);
      console.log(`   Product ID: ${offer.productId}`);
      console.log(`   价格: $${offer.priceUsd} USD / ${offer.priceKaia} KAIA / ${offer.pricePhrs} PHRS`);
      console.log(`   每日限购: ${offer.dailyLimit}, 账号限购: ${offer.accountLimit || '无限制'}`);
      console.log(`   描述: ${offer.description}`);
      
      if (offer.config) {
        console.log(`   配置:`);
        console.log(JSON.stringify(offer.config, null, 4));
      }
    });

    console.log('\n✅ 检查完成！');

  } catch (error) {
    console.error('❌ 检查失败:', error);
  } finally {
    await sequelize.close();
  }
}

checkSpecialOffer();
