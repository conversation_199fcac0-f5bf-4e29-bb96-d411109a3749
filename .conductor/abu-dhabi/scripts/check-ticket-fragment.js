'use strict';

// 此脚本用于检查user_wallets表中是否存在ticket_fragment字段
const mysql = require('mysql2/promise');
const dotenv = require('dotenv');
dotenv.config();

async function checkTicketFragmentField() {
  let connection;
  
  try {
    console.log('开始检查user_wallets表中的ticket_fragment字段...');
    
    // 创建数据库连接
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      port: process.env.DB_PORT || 3306,
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASS || '',
      database: process.env.DB_NAME || 'wolf'
    });
    
    console.log('成功连接到数据库');
    
    // 使用原始查询检查字段是否存在
    const [results] = await connection.query(`
      SELECT COLUMN_NAME 
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = DATABASE() 
      AND TABLE_NAME = 'user_wallets' 
      AND COLUMN_NAME = 'ticket_fragment'
    `);
    
    if (results.length > 0) {
      console.log('✅ ticket_fragment字段已存在于user_wallets表中！');
      
      // 检查字段类型
      const [typeResults] = await connection.query(`
        SELECT DATA_TYPE, COLUMN_DEFAULT 
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_SCHEMA = DATABASE() 
        AND TABLE_NAME = 'user_wallets' 
        AND COLUMN_NAME = 'ticket_fragment'
      `);
      
      console.log(`字段类型: ${typeResults[0].DATA_TYPE}`);
      console.log(`字段默认值: ${typeResults[0].COLUMN_DEFAULT || '无'}`);
      
      // 查询一个示例钱包的ticket_fragment值
      const [walletResults] = await connection.query(`
        SELECT id, ticket_fragment FROM user_wallets LIMIT 5
      `);
      
      console.log('示例钱包数据:');
      console.table(walletResults);
      
    } else {
      console.log('❌ ticket_fragment字段不存在于user_wallets表中！');
      console.log('请确保迁移已正确运行：npx sequelize-cli db:migrate');
    }
    
  } catch (error) {
    console.error('检查过程中出错:', error);
    process.exit(1);
  } finally {
    // 关闭数据库连接
    if (connection) {
      await connection.end();
      console.log('数据库连接已关闭');
    }
  }
}

// 执行检查
checkTicketFragmentField(); 