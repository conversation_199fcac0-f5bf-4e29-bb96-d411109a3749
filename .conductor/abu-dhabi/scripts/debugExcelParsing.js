#!/usr/bin/env node

/**
 * 调试Excel解析
 */

const XLSX = require('xlsx');
const path = require('path');

async function debugExcelParsing() {
  try {
    const excelPath = path.join(__dirname, '../doc/tasks.xlsx');
    console.log('读取Excel文件:', excelPath);
    
    // 读取Excel文件
    const workbook = XLSX.readFile(excelPath);
    
    // 获取第一个工作表
    const sheetName = workbook.SheetNames[0];
    console.log('工作表名称:', sheetName);
    
    const worksheet = workbook.Sheets[sheetName];
    
    // 将工作表转换为JSON数组
    const jsonData = XLSX.utils.sheet_to_json(worksheet, { 
      header: 1,  // 使用数组格式而不是对象格式
      defval: ''  // 空单元格的默认值
    });
    
    console.log('总行数:', jsonData.length);
    console.log('前10行数据:');
    
    for (let i = 0; i < Math.min(10, jsonData.length); i++) {
      console.log(`第${i + 1}行:`, jsonData[i]);
    }
    
    // 找到真正的表头行（包含'id'的行）
    let headerRowIndex = -1;
    for (let i = 0; i < jsonData.length; i++) {
      const row = jsonData[i];
      if (row && row[0] === 'id') {
        headerRowIndex = i;
        break;
      }
    }

    console.log('\n表头行索引:', headerRowIndex);

    if (headerRowIndex !== -1) {
      // 从表头行的下一行开始解析数据
      const dataRows = jsonData.slice(headerRowIndex + 1);
      console.log('\n数据行 (从表头行后开始):');

      for (let i = 0; i < Math.min(5, dataRows.length); i++) {
        const row = dataRows[i];
        console.log(`数据行${i + 1}:`, row);
        console.log(`  第一个单元格值: "${row[0]}", 类型: ${typeof row[0]}`);
        console.log(`  是否为空: ${!row || row.length === 0 || !row[0]}`);
        console.log(`  是否为表头: ${row[0] === 'id'}`);
      }
    }
    
  } catch (error) {
    console.error('调试失败:', error);
  }
}

debugExcelParsing();
