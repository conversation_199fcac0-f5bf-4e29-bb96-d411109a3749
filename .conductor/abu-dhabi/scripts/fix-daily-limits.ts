import { sequelize } from '../src/config/db';
import { IapProduct } from '../src/models';

async function fixDailyLimits() {
  try {
    console.log('🔧 修复商品每日限制...\n');

    // 1. 更新时间跳跃商品的每日限制为1
    console.log('1. 更新时间跳跃商品每日限制...');
    const timeWarpResult = await IapProduct.update(
      { dailyLimit: 1 },
      { where: { type: 'time_warp' } }
    );
    console.log(`✅ 更新了 ${timeWarpResult[0]} 个时间跳跃商品`);

    // 2. 更新速度加成商品的每日限制为1
    console.log('2. 更新速度加成商品每日限制...');
    const speedBoostResult = await IapProduct.update(
      { dailyLimit: 1 },
      { where: { type: 'speed_boost' } }
    );
    console.log(`✅ 更新了 ${speedBoostResult[0]} 个速度加成商品`);

    // 3. 更新VIP会员的每日限制为1
    console.log('3. 更新VIP会员每日限制...');
    const vipResult = await IapProduct.update(
      { dailyLimit: 1 },
      { where: { type: 'vip_membership' } }
    );
    console.log(`✅ 更新了 ${vipResult[0]} 个VIP会员商品`);

    // 4. 更新特殊套餐的限制
    console.log('4. 更新特殊套餐限制...');
    const specialOfferResult = await IapProduct.update(
      { dailyLimit: 1, accountLimit: 1 },
      { where: { type: 'special_offer' } }
    );
    console.log(`✅ 更新了 ${specialOfferResult[0]} 个特殊套餐商品`);

    // 5. 验证更新结果
    console.log('\n5. 验证更新结果...');
    const allProducts = await IapProduct.findAll({
      attributes: ['id', 'name', 'type', 'dailyLimit', 'accountLimit'],
      order: [['type', 'ASC'], ['id', 'ASC']]
    });

    console.log('所有商品的限制设置:');
    allProducts.forEach(product => {
      console.log(`   ${product.name} (${product.type}): 每日限制=${product.dailyLimit}, 账号限制=${product.accountLimit || '无'}`);
    });

    console.log('\n✅ 商品限制修复完成！');

  } catch (error) {
    console.error('❌ 修复失败:', error);
  } finally {
    await sequelize.close();
  }
}

fixDailyLimits();
