#!/usr/bin/env node

/**
 * 农场配置系统简化初始化脚本
 * 直接使用 SQL 操作，避免 TypeScript 模块导入问题
 */

const mysql = require('mysql2/promise');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

// 您提供的真实配置数据
const REAL_CONFIG_DATA = [
  { grade: 0, production: 0, cow: 0, speed: 0, milk: 0, cost: 13096, offline: 0 },
  { grade: 1, production: 182, cow: 1, speed: 100, milk: 60.6286626604159, cost: 20043, offline: 90.9429939906239 },
  { grade: 2, production: 232, cow: 1, speed: 100, milk: 77.3272817972705, cost: 28583, offline: 115.990922695906 },
  { grade: 3, production: 276, cow: 2, speed: 110, milk: 95.0646914480305, cost: 39214, offline: 137.843802599644 },
  { grade: 4, production: 315, cow: 2, speed: 110, milk: 108.683909147742, cost: 52496, offline: 157.591668264226 },
  { grade: 5, production: 352, cow: 3, speed: 120, milk: 125.578116496437, cost: 69100, offline: 175.809363095011 },
  { grade: 6, production: 386, cow: 3, speed: 120, milk: 137.74696498783, cost: 89837, offline: 192.845750982962 },
  { grade: 7, production: 418, cow: 4, speed: 130, milk: 154.764544585978, cost: 115699, offline: 208.93213519107 },
  { grade: 8, production: 448, cow: 4, speed: 130, milk: 166.097458615402, cost: 147898, offline: 224.231569130793 },
  { grade: 9, production: 478, cow: 5, speed: 140, milk: 183.741771024691, cost: 187923, offline: 238.864302332098 },
  { grade: 10, production: 506, cow: 5, speed: 140, milk: 194.555498450395, cost: 237594, offline: 252.922147985514 },
  { grade: 11, production: 533, cow: 6, speed: 150, milk: 213.1817313526, cost: 299139, offline: 266.47716419075 },
  { grade: 12, production: 559, cow: 6, speed: 150, milk: 223.669748161731, cost: 375289, offline: 279.587185202164 },
  { grade: 13, production: 585, cow: 7, speed: 160, milk: 243.582916288346, cost: 469380, offline: 292.299499546015 },
  { grade: 14, production: 609, cow: 7, speed: 160, milk: 253.87781959937, cost: 585495, offline: 304.653383519244 },
  { grade: 15, production: 633, cow: 8, speed: 170, milk: 275.375563987387, cost: 728621, offline: 316.681898585495 },
  { grade: 16, production: 657, cow: 8, speed: 170, milk: 285.576694795747, cost: 904851, offline: 328.413199015109 },
  { grade: 17, production: 680, cow: 9, speed: 180, milk: 308.974094605604, cost: 1121623, offline: 339.871504066165 },
  { grade: 18, production: 702, cow: 9, speed: 180, milk: 319.161667767775, cost: 1388015, offline: 351.077834544553 },
  { grade: 19, production: 724, cow: 10, speed: 190, milk: 344.810076374009, cost: 1715098, offline: 362.05058019271 },
  { grade: 20, production: 746, cow: 10, speed: 190, milk: 355.053279277757, cost: 2116373, offline: 372.805943241645 },
  { grade: 21, production: 767, cow: 11, speed: 200, milk: 383.358289739038, cost: 3568859, offline: 383.358289739038 },
  { grade: 22, production: 1077, cow: 11, speed: 200, milk: 538.717068272049, cost: 4412137, offline: 538.717068272049 },
  { grade: 23, production: 1110, cow: 12, speed: 200, milk: 555.007842049574, cost: 5448042, offline: 555.007842049574 },
  { grade: 24, production: 1142, cow: 12, speed: 200, milk: 571.096181614764, cost: 6719624, offline: 571.096181614764 },
  { grade: 25, production: 1174, cow: 13, speed: 200, milk: 586.992556305504, cost: 8279413, offline: 586.992556305504 },
  { grade: 26, production: 1205, cow: 13, speed: 200, milk: 602.706513978308, cost: 10191468, offline: 602.706513978308 },
  { grade: 27, production: 1236, cow: 14, speed: 200, milk: 618.246793110461, cost: 12533893, offline: 618.246793110461 },
  { grade: 28, production: 1267, cow: 14, speed: 200, milk: 633.621417760574, cost: 15401872, offline: 633.621417760574 },
  { grade: 29, production: 1298, cow: 15, speed: 210, milk: 682.987135271378, cost: 18911373, offline: 648.837778507809 },
  { grade: 30, production: 1328, cow: 15, speed: 210, milk: 698.844949302347, cost: 23203639, offline: 663.902701837229 },
  { grade: 31, production: 1358, cow: 16, speed: 220, milk: 754.247233265651, cost: 28450646, offline: 678.822509939086 },
  { grade: 32, production: 1387, cow: 16, speed: 220, milk: 770.670080559791, cost: 34861724, offline: 693.603072503812 },
  { grade: 33, production: 1416, cow: 17, speed: 230, milk: 833.23511975717, cost: 42691606, offline: 708.249851793595 },
  { grade: 34, production: 1446, cow: 17, speed: 230, milk: 850.315225923342, cost: 52250188, offline: 722.767942034841 },
  { grade: 35, production: 1474, cow: 18, speed: 240, milk: 921.452629985649, cost: 63914377, offline: 737.162103988519 },
  { grade: 36, production: 1503, cow: 18, speed: 240, milk: 939.295994257361, cost: 78142467, offline: 751.436795405888 },
  { grade: 37, production: 1531, cow: 19, speed: 250, milk: 1020.79493060936, cost: 95491578, offline: 765.596197957023 },
  { grade: 38, production: 1559, cow: 19, speed: 250, milk: 1039.52565482997, cost: 116638812, offline: 779.644241122478 },
  { grade: 39, production: 1587, cow: 20, speed: 260, milk: 1133.69231922783, cost: 142406902, offline: 793.584623459482 },
  { grade: 40, production: 1615, cow: 20, speed: 260, milk: 1153.45833084203, cost: 173795324, offline: 807.420831589424 },
  { grade: 41, production: 1642, cow: 20, speed: 270, milk: 1263.31716492355, cost: 308830081, offline: 821.156157200305 },
  { grade: 42, production: 2432, cow: 20, speed: 270, milk: 1870.73666517117, cost: 377475021, offline: 1215.97883236126 },
  { grade: 43, production: 2477, cow: 20, speed: 280, milk: 2064.24927715172, cost: 461187294, offline: 1238.54956629103 },
  { grade: 44, production: 2522, cow: 20, speed: 280, milk: 2101.6965485301, cost: 563241743, offline: 1261.01792911806 },
  { grade: 45, production: 2567, cow: 20, speed: 290, milk: 2333.43026387914, cost: 687619368, offline: 1283.38664513353 },
  { grade: 46, production: 2611, cow: 20, speed: 290, milk: 2373.92419743442, cost: 839158602, offline: 1305.65830858893 },
  { grade: 47, production: 2656, cow: 20, speed: 290, milk: 2414.24616824307, cost: 1023738817, offline: 1327.83539253369 },
  { grade: 48, production: 2700, cow: 20, speed: 290, milk: 2454.40046705588, cost: 1248502902, offline: 1349.92025688074 },
  { grade: 49, production: 2744, cow: 20, speed: 300, milk: 2743.83031156396, cost: 1522127175, offline: 1371.91515578198 },
  { grade: 50, production: 2788, cow: 20, speed: 300, milk: 2787.64448877048, cost: 0, offline: 1393.82224438524 }
];

class SimpleFarmConfigInitializer {
  constructor() {
    this.dbConfig = {
      host: process.env.DB_HOST || 'localhost',
      port: parseInt(process.env.DB_PORT) || 3669,
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASS || 'root',
      database: process.env.DB_NAME || 'wolf_fun_db'
    };
  }

  /**
   * 创建数据库连接
   */
  async createConnection() {
    try {
      const connection = await mysql.createConnection(this.dbConfig);
      console.log('✅ 数据库连接成功');
      return connection;
    } catch (error) {
      console.error('❌ 数据库连接失败:', error.message);
      throw error;
    }
  }

  /**
   * 检查表是否存在
   */
  async checkTablesExist(connection) {
    try {
      const [tables] = await connection.execute(`
        SELECT TABLE_NAME 
        FROM INFORMATION_SCHEMA.TABLES 
        WHERE TABLE_SCHEMA = ? AND TABLE_NAME IN ('farm_configs', 'farm_config_versions')
      `, [this.dbConfig.database]);

      const tableNames = tables.map(row => row.TABLE_NAME);
      
      return {
        farmConfigs: tableNames.includes('farm_configs'),
        farmConfigVersions: tableNames.includes('farm_config_versions')
      };
    } catch (error) {
      console.error('❌ 检查表失败:', error.message);
      return { farmConfigs: false, farmConfigVersions: false };
    }
  }

  /**
   * 创建表结构
   */
  async createTables(connection) {
    try {
      console.log('📦 创建数据库表...');

      // 创建 farm_config_versions 表
      await connection.execute(`
        CREATE TABLE IF NOT EXISTS farm_config_versions (
          id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
          version VARCHAR(50) NOT NULL UNIQUE,
          name VARCHAR(100) NOT NULL,
          description TEXT,
          isActive BOOLEAN NOT NULL DEFAULT FALSE,
          configCount INT UNSIGNED NOT NULL DEFAULT 0,
          createdBy VARCHAR(100),
          activatedAt DATETIME,
          createdAt DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
          updatedAt DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          INDEX idx_active (isActive),
          INDEX idx_created_at (createdAt)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
      `);

      // 创建 farm_configs 表
      await connection.execute(`
        CREATE TABLE IF NOT EXISTS farm_configs (
          id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
          grade INT UNSIGNED NOT NULL,
          production INT UNSIGNED NOT NULL,
          cow INT UNSIGNED NOT NULL,
          speed INT UNSIGNED NOT NULL,
          milk DECIMAL(15,3) NOT NULL,
          cost BIGINT UNSIGNED NOT NULL,
          offline DECIMAL(15,3) NOT NULL,
          version VARCHAR(50) NOT NULL DEFAULT 'default',
          isActive BOOLEAN NOT NULL DEFAULT FALSE,
          createdBy VARCHAR(100),
          remark TEXT,
          createdAt DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
          updatedAt DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          INDEX idx_grade (grade),
          INDEX idx_version (version),
          INDEX idx_active (isActive),
          INDEX idx_created_at (createdAt),
          UNIQUE KEY unique_grade_version (grade, version)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
      `);

      console.log('✅ 数据库表创建成功');
      return true;
    } catch (error) {
      console.error('❌ 创建表失败:', error.message);
      return false;
    }
  }

  /**
   * 检查是否已经初始化
   */
  async checkInitialized(connection) {
    try {
      const [configRows] = await connection.execute(
        'SELECT COUNT(*) as count FROM farm_configs WHERE isActive = 1'
      );
      
      const [versionRows] = await connection.execute(
        'SELECT COUNT(*) as count FROM farm_config_versions WHERE isActive = 1'
      );

      return {
        hasConfigs: configRows[0].count > 0,
        hasVersions: versionRows[0].count > 0,
        configCount: configRows[0].count,
        versionCount: versionRows[0].count
      };
    } catch (error) {
      console.error('❌ 检查初始化状态失败:', error.message);
      return { hasConfigs: false, hasVersions: false, configCount: 0, versionCount: 0 };
    }
  }

  /**
   * 插入配置数据
   */
  async insertConfigData(connection, force = false) {
    try {
      // 检查是否已经初始化
      const status = await this.checkInitialized(connection);
      
      if (status.hasConfigs && !force) {
        console.log(`⚠️ 系统已经初始化过了 (${status.configCount} 条配置)`);
        console.log('如果要重新初始化，请使用 --force 参数');
        return false;
      }

      if (force && status.hasConfigs) {
        console.log('🗑️ 强制重新初始化，清理现有数据...');
        await connection.execute('DELETE FROM farm_configs');
        await connection.execute('DELETE FROM farm_config_versions');
        console.log('✅ 现有数据已清理');
      }

      console.log('📊 开始插入配置数据...');

      // 创建版本记录
      const version = 'v1.0.0-initial';
      const versionName = '初始版本';
      const description = '基于fram.xlsx的初始配置数据，包含0-50级完整配置';

      const now = new Date();
      await connection.execute(`
        INSERT INTO farm_config_versions (version, name, description, isActive, configCount, createdBy, activatedAt, createdAt, updatedAt)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [version, versionName, description, true, REAL_CONFIG_DATA.length, 'system', now, now, now]);

      console.log(`✅ 版本记录创建成功: ${version}`);

      // 批量插入配置数据
      const insertSQL = `
        INSERT INTO farm_configs (grade, production, cow, speed, milk, cost, offline, version, isActive, createdBy, createdAt, updatedAt)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `;

      for (const config of REAL_CONFIG_DATA) {
        await connection.execute(insertSQL, [
          config.grade,
          config.production,
          config.cow,
          config.speed,
          parseFloat(config.milk.toFixed(3)),
          config.cost,
          parseFloat(config.offline.toFixed(3)),
          version,
          true,
          'system',
          now,
          now
        ]);
      }

      console.log(`✅ 配置数据插入成功 (${REAL_CONFIG_DATA.length} 条)`);
      return true;
    } catch (error) {
      console.error('❌ 插入配置数据失败:', error.message);
      return false;
    }
  }

  /**
   * 验证初始化结果
   */
  async validateInitialization(connection) {
    try {
      console.log('🔍 验证初始化结果...');

      // 检查配置数量
      const [configRows] = await connection.execute(
        'SELECT COUNT(*) as count FROM farm_configs WHERE isActive = 1'
      );

      // 检查版本信息
      const [versionRows] = await connection.execute(
        'SELECT * FROM farm_config_versions WHERE isActive = 1'
      );

      // 检查等级范围
      const [gradeRows] = await connection.execute(
        'SELECT grade FROM farm_configs WHERE isActive = 1 ORDER BY grade'
      );

      const configCount = configRows[0].count;
      const activeVersion = versionRows[0];
      const grades = gradeRows.map(row => row.grade);
      const expectedGrades = Array.from({ length: 51 }, (_, i) => i);

      console.log(`✅ 配置数据: ${configCount} 条`);
      console.log(`✅ 激活版本: ${activeVersion?.version || '无'}`);

      if (JSON.stringify(grades) === JSON.stringify(expectedGrades)) {
        console.log('✅ 数据完整性检查通过 (0-50级)');
        return true;
      } else {
        console.log('❌ 数据完整性检查失败');
        const missing = expectedGrades.filter(g => !grades.includes(g));
        console.log('缺失等级:', missing);
        return false;
      }
    } catch (error) {
      console.error('❌ 验证失败:', error.message);
      return false;
    }
  }

  /**
   * 执行完整初始化
   */
  async initialize(force = false) {
    let connection;
    
    try {
      console.log('🚀 开始农场配置系统初始化...\n');

      // 1. 创建数据库连接
      connection = await this.createConnection();

      // 2. 检查并创建表
      const tablesExist = await this.checkTablesExist(connection);
      if (!tablesExist.farmConfigs || !tablesExist.farmConfigVersions) {
        const created = await this.createTables(connection);
        if (!created) {
          throw new Error('创建表失败');
        }
      } else {
        console.log('✅ 数据库表已存在');
      }

      // 3. 插入配置数据
      const inserted = await this.insertConfigData(connection, force);
      if (!inserted && !force) {
        return false;
      }

      // 4. 验证结果
      const validated = await this.validateInitialization(connection);

      if (validated) {
        console.log('\n🎉 农场配置系统初始化完成！');
        console.log('\n📋 下一步操作:');
        console.log('1. 启动应用: npm start');
        console.log('2. 测试API: curl http://localhost:3000/api/admin/farm-config/current');
        return true;
      } else {
        console.log('\n⚠️ 初始化完成但验证失败');
        return false;
      }

    } catch (error) {
      console.error('❌ 初始化失败:', error.message);
      return false;
    } finally {
      if (connection) {
        await connection.end();
      }
    }
  }
}

// 主函数
async function main() {
  const args = process.argv.slice(2);
  const force = args.includes('--force');

  const initializer = new SimpleFarmConfigInitializer();

  try {
    const success = await initializer.initialize(force);
    process.exit(success ? 0 : 1);
  } catch (error) {
    console.error('❌ 执行失败:', error);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = SimpleFarmConfigInitializer;
