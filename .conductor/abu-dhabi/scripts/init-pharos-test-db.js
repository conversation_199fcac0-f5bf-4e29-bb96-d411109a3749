#!/usr/bin/env node

// Pharos Test 数据库初始化脚本
// 用于创建基础表结构

const { Sequelize } = require('sequelize');
require('dotenv').config();

// 创建数据库连接
const sequelize = new Sequelize(
  process.env.DB_NAME || 'pharos_test_db',
  process.env.DB_USER || 'pharos_test',
  process.env.DB_PASS || '00321zixunadmin',
  {
    host: process.env.DB_HOST || 'localhost',
    dialect: 'mysql',
    port: Number(process.env.DB_PORT) || 3671,
    logging: console.log,
    timezone: '+08:00',
    dialectOptions: {
      charset: 'utf8mb4',
      connectTimeout: 60000,
    },
    pool: {
      max: 10,
      min: 0,
      acquire: 30000,
      idle: 10000
    }
  }
);

async function initializeDatabase() {
  try {
    console.log('🚀 开始初始化 Pharos Test 数据库...');
    
    // 测试连接
    await sequelize.authenticate();
    console.log('✅ 数据库连接成功');

    const queryInterface = sequelize.getQueryInterface();

    // 1. 创建 users 表
    console.log('📝 创建 users 表...');
    await queryInterface.createTable('users', {
      id: {
        type: Sequelize.INTEGER.UNSIGNED,
        autoIncrement: true,
        primaryKey: true,
      },
      telegramId: {
        type: Sequelize.STRING,
        allowNull: false,
        unique: true,
      },
      hasFollowedChannel: {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
      },
      referralCount: {
        type: Sequelize.INTEGER.UNSIGNED,
        allowNull: false,
        defaultValue: 0,
      },
      referrerId: {
        type: Sequelize.INTEGER.UNSIGNED,
        allowNull: true,
      },
      firstWalletId: {
        type: Sequelize.INTEGER.UNSIGNED,
        allowNull: true,
      },
      username: Sequelize.STRING,
      refWalletAddress: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      firstName: Sequelize.STRING,
      lastName: Sequelize.STRING,
      photoUrl: Sequelize.STRING,
      authDate: Sequelize.INTEGER,
      hash: Sequelize.STRING,
      telegram_premium: {
        type: Sequelize.BOOLEAN,
        allowNull: true,
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
      },
    });

    // 2. 创建 user_wallets 表
    console.log('📝 创建 user_wallets 表...');
    await queryInterface.createTable('user_wallets', {
      id: {
        type: Sequelize.INTEGER.UNSIGNED,
        autoIncrement: true,
        primaryKey: true,
      },
      userId: {
        type: Sequelize.INTEGER.UNSIGNED,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      walletAddress: {
        type: Sequelize.STRING,
        allowNull: false,
        unique: true,
      },
      gem: {
        type: Sequelize.DECIMAL(65, 3),
        allowNull: false,
        defaultValue: 0,
      },
      milk: {
        type: Sequelize.DECIMAL(65, 3),
        allowNull: false,
        defaultValue: 0,
      },
      pendingMilk: {
        type: Sequelize.DECIMAL(65, 3),
        allowNull: false,
        defaultValue: 0,
      },
      phrsBalance: {
        type: Sequelize.DECIMAL(65, 3),
        allowNull: false,
        defaultValue: 0,
      },
      phrsWalletAddress: {
        type: Sequelize.STRING(42),
        allowNull: true,
      },
      lastActiveTime: {
        type: Sequelize.DATE,
        allowNull: true,
      },
      lastPhrsUpdateTime: {
        type: Sequelize.DATE,
        allowNull: true,
      },
      accumulatedOfflineGems: {
        type: Sequelize.DECIMAL(65, 3),
        allowNull: false,
        defaultValue: 0,
      },
      lastOfflineRewardCalculation: {
        type: Sequelize.DATE,
        allowNull: true,
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
      },
    });

    // 3. 创建 farm_plots 表
    console.log('📝 创建 farm_plots 表...');
    await queryInterface.createTable('farm_plots', {
      id: {
        type: Sequelize.INTEGER.UNSIGNED,
        autoIncrement: true,
        primaryKey: true,
      },
      walletId: {
        type: Sequelize.INTEGER.UNSIGNED,
        allowNull: false,
        references: {
          model: 'user_wallets',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      plotNumber: {
        type: Sequelize.INTEGER.UNSIGNED,
        allowNull: false,
      },
      level: {
        type: Sequelize.INTEGER.UNSIGNED,
        allowNull: false,
        defaultValue: 1,
      },
      barnCount: {
        type: Sequelize.INTEGER.UNSIGNED,
        allowNull: false,
        defaultValue: 1,
      },
      milkProduction: {
        type: Sequelize.DECIMAL(65, 3),
        allowNull: false,
        defaultValue: 1,
      },
      productionSpeed: {
        type: Sequelize.DECIMAL(65, 3),
        allowNull: false,
        defaultValue: 5,
      },
      unlockCost: {
        type: Sequelize.DECIMAL(65, 3),
        allowNull: false,
        defaultValue: 2000,
      },
      upgradeCost: {
        type: Sequelize.DECIMAL(65, 3),
        allowNull: false,
        defaultValue: 200,
      },
      lastProductionTime: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
      },
      isUnlocked: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: false,
      },
      accumulatedMilk: {
        type: Sequelize.DECIMAL(65, 3),
        allowNull: false,
        defaultValue: 0,
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
      },
    });

    // 4. 创建 delivery_lines 表
    console.log('📝 创建 delivery_lines 表...');
    await queryInterface.createTable('delivery_lines', {
      id: {
        type: Sequelize.INTEGER.UNSIGNED,
        autoIncrement: true,
        primaryKey: true,
      },
      walletId: {
        type: Sequelize.INTEGER.UNSIGNED,
        allowNull: false,
        references: {
          model: 'user_wallets',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      lineNumber: {
        type: Sequelize.INTEGER.UNSIGNED,
        allowNull: false,
      },
      level: {
        type: Sequelize.INTEGER.UNSIGNED,
        allowNull: false,
        defaultValue: 1,
      },
      capacity: {
        type: Sequelize.DECIMAL(65, 3),
        allowNull: false,
        defaultValue: 10,
      },
      deliverySpeed: {
        type: Sequelize.DECIMAL(65, 3),
        allowNull: false,
        defaultValue: 1,
      },
      blockPrice: {
        type: Sequelize.DECIMAL(65, 3),
        allowNull: false,
        defaultValue: 1,
      },
      unlockCost: {
        type: Sequelize.DECIMAL(65, 3),
        allowNull: false,
        defaultValue: 5000,
      },
      upgradeCost: {
        type: Sequelize.DECIMAL(65, 3),
        allowNull: false,
        defaultValue: 500,
      },
      lastDeliveryTime: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
      },
      isUnlocked: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: false,
      },
      currentMilk: {
        type: Sequelize.DECIMAL(65, 3),
        allowNull: false,
        defaultValue: 0,
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
      },
    });

    // 5. 创建 phrs_deposits 表
    console.log('📝 创建 phrs_deposits 表...');
    await queryInterface.createTable('phrs_deposits', {
      id: {
        type: Sequelize.INTEGER.UNSIGNED,
        autoIncrement: true,
        primaryKey: true,
      },
      walletId: {
        type: Sequelize.INTEGER.UNSIGNED,
        allowNull: true,
        references: {
          model: 'user_wallets',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL',
      },
      userAddress: {
        type: Sequelize.STRING(42),
        allowNull: false,
      },
      amount: {
        type: Sequelize.DECIMAL(65, 18),
        allowNull: false,
      },
      transactionHash: {
        type: Sequelize.STRING(66),
        allowNull: false,
        unique: true,
      },
      blockNumber: {
        type: Sequelize.BIGINT.UNSIGNED,
        allowNull: false,
      },
      blockHash: {
        type: Sequelize.STRING(66),
        allowNull: false,
      },
      logIndex: {
        type: Sequelize.INTEGER.UNSIGNED,
        allowNull: false,
      },
      status: {
        type: Sequelize.ENUM('pending', 'confirmed', 'failed'),
        allowNull: false,
        defaultValue: 'pending',
      },
      processedAt: {
        type: Sequelize.DATE,
        allowNull: true,
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
      },
    });

    // 添加索引
    console.log('📝 添加索引...');

    // farm_plots 索引
    await queryInterface.addIndex('farm_plots', ['walletId', 'plotNumber'], {
      unique: true,
      name: 'farm_plots_wallet_plot_unique',
    });

    // delivery_lines 索引
    await queryInterface.addIndex('delivery_lines', ['walletId', 'lineNumber'], {
      unique: true,
      name: 'delivery_lines_wallet_line_unique',
    });

    // phrs_deposits 索引
    await queryInterface.addIndex('phrs_deposits', ['userAddress'], {
      name: 'idx_phrs_deposits_user_address',
    });
    await queryInterface.addIndex('phrs_deposits', ['blockNumber'], {
      name: 'idx_phrs_deposits_block_number',
    });
    await queryInterface.addIndex('phrs_deposits', ['status'], {
      name: 'idx_phrs_deposits_status',
    });

    // user_wallets 索引
    await queryInterface.addIndex('user_wallets', ['phrsWalletAddress'], {
      name: 'idx_user_wallets_phrs_wallet_address',
    });
    await queryInterface.addIndex('user_wallets', ['lastActiveTime'], {
      name: 'idx_user_wallets_last_active_time',
    });

    console.log('✅ 基础表和索引创建完成');
    console.log('🎉 Pharos Test 数据库初始化成功！');

  } catch (error) {
    console.error('❌ 数据库初始化失败:', error);
    throw error;
  } finally {
    await sequelize.close();
  }
}

// 运行初始化
if (require.main === module) {
  initializeDatabase()
    .then(() => {
      console.log('✅ 初始化完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ 初始化失败:', error);
      process.exit(1);
    });
}

module.exports = { initializeDatabase };
