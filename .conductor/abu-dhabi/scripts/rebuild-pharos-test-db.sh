#!/bin/bash

# Pharos Test 数据库重建脚本
# 用于完全重建数据库环境，解决认证插件问题

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🔄 开始重建 Pharos Test 数据库环境...${NC}"

# 1. 停止现有服务
echo -e "${YELLOW}🛑 停止现有服务...${NC}"
docker compose -f docker-compose.pharos-test.yml down

# 2. 清理数据卷和日志
echo -e "${YELLOW}🧹 清理数据卷和日志...${NC}"
rm -rf mysql-data-pharos-test logs/mysql logs/redis 2>/dev/null || true
mkdir -p logs/mysql logs/redis

# 3. 创建日志目录并设置权限
echo -e "${YELLOW}📁 设置目录权限...${NC}"
chmod -R 755 logs/ 2>/dev/null || true

# 4. 启动 MySQL 服务
echo -e "${YELLOW}🚀 启动 MySQL 服务...${NC}"
docker compose -f docker-compose.pharos-test.yml up -d mysql

# 5. 等待 MySQL 启动
echo -e "${YELLOW}⏳ 等待 MySQL 启动...${NC}"
sleep 30

# 6. 检查 MySQL 状态
echo -e "${YELLOW}🔍 检查 MySQL 状态...${NC}"
max_attempts=10
attempt=1

while [ $attempt -le $max_attempts ]; do
    echo "检查尝试 $attempt/$max_attempts..."
    
    if docker compose -f docker-compose.pharos-test.yml exec mysql mysqladmin ping -h localhost -u root -p00321zixun > /dev/null 2>&1; then
        echo -e "${GREEN}✅ MySQL 启动成功${NC}"
        break
    fi
    
    if [ $attempt -eq $max_attempts ]; then
        echo -e "${RED}❌ MySQL 启动失败${NC}"
        docker compose -f docker-compose.pharos-test.yml logs mysql
        exit 1
    fi
    
    sleep 5
    ((attempt++))
done

# 7. 执行认证插件初始化
echo -e "${YELLOW}🔐 初始化认证插件...${NC}"
docker compose -f docker-compose.pharos-test.yml exec mysql mysql -u root -p00321zixun < scripts/init-mysql-auth.sql

# 8. 启动 Redis
echo -e "${YELLOW}🚀 启动 Redis...${NC}"
docker compose -f docker-compose.pharos-test.yml up -d redis

# 9. 等待 Redis 启动
echo -e "${YELLOW}⏳ 等待 Redis 启动...${NC}"
sleep 10

# 10. 初始化数据库表结构
echo -e "${YELLOW}🗄️ 初始化数据库表结构...${NC}"
node scripts/init-pharos-test-db.js

# 11. 测试数据库连接
echo -e "${YELLOW}🔍 测试数据库连接...${NC}"
node scripts/test-pharos-db-connection.js

# 12. 显示服务状态
echo -e "${BLUE}📊 服务状态:${NC}"
docker compose -f docker-compose.pharos-test.yml ps

echo -e "${GREEN}🎉 Pharos Test 数据库环境重建完成！${NC}"
echo -e "${BLUE}📋 访问信息:${NC}"
echo -e "   - MySQL 端口: 3671"
echo -e "   - Redis 端口: 6258"
echo -e "   - phpMyAdmin: http://localhost:8271"

echo -e "${YELLOW}💡 下一步:${NC}"
echo -e "   - 启动应用: npm run docker:pharos-test:build"
echo -e "   - 查看日志: docker compose -f docker-compose.pharos-test.yml logs -f"
