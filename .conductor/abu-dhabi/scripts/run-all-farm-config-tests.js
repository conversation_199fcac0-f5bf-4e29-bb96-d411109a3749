#!/usr/bin/env node

/**
 * 农场配置系统完整测试运行脚本
 * 运行所有相关测试：单元测试、集成测试、性能测试、迁移验证
 */

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

class FarmConfigTestRunner {
  constructor() {
    this.testResults = [];
    this.startTime = Date.now();
  }

  /**
   * 运行命令并返回结果
   */
  async runCommand(command, args = [], options = {}) {
    return new Promise((resolve, reject) => {
      console.log(`🚀 运行命令: ${command} ${args.join(' ')}`);
      
      const child = spawn(command, args, {
        stdio: 'inherit',
        shell: true,
        ...options
      });

      child.on('close', (code) => {
        if (code === 0) {
          resolve({ success: true, code });
        } else {
          resolve({ success: false, code });
        }
      });

      child.on('error', (error) => {
        reject(error);
      });
    });
  }

  /**
   * 记录测试结果
   */
  recordTestResult(testName, success, duration, details = {}) {
    const result = {
      test: testName,
      success,
      duration,
      details,
      timestamp: new Date().toISOString()
    };
    
    this.testResults.push(result);
    
    const status = success ? '✅' : '❌';
    const durationStr = duration ? ` (${(duration / 1000).toFixed(1)}s)` : '';
    console.log(`${status} ${testName}${durationStr}`);
    
    return result;
  }

  /**
   * 检查环境依赖
   */
  async checkEnvironment() {
    console.log('🔍 检查环境依赖...\n');
    
    const checks = [
      { name: 'Node.js', command: 'node', args: ['--version'] },
      { name: 'npm', command: 'npm', args: ['--version'] },
      { name: 'MySQL', command: 'mysql', args: ['--version'] },
    ];

    for (const check of checks) {
      try {
        const result = await this.runCommand(check.command, check.args, { stdio: 'pipe' });
        this.recordTestResult(`环境检查-${check.name}`, result.success, 0);
      } catch (error) {
        this.recordTestResult(`环境检查-${check.name}`, false, 0, { error: error.message });
      }
    }
  }

  /**
   * 运行数据库迁移
   */
  async runMigration() {
    console.log('\n📦 运行数据库迁移...\n');
    
    const startTime = Date.now();
    
    try {
      // 运行迁移脚本
      const migrationScript = path.join(__dirname, 'migrate-farm-config.js');
      const result = await this.runCommand('node', [migrationScript]);
      
      const duration = Date.now() - startTime;
      this.recordTestResult('数据库迁移', result.success, duration);
      
      return result.success;
    } catch (error) {
      const duration = Date.now() - startTime;
      this.recordTestResult('数据库迁移', false, duration, { error: error.message });
      return false;
    }
  }

  /**
   * 运行迁移验证
   */
  async runMigrationValidation() {
    console.log('\n🔍 运行迁移验证...\n');
    
    const startTime = Date.now();
    
    try {
      const validationScript = path.join(__dirname, 'validate-migration.js');
      const result = await this.runCommand('node', [validationScript]);
      
      const duration = Date.now() - startTime;
      this.recordTestResult('迁移验证', result.success, duration);
      
      return result.success;
    } catch (error) {
      const duration = Date.now() - startTime;
      this.recordTestResult('迁移验证', false, duration, { error: error.message });
      return false;
    }
  }

  /**
   * 运行单元测试
   */
  async runUnitTests() {
    console.log('\n🧪 运行单元测试...\n');
    
    const startTime = Date.now();
    
    try {
      // 检查是否有测试框架
      const testFile = path.join(__dirname, '../test/farmConfig.test.js');
      
      if (!fs.existsSync(testFile)) {
        this.recordTestResult('单元测试', false, 0, { error: '测试文件不存在' });
        return false;
      }

      // 运行Mocha测试
      const result = await this.runCommand('npx', ['mocha', testFile, '--timeout', '30000'], {
        cwd: path.join(__dirname, '..')
      });
      
      const duration = Date.now() - startTime;
      this.recordTestResult('单元测试', result.success, duration);
      
      return result.success;
    } catch (error) {
      const duration = Date.now() - startTime;
      this.recordTestResult('单元测试', false, duration, { error: error.message });
      return false;
    }
  }

  /**
   * 运行集成测试
   */
  async runIntegrationTests() {
    console.log('\n🔗 运行集成测试...\n');
    
    const startTime = Date.now();
    
    try {
      const integrationScript = path.join(__dirname, 'test-farm-config-integration.js');
      const result = await this.runCommand('node', [integrationScript]);
      
      const duration = Date.now() - startTime;
      this.recordTestResult('集成测试', result.success, duration);
      
      return result.success;
    } catch (error) {
      const duration = Date.now() - startTime;
      this.recordTestResult('集成测试', false, duration, { error: error.message });
      return false;
    }
  }

  /**
   * 运行性能测试
   */
  async runPerformanceTests() {
    console.log('\n⚡ 运行性能测试...\n');
    
    const startTime = Date.now();
    
    try {
      const performanceScript = path.join(__dirname, 'performance-test-farm-config.js');
      const result = await this.runCommand('node', [performanceScript]);
      
      const duration = Date.now() - startTime;
      this.recordTestResult('性能测试', result.success, duration);
      
      return result.success;
    } catch (error) {
      const duration = Date.now() - startTime;
      this.recordTestResult('性能测试', false, duration, { error: error.message });
      return false;
    }
  }

  /**
   * 生成综合测试报告
   */
  generateComprehensiveReport() {
    const totalDuration = Date.now() - this.startTime;
    const totalTests = this.testResults.length;
    const passedTests = this.testResults.filter(r => r.success).length;
    const failedTests = totalTests - passedTests;

    console.log('\n' + '='.repeat(80));
    console.log('🎯 农场配置系统完整测试报告');
    console.log('='.repeat(80));
    console.log(`⏱️  总耗时: ${(totalDuration / 1000).toFixed(1)}秒`);
    console.log(`📊 总测试数: ${totalTests}`);
    console.log(`✅ 通过: ${passedTests}`);
    console.log(`❌ 失败: ${failedTests}`);
    console.log(`📈 通过率: ${((passedTests / totalTests) * 100).toFixed(1)}%`);
    
    // 按类别显示结果
    console.log('\n📋 测试结果详情:');
    this.testResults.forEach(result => {
      const status = result.success ? '✅' : '❌';
      const duration = result.duration ? ` (${(result.duration / 1000).toFixed(1)}s)` : '';
      console.log(`   ${status} ${result.test}${duration}`);
    });

    // 失败测试详情
    const failedResults = this.testResults.filter(r => !r.success);
    if (failedResults.length > 0) {
      console.log('\n❌ 失败测试详情:');
      failedResults.forEach(result => {
        console.log(`   • ${result.test}: ${result.details?.error || '未知错误'}`);
      });
    }

    // 性能评估
    const performanceResult = this.testResults.find(r => r.test === '性能测试');
    if (performanceResult) {
      console.log('\n⚡ 性能评估:');
      if (performanceResult.success) {
        console.log('   🏆 性能测试通过 - 系统性能良好');
      } else {
        console.log('   ⚠️  性能测试失败 - 需要优化性能');
      }
    }

    // 建议
    console.log('\n💡 建议:');
    if (failedTests === 0) {
      console.log('   🎉 所有测试通过！系统可以安全部署。');
    } else if (failedTests <= 2) {
      console.log('   ⚠️  少数测试失败，建议修复后再部署。');
    } else {
      console.log('   🚨 多个测试失败，强烈建议修复所有问题后再部署。');
    }

    // 保存综合报告
    const reportPath = path.join(__dirname, 'comprehensive-test-report.json');
    fs.writeFileSync(reportPath, JSON.stringify({
      summary: {
        totalDuration: totalDuration,
        totalTests,
        passedTests,
        failedTests,
        passRate: (passedTests / totalTests) * 100,
        timestamp: new Date().toISOString(),
        environment: {
          nodeVersion: process.version,
          platform: process.platform,
          arch: process.arch
        }
      },
      results: this.testResults
    }, null, 2));

    console.log(`\n📄 综合测试报告已保存到: ${reportPath}`);
    console.log('='.repeat(80));

    return failedTests === 0;
  }

  /**
   * 运行所有测试
   */
  async runAllTests() {
    console.log('🚀 开始农场配置系统完整测试套件...\n');
    console.log('测试包括：环境检查、数据库迁移、迁移验证、单元测试、集成测试、性能测试\n');

    try {
      // 1. 环境检查
      await this.checkEnvironment();

      // 2. 数据库迁移
      const migrationSuccess = await this.runMigration();
      if (!migrationSuccess) {
        console.log('❌ 数据库迁移失败，跳过后续测试');
        return this.generateComprehensiveReport();
      }

      // 3. 迁移验证
      const validationSuccess = await this.runMigrationValidation();
      if (!validationSuccess) {
        console.log('⚠️ 迁移验证失败，但继续运行其他测试');
      }

      // 4. 单元测试
      await this.runUnitTests();

      // 5. 集成测试
      await this.runIntegrationTests();

      // 6. 性能测试
      await this.runPerformanceTests();

      // 生成综合报告
      const allPassed = this.generateComprehensiveReport();

      return allPassed;
    } catch (error) {
      console.error('❌ 测试套件执行失败:', error);
      this.recordTestResult('测试套件执行', false, Date.now() - this.startTime, { error: error.message });
      this.generateComprehensiveReport();
      return false;
    }
  }
}

// 主函数
async function main() {
  const runner = new FarmConfigTestRunner();

  try {
    const success = await runner.runAllTests();
    
    if (success) {
      console.log('\n🎉 所有测试通过！农场配置系统重构成功！');
    } else {
      console.log('\n⚠️ 部分测试失败，请检查并修复问题。');
    }
    
    process.exit(success ? 0 : 1);
  } catch (error) {
    console.error('❌ 测试运行失败:', error);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = FarmConfigTestRunner;
