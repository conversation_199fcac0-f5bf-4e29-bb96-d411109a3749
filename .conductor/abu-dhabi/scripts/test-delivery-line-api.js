const axios = require('axios');

// API测试配置
const API_BASE_URL = process.env.API_BASE_URL || 'http://localhost:3000';
const TEST_TOKEN = process.env.TEST_TOKEN || 'your-test-token-here';

async function testDeliveryLineAPI() {
    console.log('🧪 开始测试流水线API...\n');
    
    try {
        // 1. 测试获取配置接口
        console.log('1️⃣ 测试获取配置接口...');
        
        try {
            const configResponse = await axios.get(`${API_BASE_URL}/api/delivery-line/configs`, {
                headers: {
                    'Authorization': `Bearer ${TEST_TOKEN}`
                }
            });
            
            if (configResponse.data.ok) {
                const configs = configResponse.data.data.configs;
                console.log(`✅ 成功获取 ${configs.length} 个配置`);
                
                if (configs.length !== 50) {
                    throw new Error(`配置数量不正确，期望50个，实际${configs.length}个`);
                }
                
                // 显示前5个和后5个配置
                console.log('前5个配置:');
                configs.slice(0, 5).forEach(config => {
                    console.log(`  等级${config.level}: 利润=${config.profit}, 容量=${config.capacity}, 间隔=${config.productionInterval}s, 费用=${config.upgradeCost}`);
                });
                
                console.log('后5个配置:');
                configs.slice(-5).forEach(config => {
                    console.log(`  等级${config.level}: 利润=${config.profit}, 容量=${config.capacity}, 间隔=${config.productionInterval}s, 费用=${config.upgradeCost}`);
                });
                
            } else {
                throw new Error(`获取配置失败: ${configResponse.data.error}`);
            }
        } catch (error) {
            if (error.response) {
                console.error(`❌ API错误 (${error.response.status}): ${error.response.data?.error || error.response.statusText}`);
            } else if (error.code === 'ECONNREFUSED') {
                console.warn('⚠️ 无法连接到API服务器，跳过API测试');
                console.log('请确保服务器正在运行: npm run dev');
                return;
            } else {
                throw error;
            }
        }
        
        // 2. 测试获取用户流水线接口
        console.log('\n2️⃣ 测试获取用户流水线接口...');
        
        try {
            const deliveryLineResponse = await axios.get(`${API_BASE_URL}/api/delivery-line`, {
                headers: {
                    'Authorization': `Bearer ${TEST_TOKEN}`
                }
            });
            
            if (deliveryLineResponse.data.ok) {
                const deliveryLine = deliveryLineResponse.data.data.deliveryLine;
                console.log(`✅ 获取流水线成功:`);
                console.log(`  等级: ${deliveryLine.level}`);
                console.log(`  速度: ${deliveryLine.deliverySpeed}s`);
                console.log(`  容量: ${deliveryLine.blockUnit}`);
                console.log(`  价格: ${deliveryLine.blockPrice}`);
                console.log(`  升级费用: ${deliveryLine.upgradeCost}`);
                
                if (deliveryLine.nextUpgradeGrowth) {
                    console.log(`  下次升级预览:`);
                    console.log(`    速度: ${deliveryLine.nextUpgradeGrowth.nextDeliverySpeed}s`);
                    console.log(`    容量: ${deliveryLine.nextUpgradeGrowth.nextBlockUnit}`);
                    console.log(`    价格: ${deliveryLine.nextUpgradeGrowth.nextBlockPrice}`);
                }
                
            } else {
                throw new Error(`获取流水线失败: ${deliveryLineResponse.data.error}`);
            }
        } catch (error) {
            if (error.response) {
                console.error(`❌ API错误 (${error.response.status}): ${error.response.data?.error || error.response.statusText}`);
            } else {
                throw error;
            }
        }
        
        // 3. 测试升级接口（如果有足够的GEM）
        console.log('\n3️⃣ 测试升级接口...');
        
        try {
            const upgradeResponse = await axios.post(`${API_BASE_URL}/api/delivery-line/upgrade`, {}, {
                headers: {
                    'Authorization': `Bearer ${TEST_TOKEN}`
                }
            });
            
            if (upgradeResponse.data.ok) {
                const upgradedLine = upgradeResponse.data.data.deliveryLine;
                console.log(`✅ 升级成功:`);
                console.log(`  新等级: ${upgradedLine.level}`);
                console.log(`  新速度: ${upgradedLine.deliverySpeed}s`);
                console.log(`  新容量: ${upgradedLine.blockUnit}`);
                console.log(`  新价格: ${upgradedLine.blockPrice}`);
                
            } else {
                console.log(`ℹ️ 升级失败 (可能是GEM不足或已达最高等级): ${upgradeResponse.data.error}`);
            }
        } catch (error) {
            if (error.response) {
                console.log(`ℹ️ 升级失败 (${error.response.status}): ${error.response.data?.error || error.response.statusText}`);
            } else {
                throw error;
            }
        }
        
        console.log('\n🎉 API测试完成！');
        
    } catch (error) {
        console.error('\n❌ 测试失败:', error.message);
        throw error;
    }
}

// 显示使用说明
function showUsage() {
    console.log('📋 使用说明:');
    console.log('1. 确保服务器正在运行: npm run dev');
    console.log('2. 设置环境变量:');
    console.log('   export API_BASE_URL=http://localhost:3000');
    console.log('   export TEST_TOKEN=your-test-token');
    console.log('3. 运行测试: node scripts/test-delivery-line-api.js');
    console.log('');
}

// 执行测试
if (require.main === module) {
    if (!TEST_TOKEN || TEST_TOKEN === 'your-test-token-here') {
        console.warn('⚠️ 未设置测试令牌');
        showUsage();
        process.exit(1);
    }
    
    testDeliveryLineAPI()
        .then(() => {
            console.log('\n✅ 测试完成');
            process.exit(0);
        })
        .catch(error => {
            console.error('\n❌ 测试异常:', error);
            showUsage();
            process.exit(1);
        });
}

module.exports = { testDeliveryLineAPI };
