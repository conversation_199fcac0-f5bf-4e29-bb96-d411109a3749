const { Sequelize, DataTypes } = require('sequelize');
const mysql = require('mysql2/promise');

// 数据库配置
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'moofun_kaia_tasks_test',
  port: process.env.DB_PORT || 3306
};

async function testDeliveryLineConfigs() {
    console.log('🧪 开始测试流水线配置系统...\n');
    
    let connection;
    
    try {
        // 创建数据库连接
        connection = await mysql.createConnection(dbConfig);
        
        // 1. 测试配置表是否存在
        console.log('1️⃣ 检查配置表...');
        
        const [tables] = await connection.execute(
            "SHOW TABLES LIKE 'delivery_line_configs'"
        );
        
        if (tables.length === 0) {
            throw new Error('配置表 delivery_line_configs 不存在');
        }
        
        console.log('✅ 配置表存在');
        
        // 2. 检查配置数据
        console.log('\n2️⃣ 检查配置数据...');
        
        const [configs] = await connection.execute(
            'SELECT * FROM delivery_line_configs ORDER BY grade ASC'
        );
        
        console.log(`✅ 找到 ${configs.length} 个配置`);
        
        if (configs.length !== 50) {
            throw new Error(`配置数量不正确，期望50个，实际${configs.length}个`);
        }
        
        // 验证关键等级的配置
        const level1 = configs.find(c => c.grade === 1);
        const level50 = configs.find(c => c.grade === 50);
        
        console.log(`等级1配置: 利润=${level1.profit}, 容量=${level1.capacity}, 间隔=${level1.production_interval}s, 费用=${level1.upgrade_cost}`);
        console.log(`等级50配置: 利润=${level50.profit}, 容量=${level50.capacity}, 间隔=${level50.production_interval}s, 费用=${level50.upgrade_cost}`);
        
        // 验证数据完整性
        for (let i = 1; i <= 50; i++) {
            const config = configs.find(c => c.grade === i);
            if (!config) {
                throw new Error(`缺少等级 ${i} 的配置`);
            }
            
            if (!config.profit || !config.capacity || !config.production_interval || !config.upgrade_cost) {
                throw new Error(`等级 ${i} 的配置数据不完整`);
            }
        }
        
        console.log('✅ 所有等级配置完整');
        
        // 3. 检查现有流水线数据
        console.log('\n3️⃣ 检查现有流水线数据...');
        
        const [deliveryLines] = await connection.execute(
            'SELECT * FROM delivery_lines'
        );
        
        console.log(`找到 ${deliveryLines.length} 个现有流水线`);
        
        if (deliveryLines.length > 0) {
            // 验证流水线数据是否与配置匹配
            for (const line of deliveryLines) {
                const config = configs.find(c => c.grade === line.level);
                if (!config) {
                    console.warn(`警告: 流水线等级 ${line.level} 没有对应配置`);
                    continue;
                }
                
                const speedMatch = Math.abs(parseFloat(line.deliverySpeed) - parseFloat(config.production_interval)) < 0.01;
                const capacityMatch = parseInt(line.blockUnit) === parseInt(config.capacity);
                const priceMatch = parseInt(line.blockPrice) === parseInt(config.profit);
                
                if (!speedMatch || !capacityMatch || !priceMatch) {
                    console.warn(`警告: 流水线 ID ${line.id} 数据与配置不匹配`);
                    console.warn(`  速度: ${line.deliverySpeed} vs ${config.production_interval} (匹配: ${speedMatch})`);
                    console.warn(`  容量: ${line.blockUnit} vs ${config.capacity} (匹配: ${capacityMatch})`);
                    console.warn(`  价格: ${line.blockPrice} vs ${config.profit} (匹配: ${priceMatch})`);
                } else {
                    console.log(`✅ 流水线 ID ${line.id} (等级${line.level}) 数据与配置匹配`);
                }
            }
        }
        
        // 4. 测试配置查询性能
        console.log('\n4️⃣ 测试配置查询性能...');
        
        const startTime = Date.now();
        
        for (let i = 1; i <= 50; i++) {
            await connection.execute(
                'SELECT * FROM delivery_line_configs WHERE grade = ?',
                [i]
            );
        }
        
        const endTime = Date.now();
        const queryTime = endTime - startTime;
        
        console.log(`50次配置查询耗时: ${queryTime}ms (平均 ${(queryTime/50).toFixed(2)}ms/次)`);
        
        if (queryTime > 500) {
            console.warn('⚠️ 配置查询性能较慢，建议检查索引');
        } else {
            console.log('✅ 配置查询性能良好');
        }
        
        // 5. 验证数据类型和范围
        console.log('\n5️⃣ 验证数据类型和范围...');
        
        let validationErrors = 0;
        
        for (const config of configs) {
            // 检查数据类型
            if (typeof config.grade !== 'number' || config.grade < 1 || config.grade > 50) {
                console.error(`等级 ${config.grade} 超出范围 [1-50]`);
                validationErrors++;
            }
            
            if (typeof config.profit !== 'number' || config.profit <= 0) {
                console.error(`等级 ${config.grade} 利润值无效: ${config.profit}`);
                validationErrors++;
            }
            
            if (typeof config.capacity !== 'number' || config.capacity <= 0) {
                console.error(`等级 ${config.grade} 容量值无效: ${config.capacity}`);
                validationErrors++;
            }
            
            if (typeof config.production_interval !== 'number' || config.production_interval <= 0) {
                console.error(`等级 ${config.grade} 生产间隔无效: ${config.production_interval}`);
                validationErrors++;
            }
            
            if (typeof config.upgrade_cost !== 'number' || config.upgrade_cost <= 0) {
                console.error(`等级 ${config.grade} 升级费用无效: ${config.upgrade_cost}`);
                validationErrors++;
            }
        }
        
        if (validationErrors > 0) {
            throw new Error(`发现 ${validationErrors} 个数据验证错误`);
        }
        
        console.log('✅ 所有数据验证通过');
        
        // 6. 检查数据趋势
        console.log('\n6️⃣ 检查数据趋势...');
        
        // 检查利润和容量是否递增
        for (let i = 1; i < configs.length; i++) {
            const current = configs[i];
            const previous = configs[i-1];
            
            if (current.profit < previous.profit) {
                console.warn(`警告: 等级 ${current.grade} 利润 (${current.profit}) 小于等级 ${previous.grade} (${previous.profit})`);
            }
            
            if (current.capacity < previous.capacity) {
                console.warn(`警告: 等级 ${current.grade} 容量 (${current.capacity}) 小于等级 ${previous.grade} (${previous.capacity})`);
            }
        }
        
        // 检查生产间隔是否递减（速度递增）
        let speedIncreasing = true;
        for (let i = 1; i < configs.length; i++) {
            if (configs[i].production_interval > configs[i-1].production_interval) {
                speedIncreasing = false;
                break;
            }
        }
        
        if (speedIncreasing) {
            console.log('✅ 生产速度随等级递增');
        } else {
            console.log('ℹ️ 生产速度在某些等级保持不变或递减');
        }
        
        console.log('\n🎉 所有测试通过！流水线配置系统工作正常');
        
    } catch (error) {
        console.error('\n❌ 测试失败:', error.message);
        throw error;
    } finally {
        if (connection) {
            await connection.end();
        }
    }
}

// 执行测试
if (require.main === module) {
    testDeliveryLineConfigs()
        .then(() => {
            console.log('\n✅ 测试完成');
            process.exit(0);
        })
        .catch(error => {
            console.error('\n❌ 测试异常:', error);
            process.exit(1);
        });
}

module.exports = { testDeliveryLineConfigs };
