#!/usr/bin/env node

/**
 * 测试农场API（带认证）
 */

const jwt = require('jsonwebtoken');
const axios = require('axios');
require('dotenv').config();

async function testFarmAPIWithAuth() {
  console.log('🧪 测试农场API（带认证）\n');

  const baseUrl = 'http://localhost:3456/api';
  const jwtSecret = process.env.JWT_SECRET_Wallet;
  
  if (!jwtSecret) {
    console.error('❌ 缺少 JWT_SECRET_Wallet 环境变量');
    return;
  }

  try {
    // 1. 生成测试JWT token
    console.log('1. 生成测试JWT token...');
    
    const testPayload = {
      walletId: 1,
      walletAddress: 'test_wallet_address',
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + (60 * 60) // 1小时后过期
    };
    
    const token = jwt.sign(testPayload, jwtSecret);
    console.log(`✅ Token生成成功: ${token.substring(0, 50)}...`);

    // 2. 测试农场区块API
    console.log('\n2. 测试农场区块API...');
    
    const farmPlotsResponse = await axios.get(`${baseUrl}/farm/farm-plots`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    
    console.log(`✅ 农场区块API: ${farmPlotsResponse.status}`);
    
    if (farmPlotsResponse.data.ok) {
      const farmPlots = farmPlotsResponse.data.data.farmPlots;
      console.log(`📊 返回数据: ${farmPlots.length} 个农场区块`);
      
      if (farmPlots.length > 0) {
        const firstPlot = farmPlots[0];
        console.log('\n📦 第一个区块详情:');
        console.log(`   区块编号: ${firstPlot.plotNumber}`);
        console.log(`   等级: ${firstPlot.level}`);
        console.log(`   是否解锁: ${firstPlot.isUnlocked}`);
        console.log(`   牛数量: ${firstPlot.barnCount}`);
        console.log(`   牛奶产量: ${firstPlot.milkProduction}`);
        console.log(`   生产速度: ${firstPlot.productionSpeed}`);
        console.log(`   升级费用: ${firstPlot.upgradeCost}`);
        
        // 检查是否使用了新配置
        console.log('\n🔍 配置验证:');
        if (firstPlot.isUnlocked) {
          const expectedMilkProduction = 60.629; // 等级1的配置值
          const expectedSpeed = 100; // 等级1的配置值
          const expectedUpgradeCost = 28583; // 等级2的配置值
          
          const milkMatch = Math.abs(parseFloat(firstPlot.milkProduction) - expectedMilkProduction) < 0.001;
          const speedMatch = firstPlot.productionSpeed === expectedSpeed;
          const costMatch = Math.abs(parseFloat(firstPlot.upgradeCost) - expectedUpgradeCost) < 1;
          
          console.log(`   牛奶产量: ${milkMatch ? '✅' : '❌'} (期望: ${expectedMilkProduction}, 实际: ${firstPlot.milkProduction})`);
          console.log(`   生产速度: ${speedMatch ? '✅' : '❌'} (期望: ${expectedSpeed}, 实际: ${firstPlot.productionSpeed})`);
          console.log(`   升级费用: ${costMatch ? '✅' : '❌'} (期望: ${expectedUpgradeCost}, 实际: ${firstPlot.upgradeCost})`);
          
          if (milkMatch && speedMatch && costMatch) {
            console.log('\n🎉 新配置系统已成功集成！');
          } else {
            console.log('\n⚠️ 配置值不匹配，可能需要进一步检查');
          }
        }
        
        // 检查升级预览数据
        if (firstPlot.nextUpgradeGrowth && Object.keys(firstPlot.nextUpgradeGrowth).length > 0) {
          console.log('\n📈 升级预览数据:');
          console.log(`   ${JSON.stringify(firstPlot.nextUpgradeGrowth, null, 2)}`);
          console.log('✅ 升级预览功能正常');
        } else {
          console.log('\n⚠️ 升级预览数据为空或缺失');
        }
      }
    } else {
      console.log(`❌ API返回错误: ${farmPlotsResponse.data.message}`);
    }

    // 3. 测试农场配置API（对比）
    console.log('\n3. 测试农场配置API（对比）...');
    
    const configResponse = await axios.get(`${baseUrl}/admin/farm-config/current`);
    console.log(`✅ 配置API: ${configResponse.status}`);
    
    if (configResponse.data.ok) {
      const configs = configResponse.data.data.configs;
      const level1Config = configs.find(c => c.grade === 1);
      
      if (level1Config) {
        console.log('\n📋 等级1配置数据:');
        console.log(`   牛数量: ${level1Config.cow}`);
        console.log(`   牛奶产量: ${level1Config.milk}`);
        console.log(`   生产速度: ${level1Config.speed}`);
        console.log(`   升级费用: ${level1Config.cost}`);
      }
    }

  } catch (error) {
    if (error.response) {
      console.error(`❌ API错误: ${error.response.status} - ${error.response.data.message || error.response.data}`);
    } else {
      console.error('❌ 网络错误:', error.message);
    }
  }
}

// 主函数
async function main() {
  console.log('🚀 农场API认证测试开始');
  console.log('='.repeat(50));
  
  await testFarmAPIWithAuth();
  
  console.log('\n' + '='.repeat(50));
  console.log('📋 测试完成');
}

// 如果直接运行此脚本
if (require.main === module) {
  main().catch(error => {
    console.error('❌ 脚本执行失败:', error);
    process.exit(1);
  });
}

module.exports = { testFarmAPIWithAuth };
