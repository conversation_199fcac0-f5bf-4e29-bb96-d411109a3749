#!/usr/bin/env node

/**
 * 测试农场配置API脚本
 * 用于验证数据库数据和API响应
 */

const mysql = require('mysql2/promise');
const axios = require('axios');
require('dotenv').config();

async function testDatabaseData() {
  const dbConfig = {
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT) || 3669,
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASS || 'root',
    database: process.env.DB_NAME || 'wolf_fun_db'
  };

  let connection;

  try {
    console.log('🔍 测试数据库数据...\n');

    // 创建连接
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ 数据库连接成功');

    // 检查配置数据
    const [configRows] = await connection.execute(
      'SELECT COUNT(*) as total, COUNT(CASE WHEN isActive = 1 THEN 1 END) as active FROM farm_configs'
    );
    
    console.log(`📊 配置数据统计:`);
    console.log(`   总配置数: ${configRows[0].total}`);
    console.log(`   激活配置数: ${configRows[0].active}`);

    // 检查版本数据
    const [versionRows] = await connection.execute(
      'SELECT COUNT(*) as total, COUNT(CASE WHEN isActive = 1 THEN 1 END) as active FROM farm_config_versions'
    );
    
    console.log(`📋 版本数据统计:`);
    console.log(`   总版本数: ${versionRows[0].total}`);
    console.log(`   激活版本数: ${versionRows[0].active}`);

    // 获取激活版本详情
    const [activeVersions] = await connection.execute(
      'SELECT version, name, isActive, configCount FROM farm_config_versions WHERE isActive = 1'
    );
    
    if (activeVersions.length > 0) {
      console.log(`\n🎯 激活版本详情:`);
      activeVersions.forEach(v => {
        console.log(`   版本: ${v.version}`);
        console.log(`   名称: ${v.name}`);
        console.log(`   配置数量: ${v.configCount}`);
      });
    } else {
      console.log(`\n❌ 没有找到激活的版本`);
    }

    // 获取部分激活配置数据
    const [activeConfigs] = await connection.execute(
      'SELECT grade, production, cow, speed, milk, cost, offline FROM farm_configs WHERE isActive = 1 ORDER BY grade LIMIT 5'
    );
    
    if (activeConfigs.length > 0) {
      console.log(`\n📦 激活配置样本 (前5条):`);
      activeConfigs.forEach(config => {
        console.log(`   等级${config.grade}: 产量=${config.production}, 牛=${config.cow}, 速度=${config.speed}, 牛奶=${config.milk}, 费用=${config.cost}`);
      });
    } else {
      console.log(`\n❌ 没有找到激活的配置数据`);
    }

    return {
      totalConfigs: configRows[0].total,
      activeConfigs: configRows[0].active,
      totalVersions: versionRows[0].total,
      activeVersions: versionRows[0].active
    };

  } catch (error) {
    console.error('❌ 数据库测试失败:', error.message);
    return null;
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

async function testAPI() {
  console.log('\n🌐 测试API响应...\n');

  const apiUrl = 'http://localhost:3456/api/admin/farm-config/current';
  
  try {
    console.log(`📡 请求URL: ${apiUrl}`);
    
    const response = await axios.get(apiUrl, {
      timeout: 5000,
      headers: {
        'Content-Type': 'application/json'
      }
    });

    console.log(`✅ API响应状态: ${response.status}`);
    console.log(`📊 响应数据:`);
    
    if (response.data) {
      const data = response.data;
      console.log(`   成功: ${data.ok}`);
      console.log(`   消息: ${data.message}`);
      
      if (data.data) {
        console.log(`   版本: ${data.data.version}`);
        console.log(`   版本名称: ${data.data.versionName}`);
        console.log(`   配置数量: ${data.data.totalConfigs}`);
        
        if (data.data.configs && data.data.configs.length > 0) {
          console.log(`   配置样本 (前3条):`);
          data.data.configs.slice(0, 3).forEach(config => {
            console.log(`     等级${config.grade}: 产量=${config.production}, 牛=${config.cow}, 速度=${config.speed}`);
          });
        } else {
          console.log(`   ❌ 配置数据为空`);
        }
      } else {
        console.log(`   ❌ 响应中没有data字段`);
      }
    } else {
      console.log(`   ❌ 响应数据为空`);
    }

    return response.data;

  } catch (error) {
    if (error.code === 'ECONNREFUSED') {
      console.error('❌ API测试失败: 连接被拒绝 (服务器可能未启动)');
      console.log('💡 请确保应用正在运行: npm run dev');
    } else if (error.response) {
      console.error(`❌ API测试失败: HTTP ${error.response.status}`);
      console.error(`   响应: ${JSON.stringify(error.response.data, null, 2)}`);
    } else {
      console.error('❌ API测试失败:', error.message);
    }
    return null;
  }
}

async function main() {
  console.log('🧪 农场配置系统测试\n');
  console.log('='.repeat(50));

  // 1. 测试数据库数据
  const dbResult = await testDatabaseData();
  
  console.log('\n' + '='.repeat(50));
  
  // 2. 测试API
  const apiResult = await testAPI();
  
  console.log('\n' + '='.repeat(50));
  console.log('📋 测试总结:');
  
  if (dbResult) {
    console.log(`✅ 数据库: ${dbResult.activeConfigs}/${dbResult.totalConfigs} 配置激活`);
    console.log(`✅ 版本: ${dbResult.activeVersions}/${dbResult.totalVersions} 版本激活`);
  } else {
    console.log(`❌ 数据库: 测试失败`);
  }
  
  if (apiResult) {
    console.log(`✅ API: 响应正常`);
  } else {
    console.log(`❌ API: 测试失败`);
  }
  
  console.log('\n💡 建议:');
  if (!dbResult || dbResult.activeConfigs === 0) {
    console.log('- 运行初始化脚本: node scripts/init-farm-config-simple.js');
  }
  if (!apiResult) {
    console.log('- 启动应用: npm run dev');
    console.log('- 检查端口: 确保应用运行在 3456 端口');
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main().catch(error => {
    console.error('❌ 测试执行失败:', error);
    process.exit(1);
  });
}

module.exports = { testDatabaseData, testAPI };
