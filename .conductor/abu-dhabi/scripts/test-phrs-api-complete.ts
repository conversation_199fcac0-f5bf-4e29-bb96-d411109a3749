import axios from 'axios';
import { sequelize } from '../src/config/db';
import { UserWallet, FarmPlot, DeliveryLine, User } from '../src/models';

const API_BASE_URL = 'http://localhost:3000/api';

// 模拟钱包地址和签名（开发环境下可以使用假签名）
const TEST_WALLET_ADDRESS = '******************************************';
const FAKE_SIGNATURE = '0xfakesignature';

async function getAuthToken(): Promise<string | null> {
  try {
    console.log('🔑 获取认证token...');
    
    // 1. 获取nonce
    console.log('   1. 获取nonce...');
    const nonceResponse = await axios.post(`${API_BASE_URL}/web3-auth/nonce`, {
      walletAddress: TEST_WALLET_ADDRESS
    });
    
    const { nonce, message } = nonceResponse.data.data;
    console.log(`   ✅ 获取nonce成功: ${nonce}`);
    
    // 2. 登录获取token
    console.log('   2. 登录获取token...');
    const loginResponse = await axios.post(`${API_BASE_URL}/web3-auth/login`, {
      walletAddress: TEST_WALLET_ADDRESS,
      signature: FAKE_SIGNATURE,
      message: message
    });
    
    const token = loginResponse.data.data.token;
    console.log(`   ✅ 获取token成功: ${token.substring(0, 20)}...`);
    
    return token;
  } catch (error: any) {
    console.log('   ❌ 获取token失败:', error.response?.data || error.message);
    return null;
  }
}

async function testPhrsAPIWithAuth() {
  try {
    console.log('🚀 完整测试PHRS支付API...\n');

    // 1. 创建测试数据
    console.log('1. 创建测试数据...');
    
    // 创建用户
    let user = await User.findOne({ where: { walletAddress: TEST_WALLET_ADDRESS } });
    if (!user) {
      user = await User.create({
        username: 'testuser',
        walletAddress: TEST_WALLET_ADDRESS
      });
    }

    // 创建钱包
    let wallet = await UserWallet.findOne({ where: { walletAddress: TEST_WALLET_ADDRESS } });
    if (!wallet) {
      wallet = await UserWallet.create({
        userId: user.id,
        walletAddress: TEST_WALLET_ADDRESS,
        phrsBalance: '15000',
        gem: '0',
        milk: 0,
        phrsWalletAddress: TEST_WALLET_ADDRESS,
        lastPhrsUpdateTime: new Date()
      });

      // 创建农场区和出货线
      await FarmPlot.create({
        walletId: wallet.id,
        plotNumber: 1,
        level: 1,
        isUnlocked: true,
        barnCount: 1,
        milkProduction: 1,
        productionSpeed: 5,
        unlockCost: 0,
        upgradeCost: 200,
        lastProductionTime: new Date(),
        accumulatedMilk: 0
      });

      await DeliveryLine.create({
        walletId: wallet.id,
        level: 1,
        deliverySpeed: 1.0,
        blockUnit: 10,
        blockPrice: 1.0,
        upgradeCost: 500,
        lastDeliveryTime: new Date(),
        pendingMilk: 0,
        pendingBlocks: 0
      });
    }

    console.log(`   ✅ 测试数据准备完成，钱包ID: ${wallet.id}`);

    // 2. 获取认证token
    const token = await getAuthToken();
    if (!token) {
      console.log('❌ 无法获取认证token，测试终止');
      return;
    }

    const authHeaders = {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    };

    // 3. 测试获取商品列表
    console.log('\n3. 测试获取商品列表...');
    try {
      const productsResponse = await axios.get(`${API_BASE_URL}/phrs-payment/products`, {
        headers: authHeaders
      });
      console.log('   ✅ 获取商品列表成功');
      console.log(`   找到 ${productsResponse.data.data.products.length} 个商品`);
      
      // 显示前3个商品
      productsResponse.data.data.products.slice(0, 3).forEach((product: any, index: number) => {
        console.log(`   ${index + 1}. ${product.name}: ${product.phrsPrice} PHRS (ID: ${product.id})`);
      });
    } catch (error: any) {
      console.log('   ❌ 获取商品列表失败:', error.response?.data || error.message);
    }

    // 4. 测试获取余额
    console.log('\n4. 测试获取余额...');
    try {
      const balanceResponse = await axios.get(`${API_BASE_URL}/phrs-payment/balance`, {
        headers: authHeaders
      });
      console.log('   ✅ 获取余额成功');
      console.log(`   PHRS余额: ${balanceResponse.data.data.phrsBalance}`);
      console.log(`   购买历史: ${balanceResponse.data.data.recentPurchases.length} 条记录`);
    } catch (error: any) {
      console.log('   ❌ 获取余额失败:', error.response?.data || error.message);
    }

    // 5. 测试购买接口 - 错误请求
    console.log('\n5. 测试购买接口 - 错误请求...');
    const errorTestCases = [
      { name: '缺少productId', body: {} },
      { name: 'productId为字符串', body: { productId: "invalid" } },
      { name: 'productId为0', body: { productId: 0 } },
      { name: '不存在的productId', body: { productId: 99999 } }
    ];

    for (const testCase of errorTestCases) {
      console.log(`   测试: ${testCase.name}`);
      try {
        const response = await axios.post(`${API_BASE_URL}/phrs-payment/purchase`, testCase.body, {
          headers: authHeaders
        });
        console.log(`     ❌ 应该失败但成功了: ${response.status}`);
      } catch (error: any) {
        if (error.response) {
          console.log(`     ✅ 正确返回错误: ${error.response.status} - ${error.response.data?.message || 'No message'}`);
        } else {
          console.log(`     ❌ 网络错误: ${error.message}`);
        }
      }
    }

    // 6. 测试购买接口 - 正确请求
    console.log('\n6. 测试购买接口 - 正确请求...');
    try {
      // 先获取一个有效的商品ID
      const productsResponse = await axios.get(`${API_BASE_URL}/phrs-payment/products`, {
        headers: authHeaders
      });
      
      const timeWarpProduct = productsResponse.data.data.products.find((p: any) => 
        p.type === 'time_warp' && p.duration === 1
      );

      if (timeWarpProduct) {
        console.log(`   尝试购买: ${timeWarpProduct.name} (ID: ${timeWarpProduct.id})`);
        
        const purchaseResponse = await axios.post(`${API_BASE_URL}/phrs-payment/purchase`, {
          productId: timeWarpProduct.id
        }, {
          headers: authHeaders
        });
        
        console.log('   ✅ 购买成功!');
        console.log(`   购买ID: ${purchaseResponse.data.data.purchaseId}`);
        console.log(`   支付金额: ${purchaseResponse.data.data.phrsPaid} PHRS`);
        console.log(`   剩余余额: ${purchaseResponse.data.data.remainingBalance} PHRS`);
      } else {
        console.log('   ❌ 未找到合适的测试商品');
      }
    } catch (error: any) {
      if (error.response) {
        console.log(`   ❌ 购买失败: ${error.response.status}`);
        console.log(`   错误信息: ${JSON.stringify(error.response.data, null, 2)}`);
      } else {
        console.log(`   ❌ 网络错误: ${error.message}`);
      }
    }

    console.log('\n✅ 完整API测试完成!');

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
  } finally {
    await sequelize.close();
  }
}

// 检查服务器是否运行
async function checkServer() {
  try {
    await axios.get(`${API_BASE_URL}/health`);
    return true;
  } catch (error) {
    return false;
  }
}

async function main() {
  const serverRunning = await checkServer();
  if (!serverRunning) {
    console.log('❌ 服务器未运行，请先启动服务器：npm run dev');
    console.log('   然后运行：npx ts-node scripts/test-phrs-api-complete.ts');
    return;
  }

  await testPhrsAPIWithAuth();
}

main();
