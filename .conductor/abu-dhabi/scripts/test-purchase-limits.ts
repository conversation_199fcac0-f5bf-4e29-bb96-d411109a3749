import { sequelize } from '../src/config/db';
import { UserWallet, IapProduct, IapPurchase, FarmPlot, DeliveryLine, User } from '../src/models';
import { PhrsPaymentController } from '../src/controllers/phrsPaymentController';

const phrsController = new PhrsPaymentController();

async function testPurchaseLimits() {
  try {
    console.log('🚀 测试购买限制功能...\n');

    // 1. 创建测试用户和钱包
    console.log('1. 创建测试数据...');
    
    let user = await User.findOne({ where: { telegramId: 'limittest123' } });
    if (!user) {
      user = await User.create({
        telegramId: 'limittest123',
        username: 'limittest',
        authDate: Math.floor(Date.now() / 1000),
        hash: 'testhash',
        hasFollowedChannel: false
      });
    }

    let wallet = await UserWallet.findOne({ where: { walletAddress: '0xlimitest123' } });
    if (!wallet) {
      wallet = await UserWallet.create({
        userId: user.id,
        walletAddress: '0xlimitest123',
        phrsBalance: '10000', // 给足够的余额
        gem: '0',
        milk: 0,
        phrsWalletAddress: '0xlimitest123',
        lastPhrsUpdateTime: new Date()
      });

      // 创建农场区和出货线
      await FarmPlot.create({
        walletId: wallet.id,
        plotNumber: 1,
        level: 1,
        isUnlocked: true,
        barnCount: 1,
        milkProduction: 1,
        productionSpeed: 5,
        unlockCost: 0,
        upgradeCost: 200,
        lastProductionTime: new Date(),
        accumulatedMilk: 0
      });

      await DeliveryLine.create({
        walletId: wallet.id,
        level: 1,
        deliverySpeed: 1.0,
        blockUnit: 10,
        blockPrice: 1.0,
        upgradeCost: 500,
        lastDeliveryTime: new Date(),
        pendingMilk: 0,
        pendingBlocks: 0
      });
    } else {
      // 确保有足够余额
      await wallet.update({ phrsBalance: '10000' });
    }

    console.log(`✅ 测试数据准备完成，钱包ID: ${wallet.id}, PHRS余额: ${wallet.phrsBalance}\n`);

    // 2. 测试时间跳跃每日限制
    console.log('2. 测试时间跳跃每日限制...');
    const timeWarpProduct = await IapProduct.findOne({
      where: { type: 'time_warp', duration: 1 }
    });

    if (timeWarpProduct) {
      console.log(`商品: ${timeWarpProduct.name}, 每日限制: ${timeWarpProduct.dailyLimit}`);

      // 第一次购买
      console.log('   第一次购买...');
      const firstReq = {
        user: { walletId: wallet.id },
        body: { productId: timeWarpProduct.id },
        t: (key: string) => key
      };

      const mockRes1: any = {
        status: (code: number) => ({
          json: (data: any) => {
            if (code === 200) {
              console.log(`   ✅ 第一次购买成功`);
            } else {
              console.log(`   ❌ 第一次购买失败: ${data.message}`);
            }
            return mockRes1;
          }
        }),
        json: (data: any) => {
          console.log(`   ✅ 第一次购买成功`);
          return mockRes1;
        }
      };

      await phrsController.purchaseWithPhrs(firstReq as any, mockRes1);

      // 第二次购买（应该失败）
      console.log('   第二次购买（应该失败）...');
      const secondReq = {
        user: { walletId: wallet.id },
        body: { productId: timeWarpProduct.id },
        t: (key: string) => key
      };

      const mockRes2: any = {
        status: (code: number) => ({
          json: (data: any) => {
            if (code === 400 && data.error === 'PURCHASE_LIMIT_EXCEEDED') {
              console.log(`   ✅ 第二次购买被正确拦截: ${data.message}`);
            } else {
              console.log(`   ❌ 第二次购买应该失败但成功了: ${JSON.stringify(data)}`);
            }
            return mockRes2;
          }
        }),
        json: (data: any) => {
          console.log(`   ❌ 第二次购买应该失败但成功了: ${JSON.stringify(data)}`);
          return mockRes2;
        }
      };

      await phrsController.purchaseWithPhrs(secondReq as any, mockRes2);
    }

    // 3. 测试特殊套餐账号限制
    console.log('\n3. 测试特殊套餐账号限制...');
    const specialOffer = await IapProduct.findOne({
      where: { type: 'special_offer', productId: 'special_offer_bundle' }
    });

    if (specialOffer) {
      console.log(`商品: ${specialOffer.name}, 每日限制: ${specialOffer.dailyLimit}, 账号限制: ${specialOffer.accountLimit}`);

      // 第一次购买
      console.log('   第一次购买特殊套餐...');
      const firstReq = {
        user: { walletId: wallet.id },
        body: { productId: specialOffer.id },
        t: (key: string) => key
      };

      const mockRes1: any = {
        status: (code: number) => ({
          json: (data: any) => {
            if (code === 200) {
              console.log(`   ✅ 第一次购买特殊套餐成功`);
            } else {
              console.log(`   ❌ 第一次购买特殊套餐失败: ${data.message}`);
            }
            return mockRes1;
          }
        }),
        json: (data: any) => {
          console.log(`   ✅ 第一次购买特殊套餐成功`);
          return mockRes1;
        }
      };

      await phrsController.purchaseWithPhrs(firstReq as any, mockRes1);

      // 第二次购买（应该失败，账号限制）
      console.log('   第二次购买特殊套餐（应该失败）...');
      const secondReq = {
        user: { walletId: wallet.id },
        body: { productId: specialOffer.id },
        t: (key: string) => key
      };

      const mockRes2: any = {
        status: (code: number) => ({
          json: (data: any) => {
            if (code === 400 && data.error === 'PURCHASE_LIMIT_EXCEEDED') {
              console.log(`   ✅ 第二次购买特殊套餐被正确拦截: ${data.message}`);
            } else {
              console.log(`   ❌ 第二次购买特殊套餐应该失败但成功了: ${JSON.stringify(data)}`);
            }
            return mockRes2;
          }
        }),
        json: (data: any) => {
          console.log(`   ❌ 第二次购买特殊套餐应该失败但成功了: ${JSON.stringify(data)}`);
          return mockRes2;
        }
      };

      await phrsController.purchaseWithPhrs(secondReq as any, mockRes2);
    }

    // 4. 显示购买历史
    console.log('\n4. 购买历史总结...');
    const purchases = await IapPurchase.findAll({
      where: { walletId: wallet.id, paymentMethod: 'phrs' },
      include: [{ model: IapProduct }],
      order: [['createdAt', 'DESC']]
    });

    console.log(`总购买次数: ${purchases.length}`);
    purchases.forEach((purchase, index) => {
      const product = (purchase as any).IapProduct;
      console.log(`   ${index + 1}. ${product?.name}: ${purchase.amount} PHRS, 状态: ${purchase.status}`);
    });

    console.log('\n✅ 购买限制测试完成！');

  } catch (error) {
    console.error('❌ 测试失败:', error);
  } finally {
    await sequelize.close();
  }
}

testPurchaseLimits();
