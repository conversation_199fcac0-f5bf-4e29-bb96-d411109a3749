import { sequelize } from '../src/config/db';
import { UserWallet, IapProduct, IapPurchase, TimeWarpHistory, FarmPlot, DeliveryLine, Booster } from '../src/models';
import { PhrsPaymentController } from '../src/controllers/phrsPaymentController';
import BigNumber from 'bignumber.js';

const phrsController = new PhrsPaymentController();

async function testSpecialOffer() {
  try {
    console.log('🚀 测试特殊套餐购买功能...\n');

    // 1. 创建测试用户钱包
    console.log('1. 创建测试用户钱包...');
    const testWallet = await UserWallet.create({
      userId: 1,
      walletAddress: '0xspecialtest123456789',
      phrsBalance: '25000', // 给用户25000 PHRS余额（足够购买特殊套餐）
      gem: '0',
      milk: 0,
      phrsWalletAddress: '0xphrsspecialtest123456789',
      lastPhrsUpdateTime: new Date()
    });
    console.log(`✅ 测试钱包创建成功，ID: ${testWallet.id}, PHRS余额: ${testWallet.phrsBalance}`);

    // 为用户创建农场区和出货线
    console.log('   创建农场区和出货线...');
    await FarmPlot.create({
      walletId: testWallet.id,
      plotNumber: 1,
      level: 1,
      isUnlocked: true,
      barnCount: 1,
      milkProduction: 1,
      productionSpeed: 5,
      unlockCost: 0,
      upgradeCost: 200,
      lastProductionTime: new Date(),
      accumulatedMilk: 0
    });

    await DeliveryLine.create({
      walletId: testWallet.id,
      level: 1,
      deliverySpeed: 1.0,
      blockUnit: 10,
      blockPrice: 1.0,
      upgradeCost: 500,
      lastDeliveryTime: new Date(),
      pendingMilk: 0,
      pendingBlocks: 0
    });
    console.log('   ✅ 农场区和出货线创建完成\n');

    // 2. 获取特殊套餐
    console.log('2. 获取特殊套餐...');
    const specialOffer = await IapProduct.findOne({
      where: { productId: 'special_offer_bundle' }
    });

    if (!specialOffer) {
      console.log('❌ 特殊套餐不存在');
      return;
    }

    console.log(`✅ 找到特殊套餐: ${specialOffer.name}`);
    console.log(`   价格: ${specialOffer.pricePhrs} PHRS`);
    console.log(`   配置:`, JSON.stringify(specialOffer.config, null, 2));
    console.log('');

    // 3. 测试购买特殊套餐
    console.log('3. 测试购买特殊套餐...');
    const mockReq: any = {
      user: { walletId: testWallet.id },
      body: { productId: specialOffer.id },
      t: (key: string) => key
    };
    const mockRes: any = {
      status: (code: number) => ({ json: (data: any) => console.log(`Status ${code}:`, data) }),
      json: (data: any) => console.log('Response:', data)
    };

    // 检查购买前的状态
    const beforeWallet = await UserWallet.findByPk(testWallet.id);
    const beforeGems = new BigNumber(beforeWallet?.gem?.toString() || '0');
    console.log(`   购买前 - PHRS余额: ${beforeWallet?.phrsBalance}, GEM: ${beforeGems.toFixed(3)}`);

    // 执行购买
    await phrsController.purchaseWithPhrs(mockReq, mockRes);

    // 检查购买后的状态
    const afterWallet = await UserWallet.findByPk(testWallet.id);
    const afterGems = new BigNumber(afterWallet?.gem?.toString() || '0');
    const gemsEarned = afterGems.minus(beforeGems);
    console.log(`   购买后 - PHRS余额: ${afterWallet?.phrsBalance}, GEM: ${afterGems.toFixed(3)}, 获得GEM: ${gemsEarned.toFixed(3)}`);

    // 4. 检查时间跳跃历史
    console.log('\n4. 检查时间跳跃历史...');
    const timeWarpHistories = await TimeWarpHistory.findAll({
      where: { walletId: testWallet.id },
      order: [['createdAt', 'DESC']]
    });

    console.log(`   找到 ${timeWarpHistories.length} 条时间跳跃记录:`);
    timeWarpHistories.forEach((history, index) => {
      console.log(`   记录 ${index + 1}: ${history.hours}小时, 获得GEM: ${history.gemsEarned}, 生产牛奶: ${history.milkProduced}, 处理牛奶: ${history.milkProcessed}`);
    });

    // 5. 检查速度加成道具
    console.log('\n5. 检查速度加成道具...');
    const boosters = await Booster.findAll({
      where: { walletId: testWallet.id, type: 'speed_boost' }
    });

    console.log(`   找到 ${boosters.length} 个速度加成道具:`);
    boosters.forEach((booster, index) => {
      console.log(`   道具 ${index + 1}: ${booster.multiplier}倍速度, 持续${booster.duration}小时, 数量: ${booster.quantity}`);
    });

    // 6. 测试重复购买（应该失败，因为账号限购1次）
    console.log('\n6. 测试重复购买特殊套餐（应该失败）...');
    await phrsController.purchaseWithPhrs(mockReq, mockRes);

    // 7. 显示最终状态
    console.log('\n7. 最终状态总结...');
    const finalWallet = await UserWallet.findByPk(testWallet.id);
    const purchases = await IapPurchase.findAll({
      where: { walletId: testWallet.id, paymentMethod: 'phrs' },
      include: [{ model: IapProduct }]
    });

    console.log(`   最终PHRS余额: ${finalWallet?.phrsBalance}`);
    console.log(`   最终GEM余额: ${finalWallet?.gem}`);
    console.log(`   总购买次数: ${purchases.length}`);
    purchases.forEach(purchase => {
      console.log(`   - ${(purchase as any).IapProduct?.name}: ${purchase.amount} PHRS, 状态: ${purchase.status}`);
    });

    console.log('\n✅ 特殊套餐购买功能测试完成！');

  } catch (error) {
    console.error('❌ 测试失败:', error);
  } finally {
    await sequelize.close();
  }
}

testSpecialOffer();
