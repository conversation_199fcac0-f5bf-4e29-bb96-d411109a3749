import { sequelize } from '../src/config/db';
import { UserWallet, IapProduct, FarmPlot, DeliveryLine, User } from '../src/models';
import { PhrsPaymentController } from '../src/controllers/phrsPaymentController';

const phrsController = new PhrsPaymentController();

async function testTimeWarpResponse() {
  try {
    console.log('🚀 测试时间跳跃购买响应格式...\n');

    // 1. 创建测试用户和钱包
    console.log('1. 创建测试数据...');
    
    let user = await User.findOne({ where: { telegramId: 'timewarptest123' } });
    if (!user) {
      user = await User.create({
        telegramId: 'timewarptest123',
        username: 'timewarptest',
        authDate: Math.floor(Date.now() / 1000),
        hash: 'testhash',
        hasFollowedChannel: false
      });
    }

    let wallet = await UserWallet.findOne({ where: { walletAddress: '0xtimewarptest123' } });
    if (!wallet) {
      wallet = await UserWallet.create({
        userId: user.id,
        walletAddress: '0xtimewarptest123',
        phrsBalance: '5000',
        gem: '100', // 初始GEM
        milk: 0,
        phrsWalletAddress: '0xtimewarptest123',
        lastPhrsUpdateTime: new Date()
      });

      // 创建农场区和出货线
      await FarmPlot.create({
        walletId: wallet.id,
        plotNumber: 1,
        level: 2, // 升级到2级，产量更高
        isUnlocked: true,
        barnCount: 2,
        milkProduction: 2,
        productionSpeed: 4,
        unlockCost: 0,
        upgradeCost: 400,
        lastProductionTime: new Date(),
        accumulatedMilk: 0
      });

      await DeliveryLine.create({
        walletId: wallet.id,
        level: 2, // 升级到2级，处理能力更强
        deliverySpeed: 0.8,
        blockUnit: 15,
        blockPrice: 1.5,
        upgradeCost: 1000,
        lastDeliveryTime: new Date(),
        pendingMilk: 0,
        pendingBlocks: 0
      });
    } else {
      // 确保有足够余额和初始GEM
      await wallet.update({ phrsBalance: '5000', gem: '100' });
    }

    console.log(`✅ 测试数据准备完成，钱包ID: ${wallet.id}, 初始GEM: ${wallet.gem}\n`);

    // 2. 测试不同时长的时间跳跃
    const timeWarpTests = [
      { name: 'Time Warp 1hr', duration: 1 },
      { name: 'Time Warp 6hr', duration: 6 },
      { name: 'Time Warp 24hr', duration: 24 }
    ];

    for (const test of timeWarpTests) {
      console.log(`测试: ${test.name}`);
      
      const product = await IapProduct.findOne({
        where: { type: 'time_warp', duration: test.duration }
      });

      if (!product) {
        console.log(`   ❌ 未找到${test.name}商品`);
        continue;
      }

      // 记录购买前的GEM
      const beforeWallet = await UserWallet.findByPk(wallet.id);
      const beforeGems = parseFloat(beforeWallet?.gem?.toString() || '0');

      const mockReq = {
        user: { walletId: wallet.id },
        body: { productId: product.id },
        t: (key: string) => key
      };

      let purchaseResponse: any = null;
      const mockRes: any = {
        status: (code: number) => ({
          json: (data: any) => {
            purchaseResponse = { status: code, data };
            return mockRes;
          }
        }),
        json: (data: any) => {
          purchaseResponse = { status: 200, data };
          return mockRes;
        }
      };

      await phrsController.purchaseWithPhrs(mockReq as any, mockRes);

      if (purchaseResponse && purchaseResponse.status === 200) {
        const data = purchaseResponse.data.data;
        console.log(`   ✅ 购买成功: ${data.productName}`);
        console.log(`   💰 支付: ${data.phrsPaid} PHRS`);
        console.log(`   💎 剩余余额: ${data.remainingBalance} PHRS`);
        
        if (data.rewards) {
          console.log(`   🎁 时间跳跃收益:`);
          console.log(`      - 获得GEM: ${data.rewards.gemsEarned}`);
          console.log(`      - 生产牛奶: ${data.rewards.milkProduced}`);
          console.log(`      - 处理牛奶: ${data.rewards.milkProcessed}`);
          console.log(`      - 农场产能: ${data.rewards.farmProductionPerSecond}/秒`);
          console.log(`      - 出货能力: ${data.rewards.deliveryProcessingPerSecond}/秒`);
          console.log(`      - VIP状态: ${data.rewards.hasVip ? '是' : '否'}`);
          console.log(`      - 速度加成: ${data.rewards.hasSpeedBoost ? `${data.rewards.speedBoostMultiplier}倍` : '无'}`);
        }
        
        if (data.summary) {
          console.log(`   📊 收益总结: ${data.summary}`);
        }

        // 验证GEM确实增加了
        const afterWallet = await UserWallet.findByPk(wallet.id);
        const afterGems = parseFloat(afterWallet?.gem?.toString() || '0');
        const gemsGained = afterGems - beforeGems;
        console.log(`   ✅ 实际GEM增加: ${gemsGained.toFixed(3)} (预期: ${data.rewards?.gemsEarned || 0})`);
        
        if (Math.abs(gemsGained - (data.rewards?.gemsEarned || 0)) < 0.001) {
          console.log(`   ✅ GEM增加量验证通过`);
        } else {
          console.log(`   ❌ GEM增加量不匹配`);
        }
      } else {
        console.log(`   ❌ 购买失败:`, purchaseResponse);
      }
      
      console.log('');
    }

    // 3. 测试特殊套餐的时间跳跃
    console.log('3. 测试特殊套餐时间跳跃...');
    const specialOffer = await IapProduct.findOne({
      where: { type: 'special_offer', productId: 'special_offer_bundle' }
    });

    if (specialOffer) {
      // 记录购买前的GEM
      const beforeWallet = await UserWallet.findByPk(wallet.id);
      const beforeGems = parseFloat(beforeWallet?.gem?.toString() || '0');

      const mockReq = {
        user: { walletId: wallet.id },
        body: { productId: specialOffer.id },
        t: (key: string) => key
      };

      let purchaseResponse: any = null;
      const mockRes: any = {
        status: (code: number) => ({
          json: (data: any) => {
            purchaseResponse = { status: code, data };
            return mockRes;
          }
        }),
        json: (data: any) => {
          purchaseResponse = { status: 200, data };
          return mockRes;
        }
      };

      await phrsController.purchaseWithPhrs(mockReq as any, mockRes);

      if (purchaseResponse && purchaseResponse.status === 200) {
        const data = purchaseResponse.data.data;
        console.log(`   ✅ 特殊套餐购买成功: ${data.productName}`);
        console.log(`   💰 支付: ${data.phrsPaid} PHRS`);
        
        if (data.rewards) {
          console.log(`   🎁 时间跳跃收益 (48小时):`);
          console.log(`      - 获得GEM: ${data.rewards.gemsEarned}`);
          console.log(`      - 生产牛奶: ${data.rewards.milkProduced}`);
          console.log(`      - 处理牛奶: ${data.rewards.milkProcessed}`);
        }
        
        if (data.summary) {
          console.log(`   📊 收益总结: ${data.summary}`);
        }

        // 验证GEM确实增加了
        const afterWallet = await UserWallet.findByPk(wallet.id);
        const afterGems = parseFloat(afterWallet?.gem?.toString() || '0');
        const gemsGained = afterGems - beforeGems;
        console.log(`   ✅ 实际GEM增加: ${gemsGained.toFixed(3)} (预期: ${data.rewards?.gemsEarned || 0})`);
      } else {
        console.log(`   ❌ 特殊套餐购买失败:`, purchaseResponse);
      }
    }

    console.log('\n✅ 时间跳跃响应格式测试完成！');

  } catch (error) {
    console.error('❌ 测试失败:', error);
  } finally {
    await sequelize.close();
  }
}

testTimeWarpResponse();
