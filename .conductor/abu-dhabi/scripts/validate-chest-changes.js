// 验证宝箱奖励逻辑修改的完整性测试
// 运行命令: node scripts/validate-chest-changes.js

const fs = require('fs');
const path = require('path');

// 验证配置
const EXPECTED_CONFIG = {
  probabilities: {
    level1: 40,
    level2: 30,
    level3: 20,
    level4: 10
  },
  rewards: {
    level1: { fragment_green: 16, gem: 389712 },
    level2: { fragment_blue: 4, gem: 519616 },
    level3: { fragment_purple: 1, gem: 779425 },
    level4: { fragment_gold: 1, gem: 1558850 }
  }
};

// 需要检查的文件列表
const FILES_TO_CHECK = [
  'src/services/chestService.ts',
  'src/controllers/testChestController.ts',
  'scripts/test-chest-reward.js',
  'src/i18n/locales/zh.ts',
  'src/i18n/locales/en.ts',
  'src/routes/rewards.ts'
];

function validateFile(filePath, expectedPatterns) {
  console.log(`\n🔍 检查文件: ${filePath}`);
  
  if (!fs.existsSync(filePath)) {
    console.log(`❌ 文件不存在: ${filePath}`);
    return false;
  }
  
  const content = fs.readFileSync(filePath, 'utf8');
  let allPassed = true;
  
  expectedPatterns.forEach(pattern => {
    const found = pattern.regex.test(content);
    if (found) {
      console.log(`✅ ${pattern.description}`);
    } else {
      console.log(`❌ ${pattern.description}`);
      allPassed = false;
    }
  });
  
  return allPassed;
}

function runValidation() {
  console.log('🚀 开始验证宝箱奖励逻辑修改...\n');
  
  let overallSuccess = true;
  
  // 1. 验证主要的宝箱服务文件
  const chestServicePatterns = [
    {
      description: '1级宝箱概率为40%',
      regex: /if\s*\(\s*random\s*<\s*40\s*\)/
    },
    {
      description: '2级宝箱概率为30% (累计70%)',
      regex: /else\s+if\s*\(\s*random\s*<\s*70\s*\)/
    },
    {
      description: '3级宝箱概率为20% (累计90%)',
      regex: /else\s+if\s*\(\s*random\s*<\s*90\s*\)/
    },
    {
      description: '1级宝箱绿色碎片16个',
      regex: /type:\s*['"]fragment_green['"][\s\S]*?amount:\s*16/
    },
    {
      description: '1级宝箱金牛币389712',
      regex: /type:\s*['"]diamond['"][\s\S]*?amount:\s*389712/
    },
    {
      description: '2级宝箱蓝色碎片4个',
      regex: /type:\s*['"]fragment_blue['"][\s\S]*?amount:\s*4/
    },
    {
      description: '2级宝箱金牛币519616',
      regex: /type:\s*['"]diamond['"][\s\S]*?amount:\s*519616/
    },
    {
      description: '3级宝箱紫色碎片1个',
      regex: /type:\s*['"]fragment_purple['"][\s\S]*?amount:\s*1/
    },
    {
      description: '3级宝箱金牛币779425',
      regex: /type:\s*['"]diamond['"][\s\S]*?amount:\s*779425/
    },
    {
      description: '4级宝箱金色碎片1个',
      regex: /type:\s*['"]fragment_gold['"][\s\S]*?amount:\s*1/
    },
    {
      description: '4级宝箱金牛币1558850',
      regex: /type:\s*['"]diamond['"][\s\S]*?amount:\s*1558850/
    }
  ];
  
  if (!validateFile('src/services/chestService.ts', chestServicePatterns)) {
    overallSuccess = false;
  }
  
  // 2. 验证测试控制器
  const testControllerPatterns = [
    {
      description: '测试控制器包含新的奖励配置',
      regex: /type:\s*['"]fragment_green['"][\s\S]*?amount:\s*16/
    },
    {
      description: '测试控制器包含新的金牛币配置',
      regex: /type:\s*['"]diamond['"][\s\S]*?amount:\s*389712/
    }
  ];
  
  if (!validateFile('src/controllers/testChestController.ts', testControllerPatterns)) {
    overallSuccess = false;
  }
  
  // 3. 验证国际化文件
  const i18nPatterns = [
    {
      description: '4级宝箱公告包含新奖励信息',
      regex: /金色碎片.*x\s*1.*钻石.*x\s*1558850/
    }
  ];
  
  if (!validateFile('src/i18n/locales/zh.ts', i18nPatterns)) {
    overallSuccess = false;
  }
  
  // 4. 验证奖励路由
  const rewardsPatterns = [
    {
      description: '奖励统计包含所有碎片类型',
      regex: /fragment_green:\s*0.*fragment_blue:\s*0.*fragment_purple:\s*0.*fragment_gold:\s*0/s
    }
  ];
  
  if (!validateFile('src/routes/rewards.ts', rewardsPatterns)) {
    overallSuccess = false;
  }
  
  // 5. 运行概率测试
  console.log('\n🎲 运行概率分布测试...');
  try {
    // 模拟新的generateChestReward函数
    function generateChestReward() {
      const random = Math.random() * 100;
      let level = 1;
      
      if (random < 40) level = 1;
      else if (random < 70) level = 2;
      else if (random < 90) level = 3;
      else level = 4;
      
      return { level };
    }
    
    const testCount = 10000;
    const results = { level1: 0, level2: 0, level3: 0, level4: 0 };
    
    for (let i = 0; i < testCount; i++) {
      const reward = generateChestReward();
      results[`level${reward.level}`]++;
    }
    
    const probabilities = {
      level1: (results.level1 / testCount * 100).toFixed(1),
      level2: (results.level2 / testCount * 100).toFixed(1),
      level3: (results.level3 / testCount * 100).toFixed(1),
      level4: (results.level4 / testCount * 100).toFixed(1)
    };
    
    console.log(`📊 概率分布测试结果 (${testCount}次):`);
    console.log(`   1级宝箱: ${probabilities.level1}% (期望: 40%)`);
    console.log(`   2级宝箱: ${probabilities.level2}% (期望: 30%)`);
    console.log(`   3级宝箱: ${probabilities.level3}% (期望: 20%)`);
    console.log(`   4级宝箱: ${probabilities.level4}% (期望: 10%)`);
    
    // 检查概率是否在合理范围内 (±2%)
    const tolerance = 2;
    const probabilityChecks = [
      { actual: parseFloat(probabilities.level1), expected: 40, name: '1级' },
      { actual: parseFloat(probabilities.level2), expected: 30, name: '2级' },
      { actual: parseFloat(probabilities.level3), expected: 20, name: '3级' },
      { actual: parseFloat(probabilities.level4), expected: 10, name: '4级' }
    ];
    
    let probabilityPassed = true;
    probabilityChecks.forEach(check => {
      const diff = Math.abs(check.actual - check.expected);
      if (diff <= tolerance) {
        console.log(`✅ ${check.name}宝箱概率正常 (误差: ${diff.toFixed(1)}%)`);
      } else {
        console.log(`❌ ${check.name}宝箱概率异常 (误差: ${diff.toFixed(1)}%)`);
        probabilityPassed = false;
      }
    });
    
    if (!probabilityPassed) {
      overallSuccess = false;
    }
    
  } catch (error) {
    console.log(`❌ 概率测试失败: ${error.message}`);
    overallSuccess = false;
  }
  
  // 6. 总结
  console.log('\n' + '='.repeat(50));
  if (overallSuccess) {
    console.log('🎉 所有验证都通过！宝箱奖励逻辑修改成功！');
    console.log('\n📋 修改总结:');
    console.log('   ✅ 概率调整: 1级(40%), 2级(30%), 3级(20%), 4级(10%)');
    console.log('   ✅ 奖励固定化: 移除随机性，使用固定数值');
    console.log('   ✅ 碎片简化: 每级对应一种碎片类型');
    console.log('   ✅ 金牛币更新: 使用新的固定奖励数值');
    console.log('   ✅ 国际化更新: 更新公告内容');
    console.log('   ✅ 统计更新: 包含所有碎片类型');
  } else {
    console.log('❌ 验证失败！请检查上述错误并修复。');
  }
  console.log('='.repeat(50));
}

// 运行验证
runValidation();
