const { Sequelize, DataTypes } = require('sequelize');

// 数据库配置
const sequelize = new Sequelize({
  dialect: 'mysql',
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 3306,
  username: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'moofun_kaia_tasks_test',
  logging: false, // 关闭SQL日志
  timezone: '+08:00'
});

// 定义模型
const DeliveryLineConfig = sequelize.define('DeliveryLineConfig', {
  id: { type: DataTypes.INTEGER.UNSIGNED, autoIncrement: true, primaryKey: true },
  grade: { type: DataTypes.INTEGER.UNSIGNED, allowNull: false, unique: true },
  profit: { type: DataTypes.INTEGER.UNSIGNED, allowNull: false },
  capacity: { type: DataTypes.INTEGER.UNSIGNED, allowNull: false },
  production_interval: { type: DataTypes.DECIMAL(3, 1), allowNull: false },
  delivery_speed_display: { type: DataTypes.INTEGER.UNSIGNED, allowNull: false },
  upgrade_cost: { type: DataTypes.BIGINT.UNSIGNED, allowNull: false },
  createdAt: { type: DataTypes.DATE, allowNull: false, field: 'created_at' },
  updatedAt: { type: DataTypes.DATE, allowNull: false, field: 'updated_at' }
}, {
  tableName: 'delivery_line_configs',
  timestamps: true
});

const DeliveryLine = sequelize.define('DeliveryLine', {
  id: { type: DataTypes.INTEGER.UNSIGNED, autoIncrement: true, primaryKey: true },
  walletId: { type: DataTypes.INTEGER.UNSIGNED, allowNull: false },
  level: { type: DataTypes.INTEGER.UNSIGNED, allowNull: false, defaultValue: 1 },
  deliverySpeed: { type: DataTypes.DECIMAL(15, 3), allowNull: false },
  blockUnit: { type: DataTypes.DECIMAL(15, 3), allowNull: false },
  blockPrice: { type: DataTypes.DECIMAL(15, 3), allowNull: false },
  upgradeCost: { type: DataTypes.DECIMAL(20, 3), allowNull: false },
  lastDeliveryTime: { type: DataTypes.DATE, allowNull: false },
  pendingMilk: { type: DataTypes.DECIMAL(15, 3), allowNull: false, defaultValue: 0 },
  pendingBlocks: { type: DataTypes.INTEGER.UNSIGNED, allowNull: false, defaultValue: 0 }
}, {
  tableName: 'delivery_lines',
  timestamps: true
});

async function verifyDeliveryLineConfigIntegration() {
  console.log('🔍 验证流水线配置集成状态...\n');
  
  try {
    // 测试数据库连接
    await sequelize.authenticate();
    console.log('✅ 数据库连接成功');
    
    // 1. 检查配置表数据
    console.log('\n1️⃣ 检查配置表数据...');
    const configs = await DeliveryLineConfig.findAll({
      order: [['grade', 'ASC']]
    });
    
    console.log(`✅ 找到 ${configs.length} 个配置`);
    
    if (configs.length !== 50) {
      throw new Error(`配置数量不正确，期望50个，实际${configs.length}个`);
    }
    
    // 2. 检查现有流水线数据
    console.log('\n2️⃣ 检查现有流水线数据...');
    const deliveryLines = await DeliveryLine.findAll({
      order: [['level', 'ASC']]
    });
    
    console.log(`找到 ${deliveryLines.length} 个现有流水线`);
    
    if (deliveryLines.length === 0) {
      console.log('ℹ️ 没有现有流水线数据，这是正常的');
      return;
    }
    
    // 3. 验证数据一致性
    console.log('\n3️⃣ 验证数据一致性...');
    
    let consistentCount = 0;
    let inconsistentCount = 0;
    const inconsistentLines = [];
    
    for (const line of deliveryLines) {
      const config = configs.find(c => c.grade === line.level);
      
      if (!config) {
        console.warn(`⚠️ 流水线等级 ${line.level} 没有对应配置`);
        inconsistentCount++;
        inconsistentLines.push({
          id: line.id,
          walletId: line.walletId,
          level: line.level,
          issue: '没有对应配置'
        });
        continue;
      }
      
      // 检查各个字段是否匹配
      const speedMatch = Math.abs(parseFloat(line.deliverySpeed) - parseFloat(config.production_interval)) < 0.01;
      const capacityMatch = parseInt(line.blockUnit) === parseInt(config.capacity);
      const priceMatch = parseInt(line.blockPrice) === parseInt(config.profit);
      const costMatch = parseInt(line.upgradeCost) === parseInt(config.upgrade_cost);
      
      if (speedMatch && capacityMatch && priceMatch && costMatch) {
        consistentCount++;
        console.log(`✅ 流水线 ID ${line.id} (等级${line.level}) 数据与配置一致`);
      } else {
        inconsistentCount++;
        inconsistentLines.push({
          id: line.id,
          walletId: line.walletId,
          level: line.level,
          current: {
            speed: line.deliverySpeed,
            capacity: line.blockUnit,
            price: line.blockPrice,
            cost: line.upgradeCost
          },
          expected: {
            speed: config.production_interval,
            capacity: config.capacity,
            price: config.profit,
            cost: config.upgrade_cost
          },
          matches: {
            speed: speedMatch,
            capacity: capacityMatch,
            price: priceMatch,
            cost: costMatch
          }
        });
        
        console.warn(`⚠️ 流水线 ID ${line.id} (等级${line.level}) 数据与配置不匹配:`);
        if (!speedMatch) console.warn(`  速度: ${line.deliverySpeed} vs ${config.production_interval}`);
        if (!capacityMatch) console.warn(`  容量: ${line.blockUnit} vs ${config.capacity}`);
        if (!priceMatch) console.warn(`  价格: ${line.blockPrice} vs ${config.profit}`);
        if (!costMatch) console.warn(`  费用: ${line.upgradeCost} vs ${config.upgrade_cost}`);
      }
    }
    
    // 4. 生成报告
    console.log('\n📊 验证结果报告:');
    console.log(`✅ 一致的流水线: ${consistentCount}`);
    console.log(`⚠️ 不一致的流水线: ${inconsistentCount}`);
    
    if (inconsistentCount > 0) {
      console.log('\n❌ 发现不一致的流水线数据！');
      console.log('建议执行以下操作之一:');
      console.log('1. 运行数据迁移脚本重新同步数据');
      console.log('2. 手动更新不一致的流水线数据');
      console.log('3. 重新运行迁移: npx sequelize-cli db:migrate');
      
      console.log('\n不一致的流水线详情:');
      inconsistentLines.forEach(line => {
        console.log(`- ID ${line.id}, 钱包 ${line.walletId}, 等级 ${line.level}: ${line.issue || '数据不匹配'}`);
      });
      
      return false;
    } else {
      console.log('\n🎉 所有流水线数据与配置完全一致！');
      return true;
    }
    
  } catch (error) {
    console.error('\n❌ 验证失败:', error.message);
    return false;
  } finally {
    await sequelize.close();
  }
}

// 修复不一致数据的函数
async function fixInconsistentData() {
  console.log('🔧 开始修复不一致的流水线数据...\n');
  
  try {
    await sequelize.authenticate();
    
    const configs = await DeliveryLineConfig.findAll();
    const deliveryLines = await DeliveryLine.findAll();
    
    let fixedCount = 0;
    
    for (const line of deliveryLines) {
      const config = configs.find(c => c.grade === line.level);
      
      if (!config) {
        console.warn(`跳过等级 ${line.level}，没有对应配置`);
        continue;
      }
      
      // 检查是否需要更新
      const needsUpdate = 
        Math.abs(parseFloat(line.deliverySpeed) - parseFloat(config.production_interval)) >= 0.01 ||
        parseInt(line.blockUnit) !== parseInt(config.capacity) ||
        parseInt(line.blockPrice) !== parseInt(config.profit) ||
        parseInt(line.upgradeCost) !== parseInt(config.upgrade_cost);
      
      if (needsUpdate) {
        await line.update({
          deliverySpeed: config.production_interval,
          blockUnit: config.capacity,
          blockPrice: config.profit,
          upgradeCost: config.upgrade_cost
        });
        
        fixedCount++;
        console.log(`✅ 修复流水线 ID ${line.id} (等级${line.level})`);
      }
    }
    
    console.log(`\n🎉 修复完成！共修复 ${fixedCount} 个流水线`);
    return true;
    
  } catch (error) {
    console.error('\n❌ 修复失败:', error.message);
    return false;
  } finally {
    await sequelize.close();
  }
}

// 执行验证
if (require.main === module) {
  const args = process.argv.slice(2);
  
  if (args.includes('--fix')) {
    fixInconsistentData()
      .then(success => {
        process.exit(success ? 0 : 1);
      })
      .catch(error => {
        console.error('执行异常:', error);
        process.exit(1);
      });
  } else {
    verifyDeliveryLineConfigIntegration()
      .then(success => {
        if (!success) {
          console.log('\n💡 提示: 使用 --fix 参数可以自动修复不一致的数据');
          console.log('命令: node scripts/verify-delivery-line-config-integration.js --fix');
        }
        process.exit(success ? 0 : 1);
      })
      .catch(error => {
        console.error('执行异常:', error);
        process.exit(1);
      });
  }
}

module.exports = { verifyDeliveryLineConfigIntegration, fixInconsistentData };
