'use strict';
const axios = require('axios');

module.exports = {
  up: async (queryInterface, Sequelize) => {
    // 获取当前Kaia价格
    console.log('正在从Kaiascan API获取Kaia价格...');
    let kaiaUsdPrice;
    try {
      const KAIASCAN_API_URL = 'https://mainnet-oapi.kaiascan.io/api?module=stats&action=coinprice&apikey=625b664e-c431-442c-9648-b4a9bb5d3a3c';
      const response = await axios.get(KAIASCAN_API_URL, {
        timeout: 10000, // 10秒超时
      });
      
      if (response.data && response.data.status === '1' && response.data.result) {
        kaiaUsdPrice = parseFloat(response.data.result.coin_usd);
        if (isNaN(kaiaUsdPrice) || kaiaUsdPrice <= 0) {
          throw new Error(`无效的价格数据: ${response.data.result.coin_usd}`);
        }
        console.log(`获取到Kaia价格: 1 KAIA = ${kaiaUsdPrice} USD`);
      } else {
        throw new Error(`API返回错误状态: ${response.data?.status || 'unknown'}`);
      }
    } catch (error) {
      console.error('获取Kaia价格失败:', error);
      console.log('使用默认价格 0.114 USD/KAIA');
      kaiaUsdPrice = 0.114; // 默认价格，如果API调用失败
    }

    // 首先更新现有的时间跳跃商品，确保它们支持PHRS支付
    await queryInterface.sequelize.query(`
      UPDATE iap_products 
      SET pricePhrs = priceUsd * 1000
      WHERE type = 'time_warp' AND pricePhrs IS NULL
    `);

    // 更新现有的VIP会员商品，确保它们支持PHRS支付
    await queryInterface.sequelize.query(`
      UPDATE iap_products 
      SET pricePhrs = priceUsd * 1000
      WHERE type = 'vip_membership' AND pricePhrs IS NULL
    `);

    // 更新现有的速度加成商品，确保它们支持PHRS支付
    await queryInterface.sequelize.query(`
      UPDATE iap_products 
      SET pricePhrs = priceUsd * 1000
      WHERE type = 'speed_boost' AND pricePhrs IS NULL
    `);

    // 更新现有的特殊套餐商品，确保它们支持PHRS支付
    await queryInterface.sequelize.query(`
      UPDATE iap_products 
      SET pricePhrs = priceUsd * 1000
      WHERE type = 'special_offer' AND pricePhrs IS NULL
    `);

    // 检查是否已存在新的时间跳跃商品
    const existingTimeWarp1hr = await queryInterface.sequelize.query(
      "SELECT COUNT(*) as count FROM iap_products WHERE productId = 'time_warp_1hr'",
      { type: Sequelize.QueryTypes.SELECT }
    );

    if (existingTimeWarp1hr[0].count === 0) {
      // 添加新的时间跳跃商品
      await queryInterface.bulkInsert('iap_products', [
        {
          productId: 'time_warp_1hr',
          name: 'Time Warp 1hr',
          description: '时间跳跃1小时，立即获得1小时的牛奶产量和方块收益（GEM）',
          type: 'time_warp',
          priceUsd: 0.69,
          priceKaia: parseFloat((0.69 / kaiaUsdPrice).toFixed(4)),
          pricePhrs: 690, // 0.69 USD * 1000 PHRS/USD
          multiplier: 1,
          duration: 1,
          quantity: 1,
          dailyLimit: 1,
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date()
        },
        {
          productId: 'time_warp_6hr',
          name: 'Time Warp 6hr',
          description: '时间跳跃6小时，立即获得6小时的牛奶产量和方块收益（GEM）',
          type: 'time_warp',
          priceUsd: 2.49,
          priceKaia: parseFloat((2.49 / kaiaUsdPrice).toFixed(4)),
          pricePhrs: 2490, // 2.49 USD * 1000 PHRS/USD
          multiplier: 1,
          duration: 6,
          quantity: 1,
          dailyLimit: 1,
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      ]);
    }

    // 更新现有的24小时时间跳跃商品价格
    await queryInterface.sequelize.query(`
      UPDATE iap_products 
      SET priceUsd = 5.99,
          priceKaia = ${parseFloat((5.99 / kaiaUsdPrice).toFixed(4))},
          pricePhrs = 5990,
          description = '时间跳跃24小时，立即获得24小时的牛奶产量和方块收益（GEM）'
      WHERE productId = 'time_warp_24hr'
    `);

    // 更新特殊套餐的配置，确保时间跳跃是48小时（24小时 x 2）
    await queryInterface.sequelize.query(`
      UPDATE iap_products 
      SET description = '特殊套餐：Time Warp 24hr x2（直接跳转48小时获得收益）+ Speed Boost x4 24hr x2，限购一账号一次',
          pricePhrs = 19990
      WHERE productId = 'special_offer_bundle'
    `);

    console.log('IAP商品已更新，支持PHRS支付');
  },

  down: async (queryInterface, Sequelize) => {
    // 删除新添加的商品
    await queryInterface.bulkDelete('iap_products', {
      productId: ['time_warp_1hr', 'time_warp_6hr']
    }, {});

    // 移除PHRS价格字段
    await queryInterface.sequelize.query(`
      UPDATE iap_products 
      SET pricePhrs = NULL
    `);
  }
};
