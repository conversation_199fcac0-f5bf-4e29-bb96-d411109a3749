// src/config/AppConfig.ts
import dotenv from 'dotenv';

dotenv.config();

export interface DatabaseConfig {
  host: string;
  port: number;
  database: string;
  username: string;
  password: string;
  dialect: 'mysql';
  timezone: string;
  logging: boolean;
  pool: {
    max: number;
    min: number;
    acquire: number;
    idle: number;
  };
}

export interface RedisConfig {
  host: string;
  port: number;
  password?: string;
}

export interface ServerConfig {
  port: number;
  environment: string;
}

export interface TonConfig {
  apiKey?: string;
  network: 'mainnet' | 'testnet';
  walletSeed?: string;
}

export class AppConfig {
  public readonly server: ServerConfig;
  public readonly database: DatabaseConfig;
  public readonly redis: RedisConfig;
  public readonly ton: TonConfig;

  constructor() {
    this.server = {
      port: parseInt(process.env.PORT || '3000', 10),
      environment: process.env.NODE_ENV || 'development'
    };

    this.database = {
      host: process.env.DB_HOST || 'localhost',
      port: parseInt(process.env.DB_PORT || '3669', 10),
      database: process.env.DB_NAME || 'wolf_fun_db',
      username: process.env.DB_USER || 'root',
      password: process.env.DB_PASS || 'root',
      dialect: 'mysql',
      timezone: '+08:00',
      logging: this.server.environment === 'development',
      pool: {
        max: 10,
        min: 0,
        acquire: 60000,
        idle: 10000
      }
    };

    this.redis = {
      host: process.env.REDIS_HOST || 'localhost',
      port: parseInt(process.env.REDIS_PORT || '6379', 10),
      password: process.env.REDIS_PASSWORD || undefined
    };

    this.ton = {
      apiKey: process.env.TONCENTER_API_KEY,
      network: (process.env.TON_NETWORK as 'mainnet' | 'testnet') || 'testnet',
      walletSeed: process.env.TON_WALLET_SEED
    };
  }

  public isDevelopment(): boolean {
    return this.server.environment === 'development';
  }

  public isProduction(): boolean {
    return this.server.environment === 'production';
  }

  public validate(): string[] {
    const errors: string[] = [];

    if (!this.database.host) {
      errors.push('数据库主机地址未配置');
    }

    if (!this.database.database) {
      errors.push('数据库名称未配置');
    }

    if (this.isProduction() && !this.ton.apiKey) {
      errors.push('生产环境需要配置 TON API Key');
    }

    return errors;
  }
}

export const appConfig = new AppConfig();
