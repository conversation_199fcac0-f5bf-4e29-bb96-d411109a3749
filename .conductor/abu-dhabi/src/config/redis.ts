// src/config/redis.ts
import Redis from 'ioredis';
import dotenv from 'dotenv';

dotenv.config();

export const redis = new Redis({
  host: process.env.REDIS_HOST || 'redis',
  port: parseInt(process.env.REDIS_PORT || '6379', 10),
  password: process.env.REDIS_PASS || '',
  maxRetriesPerRequest: null
});

redis.on('connect', () => {
  console.log('✅ Redis 连接成功!');
  console.log(`   连接地址: ${process.env.REDIS_HOST || 'redis'}:${process.env.REDIS_PORT || '6379'}`);
  console.log(`   认证状态: ${process.env.REDIS_PASS ? '已启用密码认证' : '无密码认证'}`);
});

redis.on('error', (err) => {
  console.error('Redis error:', err);
});