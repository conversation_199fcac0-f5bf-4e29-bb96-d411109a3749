import { Request, Response } from 'express';
import { MyRequest } from '../types/customRequest';
import { FarmConfigService } from '../services/farmConfigService';
import { FarmConfig } from '../models/FarmConfig';
import { FarmConfigVersion } from '../models/FarmConfigVersion';
import { successResponse, errorResponse } from '../utils/responseUtil';
import { tFromRequest } from '../i18n';

/**
 * 农场配置管理控制器
 * 提供配置上传、版本管理、激活回滚等管理员功能
 */
class FarmConfigController {
  /**
   * 上传配置文件
   */
  public async uploadConfig(req: MyRequest, res: Response): Promise<void> {
    try {
      // 检查是否有文件上传
      if (!req.file) {
        res.status(400).json(errorResponse(
          tFromRequest(req, 'errors.noFileUploaded') || '请上传Excel文件'
        ));
        return;
      }

      const { versionName, description } = req.body;
      const createdBy = req.user?.walletAddress || 'admin';

      if (!versionName) {
        res.status(400).json(errorResponse('请提供版本名称'));
        return;
      }

      // 上传配置
      const version = await FarmConfigService.uploadNewConfig(
        req.file.buffer,
        versionName,
        description,
        createdBy
      );

      res.json(successResponse({
        version,
        versionName,
        description,
        uploadedBy: createdBy,
        message: '配置上传成功'
      }));

    } catch (error: any) {
      console.error('配置上传失败:', error);
      res.status(500).json(errorResponse(error.message || '配置上传失败'));
    }
  }

  /**
   * 获取配置版本列表
   */
  public async getVersions(req: MyRequest, res: Response): Promise<void> {
    try {
      const versions = await FarmConfigService.getAllVersions();
      const stats = await FarmConfigService.getVersionStats();

      res.json(successResponse({
        versions,
        stats,
        message: '获取版本列表成功'
      }));

    } catch (error: any) {
      console.error('获取版本列表失败:', error);
      res.status(500).json(errorResponse(error.message || '获取版本列表失败'));
    }
  }

  /**
   * 获取当前激活配置
   */
  public async getCurrentConfig(req: MyRequest, res: Response): Promise<void> {
    try {
      console.log('🔍 [DEBUG] 开始获取当前配置...');

      const configs = await FarmConfigService.getCurrentConfig();
      console.log(`🔍 [DEBUG] FarmConfigService.getCurrentConfig() 返回: ${configs.length} 条配置`);

      const activeVersion = await FarmConfigVersion.getActiveVersion();
      console.log(`🔍 [DEBUG] 激活版本: ${activeVersion?.version || '无'}`);

      res.json(successResponse({
        version: activeVersion?.version || null,
        versionName: activeVersion?.name || null,
        configs,
        totalConfigs: configs.length,
        message: '获取当前配置成功'
      }));

    } catch (error: any) {
      console.error('获取当前配置失败:', error);
      res.status(500).json(errorResponse(error.message || '获取当前配置失败'));
    }
  }

  /**
   * 激活指定版本
   */
  public async activateVersion(req: MyRequest, res: Response): Promise<void> {
    try {
      const { version, remark } = req.body;

      if (!version) {
        res.status(400).json(errorResponse('请提供版本号'));
        return;
      }

      // 获取当前激活版本
      const previousVersion = await FarmConfigVersion.getActiveVersion();

      // 激活新版本
      const success = await FarmConfigService.activateVersion(version);

      if (success) {
        res.json(successResponse({
          previousVersion: previousVersion?.version || null,
          newVersion: version,
          remark,
          activatedAt: new Date().toISOString(),
          message: '配置版本已激活'
        }));
      } else {
        res.status(500).json(errorResponse('激活版本失败'));
      }

    } catch (error: any) {
      console.error('激活版本失败:', error);
      res.status(500).json(errorResponse(error.message || '激活版本失败'));
    }
  }

  /**
   * 回滚到上一版本
   */
  public async rollbackToPrevious(req: MyRequest, res: Response): Promise<void> {
    try {
      // 获取当前激活版本
      const currentVersion = await FarmConfigVersion.getActiveVersion();

      // 执行回滚
      const success = await FarmConfigService.rollbackToPrevious();

      if (success) {
        const newActiveVersion = await FarmConfigVersion.getActiveVersion();
        
        res.json(successResponse({
          previousVersion: currentVersion?.version || null,
          newVersion: newActiveVersion?.version || null,
          rolledBackAt: new Date().toISOString(),
          message: '已回滚到上一版本'
        }));
      } else {
        res.status(500).json(errorResponse('回滚失败'));
      }

    } catch (error: any) {
      console.error('回滚失败:', error);
      res.status(500).json(errorResponse(error.message || '回滚失败'));
    }
  }

  /**
   * 删除指定版本
   */
  public async deleteVersion(req: MyRequest, res: Response): Promise<void> {
    try {
      const { version } = req.params;

      if (!version) {
        res.status(400).json(errorResponse('请提供版本号'));
        return;
      }

      const success = await FarmConfigService.deleteVersion(version);

      if (success) {
        res.json(successResponse({
          deletedVersion: version,
          deletedAt: new Date().toISOString(),
          message: '版本删除成功'
        }));
      } else {
        res.status(500).json(errorResponse('删除版本失败'));
      }

    } catch (error: any) {
      console.error('删除版本失败:', error);
      res.status(500).json(errorResponse(error.message || '删除版本失败'));
    }
  }

  /**
   * 获取版本详情
   */
  public async getVersionDetail(req: MyRequest, res: Response): Promise<void> {
    try {
      const { version } = req.params;

      if (!version) {
        res.status(400).json(errorResponse('请提供版本号'));
        return;
      }

      const versionDetail = await FarmConfigVersion.getVersionDetail(version);
      if (!versionDetail) {
        res.status(404).json(errorResponse('版本不存在'));
        return;
      }

      const configs = await FarmConfig.getConfigsByVersion(version);

      res.json(successResponse({
        version: versionDetail,
        configs,
        totalConfigs: configs.length,
        message: '获取版本详情成功'
      }));

    } catch (error: any) {
      console.error('获取版本详情失败:', error);
      res.status(500).json(errorResponse(error.message || '获取版本详情失败'));
    }
  }

  /**
   * 获取配置统计信息
   */
  public async getStats(req: MyRequest, res: Response): Promise<void> {
    try {
      const stats = await FarmConfigService.getVersionStats();
      const activeVersion = await FarmConfigVersion.getActiveVersion();
      const allVersions = await FarmConfigService.getAllVersions();

      res.json(successResponse({
        ...stats,
        activeVersionDetail: activeVersion,
        recentVersions: allVersions.slice(0, 5), // 最近5个版本
        message: '获取统计信息成功'
      }));

    } catch (error: any) {
      console.error('获取统计信息失败:', error);
      res.status(500).json(errorResponse(error.message || '获取统计信息失败'));
    }
  }

  /**
   * 预热缓存
   */
  public async warmupCache(req: MyRequest, res: Response): Promise<void> {
    try {
      await FarmConfigService.warmupCache();

      res.json(successResponse({
        message: '缓存预热完成'
      }));

    } catch (error: any) {
      console.error('缓存预热失败:', error);
      res.status(500).json(errorResponse(error.message || '缓存预热失败'));
    }
  }

  /**
   * 验证配置数据
   */
  public async validateConfig(req: MyRequest, res: Response): Promise<void> {
    try {
      // 检查是否有文件上传
      if (!req.file) {
        res.status(400).json(errorResponse('请上传Excel文件'));
        return;
      }

      // 解析Excel文件（不保存到数据库）
      const workbook = require('xlsx').read(req.file.buffer, { type: 'buffer' });
      const sheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[sheetName];
      
      const jsonData = require('xlsx').utils.sheet_to_json(worksheet, {
        header: 1,
        defval: ''
      });

      // 简单解析数据进行验证
      const headers = jsonData[0] as string[];
      const dataRows = jsonData.slice(1);
      
      const configs = (dataRows as any[][]).map((row: any[]) => {
        const config: any = {};
        headers.forEach((header, index) => {
          const value = row[index];
          switch (header.toLowerCase()) {
            case 'grade':
            case '等级':
              config.grade = parseInt(value) || 0;
              break;
            case 'production':
            case '每秒产出计算用值':
              config.production = parseInt(value) || 0;
              break;
            case 'cow':
            case '奶牛数量':
              config.cow = parseInt(value) || 0;
              break;
            case 'speed':
            case '生产速度百分比':
              config.speed = parseInt(value) || 0;
              break;
            case 'milk':
            case '牛奶生产':
              config.milk = parseFloat(value) || 0;
              break;
            case 'cost':
            case '升级花费':
              config.cost = parseInt(value) || 0;
              break;
            case 'offline':
            case '离线产出':
              config.offline = parseFloat(value) || 0;
              break;
          }
        });
        return config;
      }).filter(config => config.grade !== undefined);

      // 验证数据
      const validation = FarmConfig.validateConfigData(configs);

      res.json(successResponse({
        isValid: validation.isValid,
        errors: validation.errors,
        configCount: configs.length,
        sampleConfigs: configs.slice(0, 5), // 前5条数据作为示例
        message: validation.isValid ? '配置数据验证通过' : '配置数据验证失败'
      }));

    } catch (error: any) {
      console.error('配置验证失败:', error);
      res.status(500).json(errorResponse(error.message || '配置验证失败'));
    }
  }
}

export default new FarmConfigController();
