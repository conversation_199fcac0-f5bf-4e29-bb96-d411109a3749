import { Response } from 'express';
import { MyRequest } from '../types/customRequest';
import farmPlotService from '../services/farmPlotService';
import { responseUtil } from '../utils/responseUtil';

class FarmPlotController {
  // 触发牛舍产生牛奶
  public async triggerMilkProduction(req: MyRequest, res: Response): Promise<void> {
    try {
      const walletId = Number(req.user?.walletId);
      const { plotNumber } = req.body; // 前端传递的牧场区编号
      
      if (!walletId) {
        responseUtil.error(res, '用户未登录', 401);
        return;
      }
      
      if (!plotNumber || isNaN(Number(plotNumber)) || Number(plotNumber) <= 0) {
        responseUtil.error(res, '无效的牧场区编号');
        return;
      }
      
      // 调用service方法触发牛舍产生牛奶
      const { producedMilk, addedToProductionLine } = await farmPlotService.triggerMilkProduction(walletId, Number(plotNumber));
      
      responseUtil.success(res, { 
        producedMilk, // 产生的牛奶数量
        addedToProductionLine, // 是否已添加到生产线
        message: `牛舍成功产生了 ${producedMilk} 单位牛奶` 
      });
    } catch (error: any) {
      responseUtil.error(res, error.message);
    }
  }
  
  // 添加牛奶到生产线
  public async addMilkToProductionLine(req: MyRequest, res: Response): Promise<void> {
    try {
      const walletId = Number(req.user?.walletId);
      const { requestedAmount } = req.body; // 前端请求添加的牛奶数量
      
      if (!walletId) {
        responseUtil.error(res, '用户未登录', 401);
        return;
      }
      
      if (!requestedAmount || isNaN(Number(requestedAmount)) || Number(requestedAmount) <= 0) {
        responseUtil.error(res, '无效的牛奶数量');
        return;
      }
      
      // 调用service方法添加牛奶到生产线
      const { actualAmount, totalProduction } = await farmPlotService.addMilkToProductionLine(walletId, Number(requestedAmount));
      
      responseUtil.success(res, { 
        actualAmount, // 实际添加的牛奶数量
        totalProduction, // 牧场区的总产量（每秒）
        message: `已成功添加 ${actualAmount} 单位牛奶到生产线` 
      });
    } catch (error: any) {
      responseUtil.error(res, error.message);
    }
  }

  // 获取用户的所有牧场区
  public async getUserFarmPlots(req: MyRequest, res: Response): Promise<void> {
   
    try {
      const walletId = Number(req.user?.walletId);
     

      if (!walletId) {
        console.log('❌ walletId 无效，返回401错误');
        responseUtil.error(res, '用户未登录', 401);
        return;
      }
      
      await farmPlotService.initializeUserFarmPlots(walletId);
   
      const farmPlots = await farmPlotService.getUserFarmPlots(walletId);
     
      responseUtil.success(res, { farmPlots: farmPlots });
    } catch (error: any) {
      console.log('❌ getUserFarmPlots 发生错误:', error.message);
      console.log('错误堆栈:', error.stack);
      responseUtil.error(res, error.message);
    }
  }
  
  // 升级牧场区
  public async upgradeFarmPlot(req: MyRequest, res: Response): Promise<void> {
    try {
      const walletId = Number(req.user?.walletId);
      const { plotNumber } = req.body;
      
      if (!walletId) {
        responseUtil.error(res, '用户未登录', 401);
        return;
      }
      
      if (!plotNumber || isNaN(Number(plotNumber))) {
        responseUtil.error(res, '无效的牧场区编号');
        return;
      }
      
      const farmPlot = await farmPlotService.upgradeFarmPlot(walletId, Number(plotNumber));
      responseUtil.success(res, { farmPlot });
    } catch (error: any) {
      responseUtil.error(res, error.message);
    }
  }
  
  // 解锁牧场区
  public async unlockFarmPlot(req: MyRequest, res: Response): Promise<void> {
    try {
      const walletId = Number(req.user?.walletId);
      const { plotNumber } = req.body;
      
      if (!walletId) {
        responseUtil.error(res, '用户未登录', 401);
        return;
      }
      
      if (!plotNumber || isNaN(Number(plotNumber))) {
        responseUtil.error(res, '无效的牧场区编号');
        return;
      }
      
      const farmPlot = await farmPlotService.unlockFarmPlot(walletId, Number(plotNumber));
      responseUtil.success(res, { farmPlot });
    } catch (error: any) {
      responseUtil.error(res, error.message);
    }
  }
  
  // 收集牧场区的牛奶
  public async collectMilk(req: MyRequest, res: Response): Promise<void> {
    try {
      const walletId = Number(req.user?.walletId);
      const { plotNumber } = req.body;
      
      if (!walletId) {
        responseUtil.error(res, '用户未登录', 401);
        return;
      }
      
      if (!plotNumber || isNaN(Number(plotNumber))) {
        responseUtil.error(res, '无效的牧场区编号');
        return;
      }
      
      const collectedMilk = await farmPlotService.collectMilk(walletId, Number(plotNumber));
      responseUtil.success(res, { collectedMilk });
    } catch (error: any) {
      responseUtil.error(res, error.message);
    }
  }
  
  // 收集所有牧场区的牛奶
  public async collectAllMilk(req: MyRequest, res: Response): Promise<void> {
    try {
      const walletId = Number(req.user?.walletId);
      
      if (!walletId) {
        responseUtil.error(res, '用户未登录', 401);
        return;
      }
      
      const totalCollectedMilk = await farmPlotService.collectAllMilk(walletId);
      responseUtil.success(res, { totalCollectedMilk });
    } catch (error: any) {
      responseUtil.error(res, error.message);
    }
  }
  
  // 计算离线收益
  public async calculateOfflineEarnings(req: MyRequest, res: Response): Promise<void> {
    try {
      const walletId = Number(req.user?.walletId);
      const { offlineTime } = req.body; // 离线时间（秒）
      
      if (!walletId) {
        responseUtil.error(res, '用户未登录', 401);
        return;
      }
      
      if (!offlineTime || isNaN(Number(offlineTime))) {
        responseUtil.error(res, '无效的离线时间');
        return;
      }
      
      const totalMilk = await farmPlotService.calculateOfflineEarnings(walletId, Number(offlineTime));
      responseUtil.success(res, { totalMilk });
    } catch (error: any) {
      responseUtil.error(res, error.message);
    }
  }
}

export default new FarmPlotController();