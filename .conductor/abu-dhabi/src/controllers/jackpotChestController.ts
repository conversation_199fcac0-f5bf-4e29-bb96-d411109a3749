import { Request, Response } from "express";
import * as jackpotChestService from "../services/jackpotChestService";
import * as chestAccelerationService from "../services/chestAccelerationService";
import { t } from "../i18n";
import { MyRequest } from "../types/customRequest";
import { getReferralChestsCount } from '../services/userService';
import { sequelize } from "../config/db";
import { User, UserWallet } from "../models";
import bot from "../bot";
import { randomUUID } from "node:crypto";

/**
 * 获取宝箱倒计时状态
 */
export async function getChestCountdownStatus(req: Request, res: Response) {
  try {

    const myReq = req as MyRequest;
    const { walletId, userId } = myReq.user!;
    const status = await jackpotChestService.getChestCountdownStatus(userId, walletId!);

    return res.json({
      ok: true,
      data: status
    });
  } catch (error: any) {
    console.error("获取宝箱倒计时状态失败:", error);
    return res.status(500).json({
      ok: false,
      message: error.message || t('errors.serverError')
    });
  }
}

/**
 * 获取用户的宝箱加速状态
 */
export async function getAccelerationStatus(req: Request, res: Response) {
  try {
    const myReq = req as MyRequest;
    const { walletId, userId } = myReq.user!;

    // 获取用户的加速状态
    const status = await chestAccelerationService.getUserAccelerationStatus(userId, walletId!);

    return res.json({
      ok: true,
      data: status
    });
  } catch (error: any) {
    console.error("获取宝箱加速状态失败:", error);
    return res.status(500).json({
      ok: false,
      message: error.message || t('errors.serverError')
    });
  }
}

/**
 * 领取倒计时宝箱
 */
export async function collectJackpotChest(req: Request, res: Response) {
  const transaction = await sequelize.transaction();
  try {
    const myReq = req as MyRequest;
    const { walletId, userId } = myReq.user!;

    const result = await jackpotChestService.collectJackpotChest(userId, walletId!, transaction);

    await transaction.commit();
    return res.json({
      ok: true,
      data: result
    });
  } catch (error: any) {
    await transaction.rollback();
    console.error("领取Jackpot宝箱失败:", error);
    return res.status(400).json({
      ok: false,
      message: error.message || t('errors.serverError')
    });
  }
}




/**
 * 使用分享助力链接
 */
export async function useShareBoostLink(req: Request, res: Response) {
  try {
    const myReq = req as MyRequest;
    const { walletId, userId } = myReq.user!;
    const { code } = req.body;

    if (!code) {
      return res.status(400).json({
        ok: false,
        message: t('errors.missingShareCode')
      });
    }

    const result = await jackpotChestService.useShareBoostLink(code, userId, walletId!);

    return res.json({
      ok: true,
      data: result
    });
  } catch (error: any) {
    console.error("使用分享助力链接失败:", error);
    return res.status(400).json({
      ok: false,
      message: error.message || t('errors.serverError')
    });
  }
}


/**
 * 切换自动领取状态
 */
export async function toggleAutoCollect(req: Request, res: Response) {
  try {
    const myReq = req as MyRequest;
    const { walletId, userId } = myReq.user!;
    const { autoCollect } = req.body;

    if (typeof autoCollect !== 'boolean') {
      return res.status(400).json({
        ok: false,
        message: t('errors.invalidAutoCollectValue')
      });
    }

    // 从数据库中查询用户的isStar字段
    const userWallet = await UserWallet.findOne({ where: { id: walletId } });
    const isStar = userWallet?.isStar || false;

    // 检查用户是否为Telegram Star用户
    if (autoCollect && !isStar) {
      return res.status(403).json({
        ok: false,
        message: '自动领取功能仅限Telegram Star用户使用'
      });
    }

    const result = await jackpotChestService.toggleAutoCollect(userId, walletId!, autoCollect);

    return res.json({
      ok: true,
      data: result
    });
  } catch (error: any) {
    console.error("切换自动领取状态失败:", error);
    return res.status(400).json({
      ok: false,
      message: error.message || t('errors.serverError')
    });
  }
}


/**
 * 获取Jackpot奖池状态
 */
export async function getJackpotPoolStatus(req: Request, res: Response) {
  try {
    const status = await jackpotChestService.getJackpotPoolStatus();

    return res.json({
      ok: true,
      data: status
    });
  } catch (error: any) {
    console.error("获取Jackpot奖池状态失败:", error);
    return res.status(500).json({
      ok: false,
      message: error.message || t('errors.serverError')
    });
  }
}


/**
 * 获取用户发出的助力历史（分页）
 */
// export async function getUserOutgoingBoostHistory(req: Request, res: Response) {
//   try {
//     const myReq = req as MyRequest;
//     const { walletId, userId } = myReq.user!;

//     // 获取分页参数
//     const page = parseInt(req.query.page as string) || 1;
//     const pageSize = parseInt(req.query.pageSize as string) || 10;

//     const history = await jackpotChestService.getUserOutgoingBoostHistory(
//       userId, 
//       walletId!, 
//       page, 
//       pageSize
//     );

//     return res.json({
//       ok: true,
//       data: history
//     });
//   } catch (error: any) {
//     console.error("获取用户发出的助力历史失败:", error);
//     return res.status(500).json({
//       ok: false,
//       message: error.message || t('errors.serverError')
//     });
//   }
// }

/**
 * 获取用户收到的助力历史（分页）
 */
export async function getUserIncomingBoostHistory(req: Request, res: Response) {
  try {
    const myReq = req as MyRequest;
    const { walletId, userId } = myReq.user!;

    // 获取分页参数
    const page = parseInt(req.query.page as string) || 1;
    const pageSize = parseInt(req.query.pageSize as string) || 10;

    const history = await jackpotChestService.getUserIncomingBoostHistory(
      userId,
      walletId!,
      page,
      pageSize
    );

    return res.json({
      ok: true,
      data: history
    });
  } catch (error: any) {
    console.error("获取用户收到的助力历史失败:", error);
    return res.status(500).json({
      ok: false,
      message: error.message || t('errors.serverError')
    });
  }
}


/**
 * 获取用户的分享助力历史
 */
export async function getShareBoostHistory(req: Request, res: Response) {
  try {
    const myReq = req as MyRequest;
    const { walletId, userId } = myReq.user!;

    // 获取方向参数
    const direction = (req.query.direction as string) === 'incoming' ? 'incoming' : 'incoming';

    // 获取分页参数
    const page = parseInt(req.query.page as string) || 1;
    const pageSize = parseInt(req.query.pageSize as string) || 10;

    const history = await jackpotChestService.getShareBoostHistory(
      userId,
      walletId!,
      direction,
      page,
      pageSize
    );

    return res.json({
      ok: true,
      data: history
    });
  } catch (error: any) {
    console.error("获取用户分享助力历史失败:", error);
    return res.status(500).json({
      ok: false,
      message: error.message || t('errors.serverError')
    });
  }
}

/**
 * 加速宝箱倒计时
 */
export async function accelerateChestCountdown(req: Request, res: Response) {
  const transaction = await sequelize.transaction();
  try {
    const myReq = req as MyRequest;
    const { walletId, userId } = myReq.user!;
    const { seconds } = req.body;

    // 验证加速秒数
    if (!seconds || isNaN(Number(seconds)) || Number(seconds) <= 0) {
      await transaction.rollback();
      return res.status(400).json({
        ok: false,
        message: t('errors.invalidAccelerationSeconds') || "请提供有效的加速秒数"
      });
    }
    
    // 验证单次加速不超过最大限制（10秒）
    if (Number(seconds) > 10) {
      await transaction.rollback();
      return res.status(400).json({
        ok: false,
        message: t('errors.accelerationExceedsLimit')
      });
    }

    // 调用服务方法进行加速
    const result = await chestAccelerationService.accelerateChestCountdown(
      userId,
      walletId!,
      Number(seconds),
      transaction
    );

    await transaction.commit();
    return res.json({
      ok: true,
      message: result.message,
      data: {
        acceleratedSeconds: result.acceleratedSeconds,
        newNextAvailableTime: result.newNextAvailableTime,
        totalAcceleratedToday: result.totalAcceleratedToday,
        remainingAccelerationSeconds: result.remainingAccelerationSeconds
      }
    });
  } catch (error: any) {
    await transaction.rollback();
    console.error("加速宝箱倒计时失败:", error);
    return res.status(400).json({
      ok: false,
      message: error.message || t('errors.serverError')
    });
  }
}



/**
 * 获取用户的分享助力链接
 */
export async function getUserShareBoostLinks(req: Request, res: Response) {
  try {
    const myReq = req as MyRequest;
    const { walletId, userId } = myReq.user!;

    // 获取状态过滤参数，默认为'unused'（未使用）
    // 可选值：'all'（全部）, 'used'（已使用）, 'unused'（未使用）
    const status = (req.query.status as string) || 'unused';

    const links = await jackpotChestService.getUserShareBoostLinks(userId, walletId!, status);

    return res.json({
      ok: true,
      data: links
    });
  } catch (error: any) {
    console.error("获取用户分享助力链接失败:", error);
    return res.status(500).json({
      ok: false,
      message: error.message || t('errors.serverError')
    });
  }
}



/**
 * 获取推荐宝箱数量
 */
export async function getReferralChestCount(req: Request, res: Response) {
  try {
    const myReq = req as MyRequest;
    const { walletId, userId } = myReq.user!;
    const count = await getReferralChestsCount(userId, walletId!);

    return res.json({
      ok: true,
      data: { count, limit: 10 }
    });
  } catch (error: any) {
    console.error("获取推荐宝箱数量失败:", error);
    return res.status(500).json({
      ok: false,
      message: error.message || t('errors.serverError')
    });
  }
}


/**
 * 获取用户的加速信息
 */
export async function getUserBoostInfo(req: Request, res: Response) {
  try {
    const myReq = req as MyRequest;
    const { walletId, userId } = myReq.user!;

    if (!userId) {
      return res.status(401).json({ ok: false, message: '未授权' });
    }

    // 获取分页参数
    const page = parseInt(req.query.page as string) || 1;
    const pageSize = parseInt(req.query.pageSize as string) || 10;

    // 获取用户的加速信息
    const result = await jackpotChestService.getUserBoostInfo(userId, walletId!, page, pageSize);

    return res.json({
      ok: true,
      data: result
    });
  } catch (error: any) {
    console.error("获取用户加速信息失败:", error);
    return res.status(500).json({
      ok: false,
      message: error.message || t('errors.serverError')
    });
  }
}


/**
 * 使用Telegram分享助力链接 - 不需要钱包认证，只需要Telegram认证
 */
export async function useTelegramShareBoostLink(req: Request, res: Response) {
  try {
    const myReq = req as MyRequest;
    const { telegramId, userId, isPremium } = myReq.user!;
    const { code } = req.body;

    if (!code) {
      return res.status(400).json({
        ok: false,
        message: t('errors.missingShareCode')
      });
    }

    // 如果用户ID为0，表示这是一个未注册的Telegram用户
    // 我们仍然允许他们使用分享助力链接，但需要创建一个临时的用户记录
    const result = await jackpotChestService.useTelegramShareBoostLink(code, userId, telegramId!, isPremium || false);

    return res.json({
      ok: true,
      data: result
    });
  } catch (error: any) {
    console.error("使用Telegram分享助力链接失败:", error);
    return res.status(400).json({
      ok: false,
      message: error.message || t('errors.serverError')
    });
  }
}



/**
 * 获取一次性4个宝箱
 * 每个用户只能获取一次
 */
export async function collectFourChests(req: Request, res: Response) {
  const transaction = await sequelize.transaction();
  try {
    const myReq = req as MyRequest;
    const { walletId, userId } = myReq.user!;

    const result = await jackpotChestService.collectFourChests(userId, walletId!, transaction);

    await transaction.commit();
    return res.json({
      ok: true,
      data: {
        // 使用合并后的结果作为主要数据返回给前端
        result: result.mergedResult
      }
    });
  } catch (error: any) {
    await transaction.rollback();
    console.error("获取一次性4个宝箱失败:", error);
    return res.status(400).json({
      ok: false,
      message: error.message || t('errors.serverError')
    });
  }
}


/**
 * 获取四个宝箱领取状态
 */
export async function getFourChestsCollectStatus(req: Request, res: Response) {
  try {
    const myReq = req as MyRequest;
    const { walletId, userId } = myReq.user!;

    const status = await jackpotChestService.getFourChestsCollectStatus(userId, walletId!);

    return res.json({
      ok: true,
      data: status
    });
  } catch (error: any) {
    console.error("获取四个宝箱领取状态失败:", error);
    return res.status(400).json({
      ok: false,
      message: error.message || t('errors.serverError')
    });
  }
}



/**
 * 创建Star会员支付链接
 * 此功能允许Telegram用户购买Star会员资格
 */
export async function createStarInvoiceLink(req: Request, res: Response) {
  try {
    const myReq = req as MyRequest;
    const { walletId, userId } = myReq.user!;

    console.log(walletId, userId);
    

    // 创建支付链接
    const price = 1; // 5 XTR
    const paymentId = `star_${randomUUID()}-${walletId}_${userId}`;

    const title = "Moo Fun Star会员";
    const productDescription = "解锁自动领取宝箱等特权功能";
    const payload = JSON.stringify({ userId, walletId, paymentId, productType: 'star_membership' });
    const provider_token = ""; // 预留的支付提供商Token
    const currency = "XTR";
    const prices = [{ amount: price, label: "Star Membership" }];

    const invoiceLink = await bot.api.createInvoiceLink(
      title,
      productDescription,
      payload,
      provider_token,
      currency,
      prices
    );

    await jackpotChestService.savePaymentRequest(paymentId, walletId!, userId, price, 'star_membership');

    return res.json({
      ok: true,
      data: {
        invoiceLink,
        paymentId,
        amount: price,
        currency: 'XTR'
      }
    });
  } catch (error: any) {
    console.error("创建Star会员支付链接失败:", error);
    return res.status(500).json({
      ok: false,
      message: error.message || t('errors.serverError')
    });
  }
}


/**
 * 处理支付回调
 * 当用户完成支付后，Telegram会调用此接口
 */
export async function handlePaymentCallback(req: Request, res: Response) {
  try {
    // 获取支付回调数据
    const { payment_id, status, telegram_id } = req.body;

    if (!payment_id || !status || !telegram_id) {
      return res.status(400).json({
        ok: false,
        message: "缺少必要的支付信息"
      });
    }

    // 验证支付ID格式
    if (!payment_id.startsWith('star_')) {
      return res.status(400).json({
        ok: false,
        message: "无效的支付ID"
      });
    }

    // 在实际实现中，应该从数据库中验证支付信息
    const paymentInfo = await jackpotChestService.verifyPaymentRequest(payment_id, telegram_id);
    if (!paymentInfo) {
      return res.status(400).json({
        ok: false,
        message: "支付信息验证失败"
      });
    }

    if (status === 'success') {
      // 支付成功，更新用户的Star会员状态
      // 查找用户钱包
      const user = await User.findOne({ where: { telegramId: telegram_id } });

      if (user) {
        const userWallet = await UserWallet.findOne({ where: { userId: user.id } });

        if (userWallet) {
          // 更新用户的Star会员状态
          await userWallet.update({ isStar: true });

          return res.json({
            ok: true,
            message: "支付成功，已更新用户的Star会员状态"
          });
        }
      }

      // 用户不存在或钱包不存在
      return res.status(404).json({
        ok: false,
        message: "未找到用户或钱包信息"
      });
    } else {
      // 支付失败
      return res.status(400).json({
        ok: false,
        message: "支付失败"
      });
    }
  } catch (error: any) {
    console.error("处理支付回调失败:", error);
    return res.status(500).json({
      ok: false,
      message: error.message || t('errors.serverError')
    });
  }
}
