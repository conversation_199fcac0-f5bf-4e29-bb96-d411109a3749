// src/controllers/phrsDepositController.ts
import { Request, Response } from 'express';
import { phrsDepositService } from '../services/phrsDepositService';
import { phrsDepositMonitor } from '../jobs/phrsDepositMonitor';
import { phrsBalanceMonitor } from '../services/phrsBalanceMonitor';
import { PhrsDeposit, UserWallet } from '../models';
import { MyRequest } from '../types/customRequest';
import { tFromRequest } from '../i18n';
import { Op } from 'sequelize';
import BigNumber from 'bignumber.js';

/**
 * PHRS充值控制器
 */
export class PhrsDepositController {
  
  /**
   * 获取用户PHRS充值记录
   */
  public async getUserDeposits(req: MyRequest, res: Response): Promise<void> {
    try {
      const walletId = req.user?.walletId;
      if (!walletId) {
        res.status(401).json({
          ok: false,
          message: tFromRequest(req, 'errors.unauthorized')
        });
        return;
      }

      const { page = 1, limit = 20, status } = req.query;
      const offset = (Number(page) - 1) * Number(limit);

      const whereCondition: any = { walletId };
      if (status) {
        whereCondition.status = status;
      }

      const { count, rows: deposits } = await PhrsDeposit.findAndCountAll({
        where: whereCondition,
        order: [['createdAt', 'DESC']],
        limit: Number(limit),
        offset,
        include: [{
          model: UserWallet,
          attributes: ['id', 'walletAddress']
        }]
      });

      res.json({
        ok: true,
        data: {
          deposits: deposits.map(deposit => ({
            id: deposit.id,
            amount: deposit.amount,
            userAddress: deposit.userAddress,
            transactionHash: deposit.transactionHash,
            blockNumber: deposit.blockNumber,
            blockTimestamp: deposit.blockTimestamp,
            status: deposit.status,
            confirmations: deposit.confirmations,
            processedAt: deposit.processedAt,
            createdAt: deposit.createdAt
          })),
          pagination: {
            total: count,
            page: Number(page),
            limit: Number(limit),
            totalPages: Math.ceil(count / Number(limit))
          }
        }
      });

    } catch (error: any) {
      console.error('获取用户PHRS充值记录失败:', error);
      res.status(500).json({
        ok: false,
        message: tFromRequest(req, 'errors.serverError'),
        details: error.message
      });
    }
  }

  /**
   * 绑定PHRS钱包地址
   */
  public async bindPhrsWallet(req: MyRequest, res: Response): Promise<void> {
    try {
      const walletId = req.user?.walletId;
      if (!walletId) {
        res.status(401).json({
          ok: false,
          message: tFromRequest(req, 'errors.unauthorized')
        });
        return;
      }

      const { phrsWalletAddress } = req.body;
      
      if (!phrsWalletAddress || !/^0x[a-fA-F0-9]{40}$/.test(phrsWalletAddress)) {
        res.status(400).json({
          ok: false,
          message: tFromRequest(req, 'errors.invalidWalletAddress')
        });
        return;
      }

      // 检查地址是否已被其他用户绑定
      const existingWallet = await UserWallet.findOne({
        where: {
          phrsWalletAddress: phrsWalletAddress.toLowerCase(),
          id: { [Op.ne]: walletId }
        }
      });

      if (existingWallet) {
        res.status(400).json({
          ok: false,
          message: tFromRequest(req, 'errors.walletAddressAlreadyBound')
        });
        return;
      }

      // 更新用户钱包
      const userWallet = await UserWallet.findByPk(walletId);
      if (!userWallet) {
        res.status(404).json({
          ok: false,
          message: tFromRequest(req, 'errors.userWalletNotFound')
        });
        return;
      }

      await userWallet.update({
        phrsWalletAddress: phrsWalletAddress.toLowerCase(),
        lastPhrsUpdateTime: new Date()
      });

      // 同步该地址的PHRS余额
      try {
        await phrsDepositService.syncUserBalance(phrsWalletAddress.toLowerCase());
      } catch (syncError) {
        console.error('同步PHRS余额失败:', syncError);
        // 不影响绑定操作
      }

      res.json({
        ok: true,
        message: tFromRequest(req, 'success.phrsWalletBound'),
        data: {
          phrsWalletAddress: phrsWalletAddress.toLowerCase(),
          phrsBalance: userWallet.phrsBalance
        }
      });

    } catch (error: any) {
      console.error('绑定PHRS钱包地址失败:', error);
      res.status(500).json({
        ok: false,
        message: tFromRequest(req, 'errors.serverError'),
        details: error.message
      });
    }
  }

  /**
   * 手动同步PHRS余额
   */
  public async syncPhrsBalance(req: MyRequest, res: Response): Promise<void> {
    try {
      const walletId = req.user?.walletId;
      if (!walletId) {
        res.status(401).json({
          ok: false,
          message: tFromRequest(req, 'errors.unauthorized')
        });
        return;
      }

      const userWallet = await UserWallet.findByPk(walletId);
      if (!userWallet || !userWallet.phrsWalletAddress) {
        res.status(400).json({
          ok: false,
          message: tFromRequest(req, 'errors.phrsWalletNotBound')
        });
        return;
      }

      await phrsDepositService.syncUserBalance(userWallet.phrsWalletAddress);

      // 重新获取更新后的余额
      await userWallet.reload();

      res.json({
        ok: true,
        message: tFromRequest(req, 'success.phrsBalanceSynced'),
        data: {
          phrsBalance: userWallet.phrsBalance,
          lastPhrsUpdateTime: userWallet.lastPhrsUpdateTime
        }
      });

    } catch (error: any) {
      console.error('同步PHRS余额失败:', error);
      res.status(500).json({
        ok: false,
        message: tFromRequest(req, 'errors.serverError'),
        details: error.message
      });
    }
  }

  /**
   * 获取PHRS充值统计信息（管理员接口）
   */
  public async getDepositStatistics(req: Request, res: Response): Promise<void> {
    try {
      // 这里应该添加管理员权限检查
      
      const { startDate, endDate } = req.query;
      
      const whereCondition: any = {};
      if (startDate && endDate) {
        whereCondition.createdAt = {
          [Op.between]: [new Date(startDate as string), new Date(endDate as string)]
        };
      }

      // 按状态统计
      const statusStats = await PhrsDeposit.findAll({
        attributes: [
          'status',
          [PhrsDeposit.sequelize!.fn('COUNT', PhrsDeposit.sequelize!.col('id')), 'count'],
          [PhrsDeposit.sequelize!.fn('SUM', PhrsDeposit.sequelize!.col('amount')), 'totalAmount']
        ],
        where: whereCondition,
        group: ['status'],
        raw: true
      });

      // 每日统计
      const dailyStats = await PhrsDeposit.findAll({
        attributes: [
          [PhrsDeposit.sequelize!.fn('DATE', PhrsDeposit.sequelize!.col('createdAt')), 'date'],
          [PhrsDeposit.sequelize!.fn('COUNT', PhrsDeposit.sequelize!.col('id')), 'count'],
          [PhrsDeposit.sequelize!.fn('SUM', PhrsDeposit.sequelize!.col('amount')), 'totalAmount']
        ],
        where: {
          ...whereCondition,
          status: 'CONFIRMED'
        },
        group: [PhrsDeposit.sequelize!.fn('DATE', PhrsDeposit.sequelize!.col('createdAt'))],
        order: [[PhrsDeposit.sequelize!.fn('DATE', PhrsDeposit.sequelize!.col('createdAt')), 'DESC']],
        limit: 30,
        raw: true
      });

      // 总体统计
      const totalStats = await PhrsDeposit.findOne({
        attributes: [
          [PhrsDeposit.sequelize!.fn('COUNT', PhrsDeposit.sequelize!.col('id')), 'totalCount'],
          [PhrsDeposit.sequelize!.fn('SUM', PhrsDeposit.sequelize!.col('amount')), 'totalAmount']
        ],
        where: {
          ...whereCondition,
          status: 'CONFIRMED'
        },
        raw: true
      });

      res.json({
        ok: true,
        data: {
          statusStats,
          dailyStats,
          totalStats,
          serviceStatus: phrsDepositService.getStatus(),
          monitorStatus: phrsDepositMonitor.getStatus()
        }
      });

    } catch (error: any) {
      console.error('获取PHRS充值统计失败:', error);
      res.status(500).json({
        ok: false,
        message: 'Failed to get deposit statistics',
        details: error.message
      });
    }
  }

  /**
   * 启动/停止监控服务（管理员接口）
   */
  public async toggleMonitorService(req: Request, res: Response): Promise<void> {
    try {
      // 这里应该添加管理员权限检查
      
      const { action } = req.body; // 'start' 或 'stop'
      
      if (action === 'start') {
        phrsDepositMonitor.start();
        res.json({
          ok: true,
          message: 'PHRS deposit monitor started',
          data: phrsDepositMonitor.getStatus()
        });
      } else if (action === 'stop') {
        phrsDepositMonitor.stop();
        res.json({
          ok: true,
          message: 'PHRS deposit monitor stopped',
          data: phrsDepositMonitor.getStatus()
        });
      } else {
        res.status(400).json({
          ok: false,
          message: 'Invalid action. Use "start" or "stop"'
        });
      }

    } catch (error: any) {
      console.error('切换监控服务状态失败:', error);
      res.status(500).json({
        ok: false,
        message: 'Failed to toggle monitor service',
        details: error.message
      });
    }
  }

  /**
   * 获取余额监控状态
   */
  public async getBalanceMonitorStatus(req: Request, res: Response): Promise<void> {
    try {
      const status = phrsBalanceMonitor.getStatus();

      res.json({
        ok: true,
        message: 'Balance monitor status retrieved',
        data: status
      });

    } catch (error: any) {
      console.error('获取余额监控状态失败:', error);
      res.status(500).json({
        ok: false,
        message: 'Failed to get balance monitor status',
        details: error.message
      });
    }
  }

  /**
   * 手动执行余额检查
   */
  public async runManualBalanceCheck(req: Request, res: Response): Promise<void> {
    try {
      // 这里应该添加管理员权限检查

      await phrsBalanceMonitor.runManualCheck();

      res.json({
        ok: true,
        message: 'Manual balance check completed',
        data: {
          timestamp: new Date().toISOString(),
          status: 'completed'
        }
      });

    } catch (error: any) {
      console.error('手动余额检查失败:', error);
      res.status(500).json({
        ok: false,
        message: 'Failed to run manual balance check',
        details: error.message
      });
    }
  }
}

export const phrsDepositController = new PhrsDepositController();
