// src/controllers/phrsPaymentController.ts
import { Request, Response } from 'express';
import { IapProduct, IapPurchase, UserWallet, Booster, ActiveBooster, VipMembership } from '../models';
import { MyRequest } from '../types/customRequest';
import { tFromRequest } from '../i18n';
import { sequelize } from '../config/db';
import BigNumber from 'bignumber.js';
import dayjs from 'dayjs';
import { Op, QueryTypes } from 'sequelize';
import { TimeWarpService } from '../services/timeWarpService';

/**
 * PHRS支付控制器
 * 处理使用PHRS余额购买IAP道具的逻辑
 */
export class PhrsPaymentController {

  /**
   * 使用PHRS购买IAP道具
   * POST /api/phrs-payment/purchase
   */
  public async purchaseWithPhrs(req: MyRequest, res: Response): Promise<void> {
    const transaction = await sequelize.transaction();
    
    try {
      const walletId = req.user?.walletId;
      if (!walletId) {
        res.status(401).json({
          ok: false,
          message: '用户未认证',
          error: 'UNAUTHORIZED'
        });
        return;
      }

      const { productId } = req.body;

      if (!productId || typeof productId !== 'number') {
        res.status(400).json({
          ok: false,
          message: '请提供有效的商品ID',
          error: 'INVALID_PRODUCT_ID',
          details: {
            received: productId,
            expected: 'number'
          }
        });
        return;
      }

      // 获取用户钱包
      const wallet = await UserWallet.findByPk(walletId, { transaction });
      if (!wallet) {
        await transaction.rollback();
        res.status(404).json({
          ok: false,
          message: '用户钱包不存在',
          error: 'USER_WALLET_NOT_FOUND'
        });
        return;
      }

      // 获取商品信息
      const product = await IapProduct.findByPk(productId, { transaction });
      if (!product || !product.isActive) {
        await transaction.rollback();
        res.status(404).json({
          ok: false,
          message: '商品不存在或已下架',
          error: 'PRODUCT_NOT_FOUND'
        });
        return;
      }

      // 检查商品是否支持PHRS支付
      if (!product.pricePhrs) {
        await transaction.rollback();
        res.status(400).json({
          ok: false,
          message: '该商品不支持PHRS支付',
          error: 'PHRS_PAYMENT_NOT_SUPPORTED'
        });
        return;
      }

      // 使用PHRS价格
      const phrsPrice = new BigNumber(product.pricePhrs.toString());
      const userPhrsBalance = new BigNumber(wallet.phrsBalance?.toString() || '0');

      // 检查余额是否足够
      if (userPhrsBalance.isLessThan(phrsPrice)) {
        await transaction.rollback();
        res.status(400).json({
          ok: false,
          message: 'PHRS余额不足',
          error: 'INSUFFICIENT_PHRS_BALANCE',
          data: {
            required: phrsPrice.toFixed(3),
            available: userPhrsBalance.toFixed(3),
            shortfall: phrsPrice.minus(userPhrsBalance).toFixed(3)
          }
        });
        return;
      }

      // 检查购买限制
      const purchaseLimitCheck = await this.checkPurchaseLimit(walletId, product, transaction);
      if (!purchaseLimitCheck.allowed) {
        await transaction.rollback();

        // 根据错误类型返回相应的中文错误消息
        let errorMessage = '';
        const limitMessage = purchaseLimitCheck.message || '';
        if (limitMessage.includes('Daily purchase limit')) {
          errorMessage = '已达到每日购买限制，请明天再试';
        } else if (limitMessage.includes('Account purchase limit')) {
          errorMessage = '已达到账号购买限制，该商品只能购买一次';
        } else if (limitMessage.includes('VIP membership already active')) {
          errorMessage = 'VIP会员已激活，无法重复购买';
        } else {
          errorMessage = '购买限制：' + limitMessage;
        }

        res.status(400).json({
          ok: false,
          message: errorMessage,
          error: 'PURCHASE_LIMIT_EXCEEDED',
          details: {
            limitType: limitMessage.includes('Daily') ? 'daily' :
                      limitMessage.includes('Account') ? 'account' :
                      limitMessage.includes('VIP') ? 'vip' : 'unknown',
            productName: product.name,
            productType: product.type
          }
        });
        return;
      }

      // 使用原子操作扣减PHRS余额
      const phrsAmountStr = phrsPrice.toFixed(18);

      const updateResult = await sequelize.query(
        `UPDATE user_wallets
         SET phrsBalance = phrsBalance - :phrsAmount,
             lastPhrsUpdateTime = :updateTime,
             updatedAt = :updateTime
         WHERE id = :walletId AND phrsBalance >= :phrsAmount`,
        {
          replacements: {
            phrsAmount: phrsAmountStr,
            updateTime: new Date(),
            walletId: walletId
          },
          type: QueryTypes.UPDATE,
          transaction
        }
      );

      // MySQL UPDATE 查询返回 [results, metadata]，metadata 包含 affectedRows
      const affectedRows = Array.isArray(updateResult) ? updateResult[1] : 0;

      if (affectedRows === 0) {
        await transaction.rollback();
        res.status(400).json({
          ok: false,
          message: tFromRequest(req, 'errors.iap.insufficientPhrsBalance'),
          data: {
            required: phrsPrice.toFixed(18),
            available: userPhrsBalance.toFixed(18)
          }
        });
        return;
      }

      // 获取更新后的余额
      const updatedWallet = await UserWallet.findByPk(walletId, {
        attributes: ['phrsBalance'],
        transaction
      });
      const newBalance = new BigNumber(updatedWallet?.phrsBalance?.toString() || '0');

      // 创建购买记录
      const purchase = await IapPurchase.create({
        walletId,
        productId: product.id,
        paymentId: `phrs_${Date.now()}_${walletId}`, // 生成唯一的支付ID
        paymentMethod: 'phrs',
        amount: phrsPrice.toNumber(),
        currency: 'PHRS',
        status: 'FINALIZED', // PHRS支付直接完成
        statusChecked: true,
        purchaseDate: dayjs().toDate()
      }, { transaction });

      // 发放商品并获取详细结果
      const fulfillmentResult = await this.fulfillPurchase(purchase, product, wallet, transaction);

      await transaction.commit();

      // 构建购买结果信息
      let purchaseResult: any = {
        purchaseId: purchase.id,
        productName: product.name,
        productType: product.type,
        phrsPaid: phrsPrice.toFixed(3),
        remainingBalance: newBalance.toFixed(3),
        product: {
          id: product.id,
          name: product.name,
          type: product.type,
          duration: product.duration,
          multiplier: product.multiplier
        }
      };

      // 根据商品发放结果添加详细信息
      if (fulfillmentResult) {
        if (fulfillmentResult.type === 'time_warp' && fulfillmentResult.timeWarpResult) {
          const result = fulfillmentResult.timeWarpResult;
          purchaseResult.message = `时间跳跃${fulfillmentResult.hours}小时已完成`;
          purchaseResult.rewards = {
            gemsEarned: result.gemsEarned,
            milkProduced: result.milkProduced,
            milkProcessed: result.milkProcessed,
            farmProductionPerSecond: result.farmProductionPerSecond,
            deliveryProcessingPerSecond: result.deliveryProcessingPerSecond,
            hasVip: result.hasVip,
            hasSpeedBoost: result.hasSpeedBoost,
            speedBoostMultiplier: result.speedBoostMultiplier
          };
          purchaseResult.summary = `获得了 ${result.gemsEarned} GEM，生产了 ${result.milkProduced} 牛奶，处理了 ${result.milkProcessed} 牛奶`;
        } else if (fulfillmentResult.type === 'special_offer' && fulfillmentResult.timeWarpResult) {
          const result = fulfillmentResult.timeWarpResult;
          purchaseResult.message = `特殊套餐已购买，时间跳跃${fulfillmentResult.hours}小时已完成，速度加成道具已添加到背包`;
          purchaseResult.rewards = {
            gemsEarned: result.gemsEarned,
            milkProduced: result.milkProduced,
            milkProcessed: result.milkProcessed,
            farmProductionPerSecond: result.farmProductionPerSecond,
            deliveryProcessingPerSecond: result.deliveryProcessingPerSecond,
            hasVip: result.hasVip,
            hasSpeedBoost: result.hasSpeedBoost,
            speedBoostMultiplier: result.speedBoostMultiplier
          };
          purchaseResult.summary = `获得了 ${result.gemsEarned} GEM，生产了 ${result.milkProduced} 牛奶，处理了 ${result.milkProcessed} 牛奶`;
        } else if (fulfillmentResult.type === 'vip_membership') {
          purchaseResult.message = `VIP会员已激活，持续${fulfillmentResult.durationDays}天`;
        } else if (fulfillmentResult.type === 'speed_boost') {
          purchaseResult.message = `速度加成道具已添加到背包，${product.multiplier}倍速度持续${product.duration}小时`;
        }
      }

      res.json({
        ok: true,
        message: '购买成功',
        data: purchaseResult
      });

    } catch (error: any) {
      await transaction.rollback();
      console.error('PHRS购买失败:', error);
      res.status(500).json({
        ok: false,
        message: '服务器内部错误，请稍后重试',
        error: 'INTERNAL_SERVER_ERROR',
        details: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  }

  /**
   * 获取用户PHRS余额和购买历史
   * GET /api/phrs-payment/balance
   */
  public async getPhrsBalance(req: MyRequest, res: Response): Promise<void> {
    try {
      const walletId = req.user?.walletId;
      if (!walletId) {
        res.status(401).json({
          ok: false,
          message: tFromRequest(req, 'errors.unauthorized')
        });
        return;
      }

      const wallet = await UserWallet.findByPk(walletId);
      if (!wallet) {
        res.status(404).json({
          ok: false,
          message: tFromRequest(req, 'errors.userWalletNotFound')
        });
        return;
      }

      // 获取PHRS购买历史
      const phrsePurchases = await IapPurchase.findAll({
        where: {
          walletId,
          paymentMethod: 'phrs',
          status: 'FINALIZED'
        },
        order: [['createdAt', 'DESC']],
        limit: 20
      });

      // 获取相关产品信息
      const productIds = phrsePurchases.map(p => p.productId);
      const products = await IapProduct.findAll({
        where: { id: productIds },
        attributes: ['id', 'name', 'type']
      });
      const productMap = new Map(products.map(p => [p.id, p]));

      res.json({
        ok: true,
        data: {
          phrsBalance: wallet.phrsBalance || '0',
          phrsWalletAddress: wallet.phrsWalletAddress,
          lastPhrsUpdateTime: wallet.lastPhrsUpdateTime,
          recentPurchases: phrsePurchases.map(purchase => {
            const product = productMap.get(purchase.productId);
            return {
              id: purchase.id,
              productName: product?.name,
              productType: product?.type,
              amount: purchase.amount,
              purchaseDate: purchase.purchaseDate,
              createdAt: purchase.createdAt
            };
          })
        }
      });

    } catch (error: any) {
      console.error('获取PHRS余额失败:', error);
      res.status(500).json({
        ok: false,
        message: tFromRequest(req, 'errors.serverError'),
        details: error.message
      });
    }
  }

  /**
   * 获取支持PHRS支付的商品列表
   * GET /api/phrs-payment/products
   */
  public async getPhrsProducts(req: MyRequest, res: Response): Promise<void> {
    try {
      const products = await IapProduct.findAll({
        where: {
          isActive: true
        },
        order: [['id', 'ASC']]
      });

      // 过滤出支持PHRS支付的商品
      const phrsProducts = products.filter(product =>
        product.pricePhrs != null
      );

      res.json({
        ok: true,
        data: {
          products: phrsProducts.map(product => ({
            id: product.id,
            productId: product.productId,
            name: product.name,
            type: product.type,
            phrsPrice: product.pricePhrs, // 只使用PHRS价格
            priceUsd: product.priceUsd,
            multiplier: product.multiplier,
            duration: product.duration,
            quantity: product.quantity,
            dailyLimit: product.dailyLimit,
            accountLimit: product.accountLimit,
            description: product.description,
            config: product.config
          }))
        }
      });

    } catch (error: any) {
      console.error('获取PHRS商品列表失败:', error);
      res.status(500).json({
        ok: false,
        message: tFromRequest(req, 'errors.serverError'),
        details: error.message
      });
    }
  }

  /**
   * 检查购买限制
   */
  private async checkPurchaseLimit(
    walletId: number,
    product: IapProduct,
    transaction: any
  ): Promise<{ allowed: boolean; message?: string }> {
    const today = dayjs().startOf('day').toDate();
    const tomorrow = dayjs().endOf('day').toDate();

    // 检查每日限制
    if (product.dailyLimit > 0) {
      const todayPurchases = await IapPurchase.count({
        where: {
          walletId,
          productId: product.id,
          status: 'FINALIZED',
          createdAt: {
            [Op.between]: [today, tomorrow]
          }
        },
        transaction
      });

      if (todayPurchases >= product.dailyLimit) {
        return {
          allowed: false,
          message: 'Daily purchase limit exceeded'
        };
      }
    }

    // 检查账号限制
    if (product.accountLimit && product.accountLimit > 0) {
      const totalPurchases = await IapPurchase.count({
        where: {
          walletId,
          productId: product.id,
          status: 'FINALIZED'
        },
        transaction
      });

      if (totalPurchases >= product.accountLimit) {
        return {
          allowed: false,
          message: 'Account purchase limit exceeded'
        };
      }
    }

    // VIP会员特殊检查：如果用户已经是VIP会员，将不可以重复购买
    if (product.type === 'vip_membership') {
      const activeVip = await VipMembership.findOne({
        where: {
          walletId,
          isActive: true,
          endTime: { [Op.gt]: new Date() }
        },
        transaction
      });

      if (activeVip) {
        return {
          allowed: false,
          message: 'VIP membership already active, cannot purchase again'
        };
      }
    }

    return { allowed: true };
  }

  /**
   * 发放购买的商品
   */
  private async fulfillPurchase(
    purchase: IapPurchase,
    product: IapProduct,
    wallet: UserWallet,
    transaction: any
  ): Promise<any> {
    const walletId = wallet.id;

    switch (product.type) {
      case 'speed_boost':
        // 速度加成道具：限购一天一次，添加到背包
        const existingSpeedBooster = await Booster.findOne({
          where: {
            walletId,
            type: product.type,
            multiplier: product.multiplier,
            duration: product.duration
          },
          transaction
        });

        if (existingSpeedBooster) {
          existingSpeedBooster.quantity += (product.quantity || 1);
          await existingSpeedBooster.save({ transaction });
        } else {
          await Booster.create({
            walletId,
            type: product.type,
            multiplier: product.multiplier || 1,
            duration: product.duration || 3600,
            quantity: product.quantity || 1
          }, { transaction });
        }
        return { type: 'speed_boost' };

      case 'time_warp':
        // 时间跳跃：限购一天一次，购买后直接跳跃时间，直接获得对应时间的牛奶产量和方块收益（GEM）
        const timeWarpResult = await TimeWarpService.executeTimeWarp(walletId, product.duration || 1, transaction, product.id, purchase.id);
        return {
          type: 'time_warp',
          timeWarpResult,
          hours: product.duration || 1
        };

      case 'vip_membership':
        // VIP会员：如果用户已经是VIP会员，将不可以重复购买，购买后用户将成为VIP会员持续1个月
        const config = product.config as any;
        const durationDays = config?.durationDays || 30;

        await VipMembership.create({
          walletId,
          startTime: new Date(),
          endTime: dayjs().add(durationDays, 'day').toDate(),
          isActive: true
        }, { transaction });
        return { type: 'vip_membership', durationDays };

      case 'special_offer':
        // 特殊套餐：限购一账号一次，包含时间跳跃和其他道具
        const offerConfig = product.config as any;
        let specialOfferResult: any = { type: 'special_offer' };

        if (offerConfig?.bundle) {
          // 处理套餐中的每个道具
          for (const item of offerConfig.bundle) {
            if (item.type === 'time_warp' && item.autoUse) {
              // 时间跳跃道具直接使用，跳跃总时长 = duration * quantity
              const totalHours = (item.duration || 1) * (item.quantity || 1);
              const timeWarpResult = await TimeWarpService.executeTimeWarp(walletId, totalHours, transaction, product.id, purchase.id);
              specialOfferResult.timeWarpResult = timeWarpResult;
              specialOfferResult.hours = totalHours;
            } else if (item.type === 'speed_boost' && !item.autoUse) {
              // 速度加成道具添加到背包
              const existingBooster = await Booster.findOne({
                where: {
                  walletId,
                  type: item.type,
                  multiplier: item.multiplier,
                  duration: item.duration
                },
                transaction
              });

              if (existingBooster) {
                existingBooster.quantity += (item.quantity || 1);
                await existingBooster.save({ transaction });
              } else {
                await Booster.create({
                  walletId,
                  type: item.type,
                  multiplier: item.multiplier || 1,
                  duration: item.duration || 3600,
                  quantity: item.quantity || 1
                }, { transaction });
              }
            }
          }
        }

        // 处理直接的宝石奖励（如果有）
        if (offerConfig?.gems) {
          const currentGems = new BigNumber(wallet.gem?.toString() || '0');
          const newGems = currentGems.plus(new BigNumber(offerConfig.gems));
          await wallet.update({ gem: newGems.toFixed(3) }, { transaction });
          specialOfferResult.directGems = offerConfig.gems;
        }

        return specialOfferResult;

      default:
        console.warn(`未知的商品类型: ${product.type}`);
        return { type: 'unknown' };
    }
  }
}

export const phrsPaymentController = new PhrsPaymentController();
