// src/controllers/walletController.ts
import { Request, Response } from "express";
import { UserWallet } from "../models/UserWallet";
import { WalletHistory } from "../models/WalletHistory";
import { DeliveryLine } from "../models/DeliveryLine";
import { MyRequest } from "../types/customRequest";
import { tFromRequest } from "../i18n";
import { successResponse, errorResponse } from "../utils/responseUtil";
import { sequelize } from "../config/db";
import { Transaction } from "sequelize";
import { createBigNumber, formatToThreeDecimalsNumber } from "../utils/bigNumberConfig";

/**
 * 增加待处理牛奶
 * @param req 请求对象
 * @param res 响应对象
 */
export const increaseMilk = async (req: Request, res: Response) => {
    const transaction: Transaction = await sequelize.transaction();
    try {
        const myReq = req as MyRequest;
        const { walletId, userId } = myReq.user!;

        // 验证请求参数
        const { pendingMilk } = req.body;

        if (pendingMilk === undefined || isNaN(Number(pendingMilk)) || Number(pendingMilk) < 0) {
            res.status(400).json(errorResponse(
                tFromRequest(req, "errors.invalidAmount"),
                tFromRequest(req, "errors.pendingMilkAmountMustBeNonNegative")
            ));
            return;
        }

        // 查找用户钱包
        const wallet = await UserWallet.findOne({
            where: { id: walletId, userId },
            transaction
        });

        if (!wallet) {
            await transaction.rollback();
            res.status(404).json(errorResponse(
                tFromRequest(req, "errors.walletNotExist"),
                tFromRequest(req, "errors.userWalletNotFound")
            ));
            return;
        }

        // 更新DeliveryLine中的pendingMilk
        const pendingMilkAmount = Number(pendingMilk);
        let updatedPendingMilk = 0;
        
        const deliveryLine = await DeliveryLine.findOne({
            where: { walletId },
            transaction
        });

        if (deliveryLine) {
            deliveryLine.pendingMilk = (deliveryLine.pendingMilk || 0) + pendingMilkAmount;
            await deliveryLine.save({ transaction });
            updatedPendingMilk = deliveryLine.pendingMilk;
        } else {
            // 如果没有找到DeliveryLine记录，可以创建一个新的
            const newDeliveryLine = await DeliveryLine.create({
                walletId: walletId!,
                pendingMilk: pendingMilkAmount,
                level: 0,
                deliverySpeed: 0,
                blockUnit: 0,
                blockPrice: 0,
                upgradeCost: 0,
                lastDeliveryTime: new Date(),
                pendingBlocks: 0
            }, { transaction });
            updatedPendingMilk = newDeliveryLine.pendingMilk;
        }
        await transaction.commit();

        res.json(successResponse(
            { pendingMilk: updatedPendingMilk },
            tFromRequest(req, "success.increaseMilk")
        ));
        return;
    } catch (err: any) {
        await transaction.rollback();
        console.error('增加待处理牛奶失败:', err);
        res.status(500).json(errorResponse(
            tFromRequest(req, "errors.increaseMilkFailed"),
            err.message
        ));
        return
    }
};

/**
 * 将牛奶兑换为宝石
 * @param req 请求对象
 * @param res 响应对象
 */
export const increaseGem = async (req: Request, res: Response) => {
    // 立即更新用户活跃时间（无论请求是否成功）
    const myReq = req as MyRequest;
    const { walletId, userId } = myReq.user!;
    
    try {
        // 优先更新最后活跃时间，确保用户被认为在线
        await UserWallet.update(
            { lastActiveTime: new Date() },
            { where: { id: walletId, userId } }
        );
    } catch (err) {
        // 忽略更新活跃时间的错误，继续处理主要业务逻辑
        console.log('更新用户活跃时间失败:', err);
    }

    const transaction: Transaction = await sequelize.transaction();
    try {
        // 验证请求参数
        const { milkAmount } = req.body;

        if (milkAmount === undefined || isNaN(Number(milkAmount)) || Number(milkAmount) <= 0) {
            res.status(400).json(errorResponse(
                tFromRequest(req, "errors.invalidAmount"),
                tFromRequest(req, "errors.milkAmountMustBePositive")
            ));
            return;
        }

        // 查找用户钱包
        const wallet = await UserWallet.findOne({
            where: { id: walletId, userId },
            transaction
        });

        if (!wallet) {
            await transaction.rollback();
            res.status(404).json(errorResponse(
                tFromRequest(req, "errors.walletNotExist"),
                tFromRequest(req, "errors.userWalletNotFound")
            ));
            return;
        }

        // 查找用户的DeliveryLine
        const deliveryLine = await DeliveryLine.findOne({
            where: { walletId },
            transaction
        });

        if (!deliveryLine) {
            await transaction.rollback();
            res.status(404).json(errorResponse(
                tFromRequest(req, "errors.deliveryLineNotExist"),
                tFromRequest(req, "errors.deliveryLineNotFound")
            ));
            return;
        }

        // 检查用户是否有足够的待处理牛奶
        const requestedMilkAmount = Number(milkAmount);
        if (deliveryLine.pendingMilk < requestedMilkAmount) {
            await transaction.rollback();
            console.warn(`牛奶兑换宝石失败: 待处理牛奶不足 - walletId: ${walletId}, 需要: ${requestedMilkAmount}, 可用: ${deliveryLine.pendingMilk}`);
            res.status(400).json(errorResponse(
                tFromRequest(req, "errors.insufficientMilk"),
                tFromRequest(req, "errors.notEnoughPendingMilk")
            ));
            return;
        }

        // 使用1:1的兑换比例，直接将牛奶等量转换为宝石
        const gemAmount = requestedMilkAmount;
        const milkUsed = requestedMilkAmount;
    
        // 更新待处理牛奶数量
        deliveryLine.pendingMilk -= milkUsed;
        await deliveryLine.save({ transaction });

        // 更新钱包宝石值（lastActiveTime已经在函数开始时更新了）
        const currentGemBN = createBigNumber(wallet.gem || 0);
        const gemAmountBN = createBigNumber(gemAmount);
        const newGemBN = currentGemBN.plus(gemAmountBN);
        wallet.gem = newGemBN.toFixed(3);
        await wallet.save({ transaction });

        // 创建钱包历史记录 - 牛奶消耗
        await WalletHistory.create({
            userId,
            walletId: walletId!,
            currency: "MILK",
            amount: -milkUsed,
            reference: tFromRequest(req, "walletHistory.reference.convertMilkToGem"),
            action: "OUT",
            category: "MILK_TO_GEM",
            credit_type: "CONVERSION",
            fe_display_remark: tFromRequest(req, "walletHistory.feDisplayRemark.milkConvertedToGem", { amount: milkUsed }),
            developer_remark: `将牛奶兑换为宝石: ${milkUsed} 牛奶`
        }, { transaction });

        // 创建钱包历史记录 - 宝石增加
        await WalletHistory.create({
            userId,
            walletId: walletId!,
            currency: "GEM",
            amount: gemAmount,
            reference: tFromRequest(req, "walletHistory.reference.increaseGem"),
            action: "IN",
            category: "MILK_TO_GEM",
            credit_type: "CONVERSION",
            fe_display_remark: tFromRequest(req, "walletHistory.feDisplayRemark.increasedGem", { amount: gemAmount }),
            developer_remark: `通过牛奶兑换获得宝石: ${gemAmount} 宝石`
        }, { transaction });

        await transaction.commit();

        res.json(successResponse(
            { 
                gem: wallet.gem, 
                pendingMilk: deliveryLine.pendingMilk,
                milkUsed,
                gemAmount
            },
            tFromRequest(req, "success.milkToGem")
        ));
        return;
    } catch (err: any) {
        await transaction.rollback();
        console.error('牛奶兑换宝石失败:', err);
        res.status(500).json(errorResponse(
            tFromRequest(req, "errors.increaseGemFailed"),
            err.message
        ));
        return;
    }
};

/**
 * 获取离线奖励信息
 * @param req 请求对象
 * @param res 响应对象
 */
export const getOfflineReward = async (req: Request, res: Response) => {
    try {
        const myReq = req as MyRequest;
        const { walletId } = myReq.user!;

        // 使用累积离线奖励服务获取累积奖励信息
        const { AccumulatedOfflineRewardService } = require('../services/accumulatedOfflineRewardService');
        const rewardInfo = await AccumulatedOfflineRewardService.getAccumulatedRewardInfo(walletId!);

        // 保持原有的API响应格式
        res.json(successResponse(
            {
                isOffline: rewardInfo.isOffline,
                offlineTime: rewardInfo.offlineTime,
                offlineReward: {
                    gem: rewardInfo.totalAccumulatedGems,
                    milkProduced: rewardInfo.milkProduced,
                    milkProcessed: rewardInfo.milkProcessed,
                    farmProductionPerSecond: rewardInfo.farmProductionPerSecond,
                    deliveryProcessingPerSecond: rewardInfo.deliveryProcessingPerSecond,
                    hasVip: rewardInfo.hasVip,
                    hasSpeedBoost: rewardInfo.hasSpeedBoost,
                    speedBoostMultiplier: rewardInfo.speedBoostMultiplier
                },
                lastActiveTime: rewardInfo.lastActiveTime
            },
            tFromRequest(req, "success.getOfflineReward")
        ));
        return;
    } catch (err: any) {
        console.error('获取离线奖励失败:', err);
        res.status(500).json(errorResponse(
            tFromRequest(req, "errors.getOfflineRewardFailed"),
            err.message
        ));
        return;
    }
};

/**
 * 结算离线奖励
 * @param req 请求对象
 * @param res 响应对象
 */
export const claimOfflineReward = async (req: Request, res: Response) => {
    const transaction: Transaction = await sequelize.transaction();
    try {
        const myReq = req as MyRequest;
        const { walletId, userId } = myReq.user!;

        // 使用累积离线奖励服务
        const { AccumulatedOfflineRewardService } = require('../services/accumulatedOfflineRewardService');
        const claimResult = await AccumulatedOfflineRewardService.claimAccumulatedRewards(walletId!, transaction);

        if (claimResult.claimedGems <= 0) {
            await transaction.rollback();
            res.status(400).json(errorResponse(
                tFromRequest(req, "errors.noOfflineReward"),
                "没有可领取的离线奖励"
            ));
            return;
        }

        await transaction.commit();

        // 获取更新后的钱包信息
        const wallet = await UserWallet.findOne({ where: { id: walletId } });
        const currentGem = wallet ? formatToThreeDecimalsNumber(wallet.gem || 0) : 0;

        res.json(successResponse(
            {
                claimedReward: {
                    gem: claimResult.claimedGems
                },
                remainingReward: {
                    gem: claimResult.remainingGems
                },
                currentGem: currentGem
            },
            tFromRequest(req, "success.claimOfflineReward")
        ));
        return;
    } catch (err: any) {
        await transaction.rollback();
        console.error('结算离线奖励失败:', err);
        res.status(500).json(errorResponse(
            tFromRequest(req, "errors.claimOfflineRewardFailed"),
            err.message
        ));
        return;
    }
};

// 旧的calculateOfflineRewardWithBoosts函数已被TimeWarpService.calculateTimeWarpRewards替代

/**
 * 批量更新用户资源（GEM和PendingMilk）- 带参数验证模式
 * @param req 请求对象
 * @param res 响应对象
 */
export const batchUpdateResources = async (req: Request, res: Response) => {
    try {
        const myReq = req as MyRequest;
        const { walletId, userId } = myReq.user!;

        // 导入BatchResourceUpdateService
        const { BatchResourceUpdateService } = require('../services/batchResourceUpdateService');

        // 验证请求体
        const { gemRequest, milkOperations } = req.body;

        // 执行批量资源更新（根据流程图逻辑验证）
        const result = await BatchResourceUpdateService.updateResources(
            userId,
            walletId!,
            { gemRequest, milkOperations },
            (req as any).language || 'zh'
        );

        res.json(successResponse(
            result.data,
            result.message
        ));

    } catch (error: any) {
        console.error('批量资源更新失败:', error);

        // 根据错误类型返回不同的状态码
        let statusCode = 500;
        let errorMessage = tFromRequest(req, "errors.batchUpdateResourcesFailed");

        if (error.message.includes('参数验证失败')) {
            statusCode = 400;
            errorMessage = tFromRequest(req, "errors.paramValidation");
        } else if (error.message.includes('用户钱包不存在')) {
            statusCode = 404;
            errorMessage = tFromRequest(req, "errors.walletNotFound");
        } else if (error.message.includes('权限')) {
            statusCode = 403;
            errorMessage = tFromRequest(req, "errors.unauthorized");
        }

        res.status(statusCode).json(errorResponse(
            errorMessage,
            error.message
        ));
    }
};

/**
 * 严格验证批量更新用户资源（GEM和PendingMilk）- 更严格的三项验证模式
 * @param req 请求对象
 * @param res 响应对象
 */
export const strictBatchUpdateResources = async (req: Request, res: Response) => {
    try {
        const myReq = req as MyRequest;
        const { walletId, userId } = myReq.user!;

        // 导入StrictBatchResourceUpdateService
        const { StrictBatchResourceUpdateService } = require('../services/strictBatchResourceUpdateService');

        // 验证请求体（参数结构与现有接口完全相同）
        const { gemRequest, milkOperations } = req.body;

        // 执行严格验证批量资源更新
        const result = await StrictBatchResourceUpdateService.updateResourcesWithStrictValidation(
            userId,
            walletId!,
            { gemRequest, milkOperations },
            (req as any).language || 'zh'
        );

        res.json(successResponse(
            result.data,
            result.message
        ));

    } catch (error: any) {
        console.error('严格验证批量资源更新失败:', error);

        // 根据错误类型返回不同的状态码
        let statusCode = 500;
        let errorMessage = tFromRequest(req, "errors.batchUpdateResourcesFailed");

        if (error.message.includes('参数验证失败')) {
            statusCode = 400;
            errorMessage = tFromRequest(req, "errors.paramValidation");
        } else if (error.message.includes('用户钱包不存在')) {
            statusCode = 404;
            errorMessage = tFromRequest(req, "errors.walletNotFound");
        } else if (error.message.includes('时间窗口')) {
            statusCode = 429;
            errorMessage = '请求频率限制';
        } else if (error.message.includes('权限')) {
            statusCode = 403;
            errorMessage = tFromRequest(req, "errors.unauthorized");
        }

        res.status(statusCode).json(errorResponse(
            errorMessage,
            error.message
        ));
    }
};

/**
 * 获取严格验证统计信息（仅开发环境）
 * @param req 请求对象
 * @param res 响应对象
 */
export const getStrictValidationStats = async (req: Request, res: Response) => {
    try {
        // 仅在开发环境提供此接口
        if (process.env.NODE_ENV !== 'development') {
            return res.status(404).json(errorResponse('接口不存在', '此接口仅在开发环境可用'));
        }

        // 导入日志记录器
        const { StrictValidationLogger } = require('../utils/strictValidationLogger');

        // 获取统计信息
        const stats = StrictValidationLogger.getStats();
        const recentLogs = StrictValidationLogger.getRecentLogs(20);
        const failedLogs = StrictValidationLogger.getFailedValidationLogs(10);
        const fallbackLogs = StrictValidationLogger.getFallbackLogs(10);
        const timeWindowRejectedLogs = StrictValidationLogger.getTimeWindowRejectedLogs(10);
        const failureReasons = StrictValidationLogger.analyzeFailureReasons();
        const performanceAnalysis = StrictValidationLogger.getPerformanceAnalysis();

        res.json(successResponse({
            stats,
            recentLogs,
            failedLogs,
            fallbackLogs,
            timeWindowRejectedLogs,
            failureReasons,
            performanceAnalysis,
            timestamp: new Date().toISOString()
        }, '严格验证统计信息获取成功'));

    } catch (error: any) {
        console.error('获取严格验证统计信息失败:', error);
        res.status(500).json(errorResponse('获取统计信息失败', error.message));
    }
};