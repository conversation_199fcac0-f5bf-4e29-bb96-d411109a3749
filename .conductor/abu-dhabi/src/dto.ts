import { boolean, z } from "zod";
// import { CHAIN } from "@tonconnect/ui-react";
import { CHAIN } from "@tonconnect/sdk";
const TonAddress = z.string();

export const GenerateTonProofPayload = z.object({
  payload: z.string().optional(),
  ok: boolean(),
  message:z.string().optional(),
});

const TonDomain = z.object({
  lengthBytes: z.number(),
  value: z.string(),
});

const TonProof = z.object({
  domain: TonDomain,
  payload: z.string(),
  signature: z.string(),
  state_init: z.string(),
  timestamp: z.number(),
});

export const public_key = z.string();

const TonNetwork = z.enum([CHAIN.MAINNET, CHAIN.TESTNET]);

export const CheckProofPayload = z.object({
  address: TonAddress,
  network: TonNetwork,
  proof: TonProof,
  public_key,
  code: z.string().optional(),
  initData: z.string(),
});

export const CheckTonProofSuccess = z.object({
  ok: z.boolean().optional(),
  message: z.string().optional(),
  token: z.string(),
  data: z.any().optional(), // 可选的 data 属性
});

export const CheckTonProofError = z.object({
  error: z.any(),
  message: z.any().optional(),
});

export const CheckTonProof = z.union([
  CheckTonProofSuccess,
  CheckTonProofError,
]);

export const WalletAddress = z.object({
  address: z.string(),
});

export type GenerateTonProofPayload = z.infer<typeof GenerateTonProofPayload>;
export type TonDomain = z.infer<typeof TonDomain>;
export type TonProof = z.infer<typeof TonProof>;
export type TonNetwork = z.infer<typeof TonNetwork>;
export type CheckProofPayload = z.infer<typeof CheckProofPayload>;
export type CheckTonProof = z.infer<typeof CheckTonProof>;
export type CheckTonProofSuccess = z.infer<typeof CheckTonProofSuccess>;
export type CheckTonProofError = z.infer<typeof CheckTonProofError>;
export type WalletAddress = z.infer<typeof WalletAddress>;
export type public_key = z.infer<typeof public_key>;
