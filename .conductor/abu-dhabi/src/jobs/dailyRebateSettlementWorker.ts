import { Job, Worker } from "bullmq";
import { redis } from "../config/redis";
import { dailyRebateSettlement } from "../services/ticketRebateService";

const logger = {
  info: (message: string, ...args: any[]) => {
    console.log(`[DailyRebateSettlementWorker] ${message}`, ...args);
  },
  error: (message: string, ...args: any[]) => {
    console.error(`[DailyRebateSettlementWorker] ${message}`, ...args);
  },
};

console.log('dailyRebateSettlementWorker.ts loaded');

// Worker 配置
export const dailyRebateSettlementWorker = new Worker(
  "daily-rebate-settlement-job",
  async (job: Job) => {
    logger.info(`开始处理任务 ID: ${job.id}`);
    await dailyRebateSettlement();
    logger.info(`任务 ID: ${job.id} 已完成`);
  },
  {
    connection: redis,
    concurrency: 1,
    removeOnComplete: { count: 1000 },
    removeOnFail: { count: 1000 },
  }
);

// 错误处理
dailyRebateSettlementWorker.on("failed", (job: Job | undefined, error: Error) => {
  logger.error(`任务 ${job?.id} 失败:`, error);
});

dailyRebateSettlementWorker.on("error", (error: Error) => {
  logger.error("Worker 出错:", error);
});

// 定义消息类型接口
interface WorkerMessage {
  type: string;
  [key: string]: any; // 允许其他可能的属性
}

// 监听主进程发送的消息
process.on('message', async (message: WorkerMessage) => {
  if (message && message.type === 'shutdown') {
    logger.info('收到关闭信号，正在清理资源...');
    
    try {
      // 关闭 worker 连接
      await dailyRebateSettlementWorker.close();
      
      logger.info('资源清理完毕，准备退出');
      
      // 通知主进程已准备好退出
      process.send?.({ type: 'ready_to_exit' });
      
      // 给主进程一些时间来接收消息
      setTimeout(() => {
        process.exit(0);
      }, 500);
    } catch (err) {
      logger.error('清理资源失败:', err);
      process.exit(1);
    }
  }
});

// 监听 SIGTERM 信号
process.on('SIGTERM', async () => {
  logger.info('收到 SIGTERM 信号，准备退出');
  try {
    await dailyRebateSettlementWorker.close();
  } catch (err) {
    logger.error('关闭 worker 失败:', err);
  }
  process.exit(0);
});

// 监听 SIGINT 信号
process.on('SIGINT', async () => {
  logger.info('收到 SIGINT 信号，准备退出');
  try {
    await dailyRebateSettlementWorker.close();
  } catch (err) {
    logger.error('关闭 worker 失败:', err);
  }
  process.exit(0);
});


// 添加导出的初始化函数
export async function initializeWorker(queue: any) {
  console.log('初始化每日返利结算处理器...');
  // Worker 已经在模块加载时创建，不需要额外操作
  return dailyRebateSettlementWorker;
}