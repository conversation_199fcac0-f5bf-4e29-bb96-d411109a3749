// /src/jobs/scheduleDailyRoundJobs.ts
import { Queue } from "bullmq";
import IORedis from "ioredis";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import timezone from "dayjs/plugin/timezone";
import { jobQueue } from "./bullmqConfig";
import { Session } from "../models";

import dotenv from "dotenv";
import { Op } from "sequelize";
dotenv.config();

dayjs.extend(utc);
dayjs.extend(timezone);

const TZ = "Asia/Shanghai";

/**
 * 调度每天每个场次每个回合的抽奖任务
 *
 * 规则：假设场次分为两类：
 *  - 12:00场次：回合结果时间为：12:08、12:18、12:28、12:38、12:48、12:58
 *  - 20:00场次：回合结果时间为：20:08、20:18、20:28、20:38、20:48、20:58
 *
 * 我们为每个场次、每个回合生成一个 BullMQ 定时任务，CRON表达式按照出结果时间设置。
 */
export async function removeScheduleDailyRoundJobs() {
  // 定义场次信息
  const sessions = [
    { session_category: "12:00:00", baseHour: 12 },
    { session_category: "20:00:00", baseHour: 20 },
  ];

  // 拿到session 表中今天的记录session_dt 等于 今天 20点或者12点的记录
  // 这里用的是sequelize的查询，你需要根据你的数据库模型来调整这部分代码
  const today = dayjs().tz(TZ);
  // 这里用的是sequelize的查询，你需要根据你的数据库模型来调整这部分代码
  const todaySessions = await Session.findAll({
    where: {
      session_dt: {
        [Op.and]: [
          { [Op.gte]: today.startOf("day").toDate() },
          { [Op.lte]: today.endOf("day").toDate() },
        ],
      },
    },
  });

  // 每个场次6个回合
  const rounds = [1, 2, 3, 4, 5, 6];

  for (const session of todaySessions) {
    for (const round of rounds) {
      // 计算出结果时间：例如对于 12:00 场次，第1回合：12:00 + 8 分钟 = 12:08；
      // 第2回合：12:00 + 8 + 10 = 12:18，以此类推
      const resultMinute = 8 + (round - 1) * 10;
      const hour = parseInt(session.session_category.split(":")[0], 10);
      // 构造 CRON 表达式：格式为 "秒 分 时 日 月 星期"
      // 这里假设秒为0
      const pattern = `0 ${resultMinute} ${hour} * * *`;
      // 生成一个唯一的调度任务ID
      const schedulerId = `scheduler-${session.session_category}-round-${round}`;

      // Remove a job scheduler with ID 'scheduler-123'
      const result = await jobQueue.removeJobScheduler(schedulerId);
      console.log(
        result ? "Scheduler removed successfully" : "Missing Job Scheduler"
      );
    }
  }
}
