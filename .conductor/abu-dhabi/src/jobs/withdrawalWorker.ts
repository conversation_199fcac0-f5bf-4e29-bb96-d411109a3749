import { Job, Worker } from "bullmq";
import { redis } from "../config/redis";
import { tonWithdrawalService } from "../services/tonWithdrawalService";
import dotenv from "dotenv";

dotenv.config();

// 日志工具
const logger = {
  info: (message: string, ...args: any[]) => {
    console.log(`[WithdrawalWorker] ${message}`, ...args);
  },
  error: (message: string, ...args: any[]) => {
    console.error(`[WithdrawalWorker] ${message}`, ...args);
  },
  warn: (message: string, ...args: any[]) => {
    console.warn(`[WithdrawalWorker] ${message}`, ...args);
  },
};

logger.info("TON提现工作器初始化开始...");

/**
 * 处理TON提现任务的核心函数
 */
async function processTONWithdrawals() {
  logger.info("开始处理TON提现任务");
  
  try {
    // 使用TON提现服务处理提现记录
    const result = await tonWithdrawalService.processTonWithdrawals();
    logger.info(`TON提现处理任务完成：成功处理 ${result.processed} 条记录`);
    return result;
  } catch (error) {
    logger.error("处理TON提现失败:", error);
    throw error;
  }
}

// 创建Worker实例处理提现任务
export const withdrawalWorker = new Worker(
  "ton-withdrawal-queue",
  async (job: Job) => {
    logger.info(`开始处理任务 ID: ${job.id}，数据:`, job.data);
    
    // 验证任务类型，确保只处理TON提现任务
    const { type } = job.data;
    if (type !== 'ton-withdrawal') {
      logger.warn(`跳过非TON提现任务: ${job.id}, 类型: ${type || '未指定'}`);
      return;
    }
    
    await processTONWithdrawals();
    logger.info(`任务 ID: ${job.id} 已完成`);
  },
  {
    connection: redis,
    concurrency: 1, // 确保一次只处理一个任务
    removeOnComplete: { count: 1000 },
    removeOnFail: { count: 1000 },
  }
);

// 错误处理
withdrawalWorker.on("failed", (job: Job | undefined, error: Error) => {
  logger.error(`任务 ${job?.id} 失败:`, error);
});

withdrawalWorker.on("error", (error: Error) => {
  logger.error("Worker 出错:", error);
});

// 监听进程消息
process.on('message', async (message: any) => {
  if (message && message.type === 'shutdown') {
    logger.info('收到关闭信号，正在清理资源...');
    
    try {
      // 关闭 worker 连接
      await withdrawalWorker.close();
      
      logger.info('资源清理完毕，准备退出');
      
      // 通知主进程已准备好退出
      process.send?.({ type: 'ready_to_exit' });
      
      // 给主进程一些时间来接收消息
      setTimeout(() => {
        process.exit(0);
      }, 500);
    } catch (err) {
      logger.error('清理资源失败:', err);
      process.exit(1);
    }
  }
});

// 监听退出信号
process.on('SIGTERM', async () => {
  logger.info('收到 SIGTERM 信号，准备退出');
  try {
    await withdrawalWorker.close();
  } catch (err) {
    logger.error('关闭 worker 失败:', err);
  }
  process.exit(0);
});

process.on('SIGINT', async () => {
  logger.info('收到 SIGINT 信号，准备退出');
  try {
    await withdrawalWorker.close();
  } catch (err) {
    logger.error('关闭 worker 失败:', err);
  }
  process.exit(0);
});


// 添加导出的初始化函数
export async function initializeWorker(queue: any) {
  console.log('初始化提现处理器...');
  // Worker 已经在模块加载时创建，不需要额外操作
  return withdrawalWorker;
}