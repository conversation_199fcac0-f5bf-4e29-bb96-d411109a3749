import { DataTypes, Model, Optional } from "sequelize";
import { sequelize } from "../config/db";
import { UserWallet } from './UserWallet';
import { IapProduct } from './IapProduct';

interface ActiveBoosterAttributes {
  id: number;
  walletId: number;
  productId?: number;
  type: 'speed_boost' | 'time_warp';
  multiplier: number;
  startTime: Date;
  endTime: Date;
  status: 'active' | 'expired' | 'used';
  createdAt?: Date;
  updatedAt?: Date;
}

interface ActiveBoosterCreationAttributes extends Optional<ActiveBoosterAttributes, 'id' | 'productId' | 'status' | 'createdAt' | 'updatedAt'> {}

export class ActiveBooster extends Model<ActiveBoosterAttributes, ActiveBoosterCreationAttributes> implements ActiveBoosterAttributes {
  public id!: number;
  public walletId!: number;
  public productId?: number;
  public type!: 'speed_boost' | 'time_warp';
  public multiplier!: number;
  public startTime!: Date;
  public endTime!: Date;
  public status!: 'active' | 'expired' | 'used';
  public readonly createdAt!: Date;
  public readonly updatedAt!: Date;

  // 检查道具是否仍然有效
  public isActive(): boolean {
    const now = new Date();
    return this.status === 'active' && now >= this.startTime && now <= this.endTime;
  }
}

ActiveBooster.init(
  {
    id: {
      type: DataTypes.INTEGER.UNSIGNED,
      autoIncrement: true,
      primaryKey: true,
    },
    walletId: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false,
      references: {
        model: UserWallet,
        key: 'id',
      },
    },
    productId: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: true,
      references: {
        model: IapProduct,
        key: 'id',
      },
    },
    type: {
      type: DataTypes.ENUM('speed_boost', 'time_warp'),
      allowNull: false,
    },
    multiplier: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    startTime: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    endTime: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    status: {
      type: DataTypes.ENUM('active', 'expired', 'used'),
      allowNull: false,
      defaultValue: 'active',
    },
  },
  {
    sequelize,
    modelName: "ActiveBooster",
    tableName: "active_boosters",
  }
);

