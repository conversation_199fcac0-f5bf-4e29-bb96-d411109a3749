import { Model, DataTypes, Optional } from "sequelize";
import { sequelize } from "../config/db";
import { UserWallet } from './UserWallet';

interface BoosterAttributes {
  id: number;
  walletId: number;
  type: 'speed_boost' | 'time_warp'; // 道具类型
  multiplier: number; // 加成倍数 (如2倍、4倍)
  duration: number; // 持续时间（小时）
  quantity: number; // 拥有数量
  createdAt?: Date;
  updatedAt?: Date;
}

interface BoosterCreationAttributes extends Optional<BoosterAttributes, 'id' | 'createdAt' | 'updatedAt'> {}

export class Booster extends Model<BoosterAttributes, BoosterCreationAttributes> implements BoosterAttributes {
  public id!: number;
  public walletId!: number;
  public type!: 'speed_boost' | 'time_warp';
  public multiplier!: number;
  public duration!: number;
  public quantity!: number;
  public readonly createdAt!: Date;
  public readonly updatedAt!: Date;
}

Booster.init(
  {
    id: {
      type: DataTypes.INTEGER.UNSIGNED,
      autoIncrement: true,
      primaryKey: true,
    },
    walletId: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false,
      references: {
        model: UserWallet,
        key: 'id',
      },
    },
    type: {
      type: DataTypes.ENUM('speed_boost', 'time_warp'),
      allowNull: false,
    },
    multiplier: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    duration: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    quantity: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
    },
  },
  {
    sequelize,
    modelName: "Booster",
    tableName: "boosters",
  }
);

