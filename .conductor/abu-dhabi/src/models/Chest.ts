// src/models/Chest.ts
import { Model, DataTypes, Optional } from "sequelize";
import { sequelize } from "../config/db";

// 在现有的 Chest 模型中添加 type 字段
// 如果 Chest 模型已经有 type 字段，则不需要此修改

interface ChestAttributes {
  id: number;
  userId: number;
  walletId: number;
  isOpened: boolean;
  type?: string;
  source?: string; // 添加 source 字段，用于记录来源
  rewardInfo?: any; // 添加 rewardInfo 字段，用于记录宝箱奖励信息
  createdAt?: Date;
  updatedAt?: Date;
  openedAt?: Date;
}

type ChestCreationAttributes = Optional<ChestAttributes, "id">;

export class Chest extends Model<ChestAttributes, ChestCreationAttributes> implements ChestAttributes {
  public id!: number;
  public userId!: number;
  public walletId!: number;
  public isOpened!: boolean;
  public type!: string;
  public source?: string;
  public rewardInfo?: any; // 添加 rewardInfo 字段
  public openedAt?: Date;
  
  public readonly createdAt!: Date;
  public readonly updatedAt!: Date;
}

Chest.init(
  {
    id: {
      type: DataTypes.INTEGER.UNSIGNED,
      autoIncrement: true,
      primaryKey: true,
    },
    userId: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false,
    },
    walletId: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false,
    },
    isOpened: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    type: {
      type: DataTypes.STRING,
      allowNull: true,
      defaultValue: 'daily'
    },
    source: {
      type: DataTypes.STRING,
      allowNull: true,
      comment: '宝箱来源，例如：premium_referral, normal_referral'
    },
    openedAt: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    rewardInfo: {
      type: DataTypes.JSON,
      allowNull: true,
      comment: '宝箱奖励信息，包含等级和奖励项目详情'
    },
  },
  {
    tableName: "chests",
    sequelize,
    timestamps: true,
  }
);

export default Chest;
