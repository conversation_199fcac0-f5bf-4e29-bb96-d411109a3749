import { Model, DataTypes, Optional } from 'sequelize';
import { sequelize } from "../config/db";

interface DeliveryLineConfigAttributes {
  id: number;
  grade: number; // 流水线等级
  profit: number; // 牛奶利润
  capacity: number; // 牛奶容量
  production_interval: number; // 生产间隔(秒)
  delivery_speed_display: number; // 显示的配送速度百分数
  upgrade_cost: number; // 升级花费
  createdAt?: Date;
  updatedAt?: Date;
}

interface DeliveryLineConfigCreationAttributes extends Optional<DeliveryLineConfigAttributes, 'id' | 'createdAt' | 'updatedAt'> {}

class DeliveryLineConfig extends Model<DeliveryLineConfigAttributes, DeliveryLineConfigCreationAttributes> implements DeliveryLineConfigAttributes {
  public id!: number;
  public grade!: number;
  public profit!: number;
  public capacity!: number;
  public production_interval!: number;
  public delivery_speed_display!: number;
  public upgrade_cost!: number;
  public readonly createdAt!: Date;
  public readonly updatedAt!: Date;

  // 静态方法：根据等级获取配置
  public static async getConfigByGrade(grade: number): Promise<DeliveryLineConfig | null> {
    return await DeliveryLineConfig.findOne({
      where: { grade }
    });
  }

  // 静态方法：获取所有配置
  public static async getAllConfigs(): Promise<DeliveryLineConfig[]> {
    return await DeliveryLineConfig.findAll({
      order: [['grade', 'ASC']]
    });
  }

  // 静态方法：获取配置范围
  public static async getConfigRange(): Promise<{ minGrade: number; maxGrade: number }> {
    const configs = await DeliveryLineConfig.findAll({
      attributes: ['grade'],
      order: [['grade', 'ASC']]
    });
    
    if (configs.length === 0) {
      return { minGrade: 1, maxGrade: 1 };
    }
    
    return {
      minGrade: configs[0].grade,
      maxGrade: configs[configs.length - 1].grade
    };
  }

  // 静态方法：批量创建配置
  public static async bulkCreateConfigs(configs: DeliveryLineConfigCreationAttributes[]): Promise<DeliveryLineConfig[]> {
    return await DeliveryLineConfig.bulkCreate(configs, {
      updateOnDuplicate: ['profit', 'capacity', 'production_interval', 'delivery_speed_display', 'upgrade_cost', 'updatedAt']
    });
  }
}

DeliveryLineConfig.init(
  {
    id: {
      type: DataTypes.INTEGER.UNSIGNED,
      autoIncrement: true,
      primaryKey: true,
    },
    grade: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false,
      unique: true,
      comment: '流水线等级',
    },
    profit: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false,
      comment: '牛奶利润',
    },
    capacity: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false,
      comment: '牛奶容量',
    },
    production_interval: {
      type: DataTypes.DECIMAL(3, 1),
      allowNull: false,
      comment: '生产间隔(秒)',
    },
    delivery_speed_display: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false,
      comment: '显示的配送速度百分数',
    },
    upgrade_cost: {
      type: DataTypes.BIGINT.UNSIGNED,
      allowNull: false,
      comment: '升级花费',
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
      field: 'created_at', // 映射到数据库的 created_at 字段
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
      field: 'updated_at', // 映射到数据库的 updated_at 字段
    },
  },
  {
    sequelize,
    tableName: 'delivery_line_configs',
    timestamps: true,
    indexes: [
      {
        unique: true,
        fields: ['grade']
      }
    ]
  }
);

export { DeliveryLineConfig };
