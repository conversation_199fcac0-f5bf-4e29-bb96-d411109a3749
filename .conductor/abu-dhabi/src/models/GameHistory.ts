import { Model, DataTypes, Optional } from "sequelize";
import { sequelize } from "../config/db";

// 定义 GameHistory 的属性接口
interface GameHistoryAttributes {
  id: number;
  userId: number;
  walletId: number;
  sessionId: number;
  round: number;
  betAmount: number;
  game_status: string;
  payout_status: string;
  payout: number;
  is_moof: boolean;
  roomId: string;
  seqno?: number;
  result_position?: number;
  createdAt?: Date;
  ticketType?: string; // 添加票类型字段：ticket(普通票)或free_ticket(免费票)
}

// 定义创建 GameHistory 实例时的属性接口（`id` 字段是可选的）
type GameHistoryCreationAttributes = Optional<GameHistoryAttributes, "id">;

// 定义 GameHistory 模型类
export class GameHistory
  extends Model<GameHistoryAttributes, GameHistoryCreationAttributes>
  implements GameHistoryAttributes
{
  public id!: number;
  public userId!: number;
  public walletId!: number;
  public sessionId!: number;
  public round!: number;
  public betAmount!: number;
  public game_status!: string;
  public payout_status!: string;
  public payout!: number;
  public is_moof!: boolean;
  public roomId!: string;
  public seqno?: number;
  public result_position?: number;
  public createdAt?: Date;
  public ticketType!: string; // 添加票类型字段
}

// 初始化 GameHistory 模型
GameHistory.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true, // 自动生成 ID
    },
    userId: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    walletId: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    sessionId: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    roomId: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    round: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    betAmount: {
      type: DataTypes.FLOAT,
      allowNull: false,
    },
    game_status: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    payout_status: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    payout: {
      type: DataTypes.FLOAT,
      allowNull: false,
    },
    is_moof: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
    },
    seqno: { type: DataTypes.INTEGER, allowNull: true }, // 新增
    result_position: { type: DataTypes.INTEGER, allowNull: true }, // 新增
    createdAt: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
    ticketType: { 
      type: DataTypes.STRING, 
      allowNull: false, 
      defaultValue: 'ticket',
      comment: '使用的票类型: ticket(普通票) 或 free_ticket(免费票)'
    },
  },
  {
    sequelize,
    modelName: "game_history",
  }
);

export default GameHistory;
