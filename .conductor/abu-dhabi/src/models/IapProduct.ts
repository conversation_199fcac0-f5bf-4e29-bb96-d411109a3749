import { Model, DataTypes, Optional } from "sequelize";
import { sequelize } from "../config/db";

interface IapProductAttributes {
  id: number;
  productId: string; // 商品唯一标识
  name: string;
  type: 'speed_boost' | 'time_warp' | 'vip_membership' | 'special_offer';
  priceUsd: number;
  priceKaia?: number;
  pricePhrs?: number; // PHRS价格
  multiplier?: number; // 对于加速道具
  duration?: number; // 持续时间（小时）
  quantity?: number; // 数量（对于套餐）
  dailyLimit: number; // 每日购买限制
  accountLimit?: number; // 账号购买限制（对于特殊套餐）
  config?: any; // 产品特定配置（JSON格式）
  isActive: boolean;
  description?: string;
  createdAt?: Date;
  updatedAt?: Date;
}

interface IapProductCreationAttributes extends Optional<IapProductAttributes, 'id' | 'createdAt' | 'updatedAt'> {}

export class IapProduct extends Model<IapProductAttributes, IapProductCreationAttributes> implements IapProductAttributes {
  public id!: number;
  public productId!: string;
  public name!: string;
  public type!: 'speed_boost' | 'time_warp' | 'vip_membership' | 'special_offer';
  public priceUsd!: number;
  public priceKaia?: number;
  public pricePhrs?: number;
  public multiplier?: number;
  public duration?: number;
  public quantity?: number;
  public dailyLimit!: number;
  public accountLimit?: number;
  public config?: any;
  public isActive!: boolean;
  public description?: string;
  public readonly createdAt!: Date;
  public readonly updatedAt!: Date;
}

IapProduct.init(
  {
    id: {
      type: DataTypes.INTEGER.UNSIGNED,
      autoIncrement: true,
      primaryKey: true,
    },
    productId: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true,
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    type: {
      type: DataTypes.ENUM('speed_boost', 'time_warp', 'vip_membership', 'special_offer'),
      allowNull: false,
    },
    priceUsd: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
    },
    priceKaia: {
      type: DataTypes.DECIMAL(10, 4),
      allowNull: true,
    },
    pricePhrs: {
      type: DataTypes.DECIMAL(20, 4),
      allowNull: true,
      comment: 'PHRS价格，基于USD价格和PHRS汇率计算'
    },
    multiplier: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    duration: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    quantity: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    dailyLimit: {
      type: DataTypes.INTEGER,
      defaultValue: 1,
    },
    accountLimit: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    config: {
      type: DataTypes.JSON,
      allowNull: true,
    },
    isActive: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
  },
  {
    sequelize,
    modelName: "IapProduct",
    tableName: "iap_products",
  }
);