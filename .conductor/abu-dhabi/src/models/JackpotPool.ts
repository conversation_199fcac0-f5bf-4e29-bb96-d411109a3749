import { Model, DataTypes, Optional } from "sequelize";
import { sequelize } from "../config/db";

interface JackpotPoolAttributes {
  id: number;
  level: number; // 1, 2, 3 等级
  currentAmount: number; // 当前累积总金额
  targetAmount: number; // 目标金额 (10, 20 ton等)
  newUserAmount: number; // 新用户贡献累积金额
  chestOpenAmount: number; // 开宝箱贡献累积金额
  lastWinnerId?: number | null; // 最后一个获奖用户ID
  lastWinnerWalletId?: number; // 最后一个获奖用户钱包ID
  lastWinTime?: Date; // 最后一次获奖时间
  createdAt?: Date;
  updatedAt?: Date;
}

type JackpotPoolCreationAttributes = Optional<JackpotPoolAttributes, "id">;

export class JackpotPool
  extends Model<JackpotPoolAttributes, JackpotPoolCreationAttributes>
  implements JackpotPoolAttributes
{
  public id!: number;
  public level!: number;
  public currentAmount!: number;
  public targetAmount!: number;
  public newUserAmount!: number;
  public chestOpenAmount!: number;
  public lastWinnerId?: number | null;
  public lastWinnerWalletId?: number;
  public lastWinTime?: Date;
  public readonly createdAt!: Date;
  public readonly updatedAt!: Date;
}

JackpotPool.init(
  {
    id: {
      type: DataTypes.INTEGER.UNSIGNED,
      autoIncrement: true,
      primaryKey: true,
    },
    level: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false,
    },
    currentAmount: {
      type: DataTypes.DECIMAL(18, 6),
      defaultValue: 0,
      allowNull: false,
      get() {
        const value = this.getDataValue('currentAmount');
        return value ? parseFloat(value.toString()) : 0;
      }
    },
    targetAmount: {
      type: DataTypes.DECIMAL(18, 6),
      allowNull: false,
      get() {
        const value = this.getDataValue('targetAmount');
        return value ? parseFloat(value.toString()) : 0;
      }
    },
    newUserAmount: {
      type: DataTypes.DECIMAL(18, 6),
      defaultValue: 0,
      allowNull: false,
      get() {
        const value = this.getDataValue('newUserAmount');
        return value ? parseFloat(value.toString()) : 0;
      }
    },
    chestOpenAmount: {
      type: DataTypes.DECIMAL(18, 6),
      defaultValue: 0,
      allowNull: false,
      get() {
        const value = this.getDataValue('chestOpenAmount');
        return value ? parseFloat(value.toString()) : 0;
      }
    },
    lastWinnerId: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: true,
    },
    lastWinnerWalletId: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: true,
    },
    lastWinTime: {
      type: DataTypes.DATE,
      allowNull: true,
    },
  },
  {
    tableName: "jackpot_pools",
    sequelize,
    timestamps: true,
  }
);