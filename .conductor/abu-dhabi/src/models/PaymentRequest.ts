import { Model, DataTypes } from 'sequelize';
import { sequelize } from "../config/db";

export class PaymentRequest extends Model {
  public id!: number;
  public paymentId!: string;
  public telegramId?: string;
  public userId!: number;
  public walletId!: number;
  public amount!: number;
  public productType!: string;
  public status!: 'pending' | 'completed' | 'failed' | 'expired';
  public createdAt!: Date;
  public updatedAt!: Date;
  public expiresAt!: Date;
  public errorMessage?: string;
  public retryCount?: number;
  public lastRetryAt?: Date;
  public metadata?: object;
}

PaymentRequest.init({
  id: {
    type: DataTypes.INTEGER,
    autoIncrement: true,
    primaryKey: true
  },
  paymentId: {
    type: DataTypes.STRING,
    allowNull: false,
    unique: true
  },
  telegramId: {
    type: DataTypes.STRING,
    allowNull: true
  },
  userId: {
    type: DataTypes.INTEGER,
    allowNull: false
  },
  walletId: {
    type: DataTypes.INTEGER,
    allowNull: false
  },
  amount: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false
  },
  productType: {
    type: DataTypes.STRING,
    allowNull: false
  },
  status: {
    type: DataTypes.ENUM('pending', 'completed', 'failed', 'expired'),
    allowNull: false,
    defaultValue: 'pending'
  },
  createdAt: {
    type: DataTypes.DATE,
    allowNull: false
  },
  updatedAt: {
    type: DataTypes.DATE,
    allowNull: false
  },
  expiresAt: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: () => {
      const date = new Date();
      date.setHours(date.getHours() + 24); // 24小时后过期
      return date;
    }
  },
  errorMessage: {
    type: DataTypes.STRING,
    allowNull: true
  },
  retryCount: {
    type: DataTypes.INTEGER,
    allowNull: true,
    defaultValue: 0
  },
  lastRetryAt: {
    type: DataTypes.DATE,
    allowNull: true
  },
  metadata: {
    type: DataTypes.JSON,
    allowNull: true
  }
}, {
  sequelize,
  modelName: 'PaymentRequest',
  tableName: 'payment_requests',
  timestamps: true
});