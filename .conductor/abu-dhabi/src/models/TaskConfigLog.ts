import { Model, DataTypes, Optional } from 'sequelize';
import { sequelize } from '../config/db';

// 操作类型
export type TaskConfigLogAction = 'upload' | 'apply' | 'rollback' | 'validate';

// 操作状态
export type TaskConfigLogStatus = 'success' | 'failed';

interface TaskConfigLogAttributes {
  id: number;
  action: TaskConfigLogAction;
  versionNumber?: string;
  details?: string;
  adminId: string;
  status: TaskConfigLogStatus;
  errorMessage?: string;
  createdAt?: Date;
  updatedAt?: Date;
}

interface TaskConfigLogCreationAttributes extends Optional<TaskConfigLogAttributes, 'id' | 'createdAt' | 'updatedAt'> {}

export class TaskConfigLog extends Model<TaskConfigLogAttributes, TaskConfigLogCreationAttributes> implements TaskConfigLogAttributes {
  public id!: number;
  public action!: TaskConfigLogAction;
  public versionNumber?: string;
  public details?: string;
  public adminId!: string;
  public status!: TaskConfigLogStatus;
  public errorMessage?: string;

  public readonly createdAt!: Date;
  public readonly updatedAt!: Date;

  /**
   * 创建成功日志
   */
  public static createSuccessLog(
    action: TaskConfigLogAction,
    adminId: string,
    details: string,
    versionNumber?: string
  ): Promise<TaskConfigLog> {
    return TaskConfigLog.create({
      action,
      adminId,
      status: 'success',
      details,
      versionNumber
    });
  }

  /**
   * 创建失败日志
   */
  public static createFailureLog(
    action: TaskConfigLogAction,
    adminId: string,
    errorMessage: string,
    details?: string,
    versionNumber?: string
  ): Promise<TaskConfigLog> {
    return TaskConfigLog.create({
      action,
      adminId,
      status: 'failed',
      errorMessage,
      details,
      versionNumber
    });
  }

  /**
   * 获取操作描述
   */
  public getActionDescription(): string {
    const actionMap: { [key in TaskConfigLogAction]: string } = {
      'upload': '上传配置',
      'apply': '应用配置',
      'rollback': '回滚配置',
      'validate': '验证配置'
    };
    return actionMap[this.action];
  }

  /**
   * 获取状态描述
   */
  public getStatusDescription(): string {
    const statusMap: { [key in TaskConfigLogStatus]: string } = {
      'success': '成功',
      'failed': '失败'
    };
    return statusMap[this.status];
  }

  /**
   * 检查操作是否成功
   */
  public isSuccess(): boolean {
    return this.status === 'success';
  }

  /**
   * 检查操作是否失败
   */
  public isFailed(): boolean {
    return this.status === 'failed';
  }

  /**
   * 获取日志的JSON表示
   */
  public toLogJSON(): any {
    return {
      id: this.id,
      action: this.action,
      actionDescription: this.getActionDescription(),
      versionNumber: this.versionNumber,
      details: this.details,
      adminId: this.adminId,
      status: this.status,
      statusDescription: this.getStatusDescription(),
      errorMessage: this.errorMessage,
      isSuccess: this.isSuccess(),
      isFailed: this.isFailed(),
      createdAt: this.createdAt,
      updatedAt: this.updatedAt
    };
  }
}

TaskConfigLog.init(
  {
    id: {
      type: DataTypes.INTEGER.UNSIGNED,
      autoIncrement: true,
      primaryKey: true,
    },
    action: {
      type: DataTypes.ENUM('upload', 'apply', 'rollback', 'validate'),
      allowNull: false,
      comment: '操作类型',
    },
    versionNumber: {
      type: DataTypes.STRING(50),
      allowNull: true,
      comment: '相关版本号',
    },
    details: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: '操作详情',
    },
    adminId: {
      type: DataTypes.STRING(100),
      allowNull: false,
      comment: '操作管理员ID',
    },
    status: {
      type: DataTypes.ENUM('success', 'failed'),
      allowNull: false,
      comment: '操作状态',
    },
    errorMessage: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: '错误信息',
    },
  },
  {
    tableName: 'task_config_logs',
    sequelize,
    timestamps: true,
    indexes: [
      {
        fields: ['action']
      },
      {
        fields: ['adminId']
      },
      {
        fields: ['status']
      },
      {
        fields: ['versionNumber']
      },
      {
        fields: ['createdAt']
      }
    ]
  }
);
