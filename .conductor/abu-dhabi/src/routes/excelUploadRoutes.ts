import express from 'express';
import multer from 'multer';
import excelUploadController from '../controllers/excelUploadController';
import { walletAuthMiddleware } from '../middlewares/walletAuth';
import { languageMiddleware } from '../middlewares/languageMiddleware';

const router = express.Router();

// 配置multer用于文件上传
const storage = multer.memoryStorage(); // 使用内存存储，直接处理文件缓冲区

const upload = multer({
  storage: storage,
  limits: {
    fileSize: 10 * 1024 * 1024, // 限制文件大小为10MB
  },
  fileFilter: (req, file, cb) => {
    // 检查文件类型
    const allowedMimeTypes = [
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
      'application/vnd.ms-excel', // .xls
    ];
    
    if (allowedMimeTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error('只允许上传Excel文件 (.xlsx 或 .xls)'));
    }
  }
});

// 应用中间件
router.use(languageMiddleware);

// 上传并读取Excel文件 - 暂时不需要认证（测试用）
router.post('/upload',
  upload.single('excelFile'),
  excelUploadController.uploadAndReadExcel
);

// 获取Excel模板文件 - 暂时不需要认证（测试用）
router.get('/template',
  excelUploadController.getExcelTemplate
);

// 批量处理区域升级数据 - 暂时不需要认证（测试用）
router.post('/batch-upgrade',
  excelUploadController.processBatchUpgrade
);

// 农场配置相关路由
router.post('/farm-config/upload',
  upload.single('excelFile'),
  excelUploadController.uploadFarmConfig
);

router.post('/farm-config/validate',
  upload.single('excelFile'),
  excelUploadController.validateFarmConfig
);

router.get('/farm-config/template',
  excelUploadController.getFarmConfigTemplate
);

// 错误处理中间件
router.use((error: any, req: express.Request, res: express.Response, next: express.NextFunction) => {
  if (error instanceof multer.MulterError) {
    if (error.code === 'LIMIT_FILE_SIZE') {
      res.status(400).json({
        ok: false,
        message: '文件大小超过限制（最大10MB）'
      });
      return;
    }
  }

  if (error.message) {
    res.status(400).json({
      ok: false,
      message: error.message
    });
    return;
  }

  next(error);
});

export default router;
