import express from 'express';
import multer from 'multer';
import farmConfigController from '../controllers/farmConfigController';
import { walletAuthMiddleware } from '../middlewares/walletAuth';
import { languageMiddleware } from '../middlewares/languageMiddleware';
import { 
  adminAuthMiddleware, 
  devAdminAuthMiddleware,
  securityWarningMiddleware,
  rateLimitMiddleware
} from '../middlewares/adminAuth';

const router = express.Router();

// 配置multer用于文件上传
const storage = multer.memoryStorage();

const upload = multer({
  storage: storage,
  limits: {
    fileSize: 10 * 1024 * 1024, // 限制文件大小为10MB
  },
  fileFilter: (req, file, cb) => {
    // 检查文件类型
    const allowedMimeTypes = [
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
      'application/vnd.ms-excel', // .xls
    ];
    
    if (allowedMimeTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error('只允许上传Excel文件 (.xlsx 或 .xls)'));
    }
  }
});

// 应用全局中间件
router.use(languageMiddleware);

// 根据环境选择认证中间件
// const authMiddleware = process.env.NODE_ENV === 'development' 
//   ? devAdminAuthMiddleware 
//   : [walletAuthMiddleware, adminAuthMiddleware];

// 公开接口（无需管理员权限）
// 获取当前激活配置 - 供游戏客户端使用
router.get('/current', farmConfigController.getCurrentConfig);

// 获取配置统计信息 - 供监控使用
router.get('/stats', farmConfigController.getStats);

// 管理员接口（需要管理员权限）
router.use(walletAuthMiddleware);

// 获取配置版本列表
router.get('/versions', farmConfigController.getVersions);

// 获取指定版本详情
router.get('/versions/:version', farmConfigController.getVersionDetail);

// 上传配置文件
router.post('/upload',
  rateLimitMiddleware(5, 300000), // 5分钟内最多5次上传
  upload.single('excelFile'),
  farmConfigController.uploadConfig
);

// 验证配置文件（不保存到数据库）
router.post('/validate',
  upload.single('excelFile'),
  farmConfigController.validateConfig
);

// 激活指定版本
router.post('/activate',
  securityWarningMiddleware,
  rateLimitMiddleware(3, 60000), // 1分钟内最多3次激活操作
  farmConfigController.activateVersion
);

// 回滚到上一版本
router.post('/rollback',
  securityWarningMiddleware,
  rateLimitMiddleware(2, 300000), // 5分钟内最多2次回滚操作
  farmConfigController.rollbackToPrevious
);

// 删除指定版本
router.delete('/versions/:version',
  securityWarningMiddleware,
  rateLimitMiddleware(3, 300000), // 5分钟内最多3次删除操作
  farmConfigController.deleteVersion
);

// 预热缓存
router.post('/warmup-cache',
  farmConfigController.warmupCache
);

// 错误处理中间件
router.use((error: any, req: express.Request, res: express.Response, next: express.NextFunction) => {
  if (error instanceof multer.MulterError) {
    if (error.code === 'LIMIT_FILE_SIZE') {
      res.status(400).json({
        ok: false,
        message: '文件大小超过限制（最大10MB）'
      });
      return;
    }
  }

  if (error.message) {
    res.status(400).json({
      ok: false,
      message: error.message
    });
    return;
  }

  console.error('农场配置路由错误:', error);
  res.status(500).json({
    ok: false,
    message: '服务器内部错误'
  });
});

export default router;
