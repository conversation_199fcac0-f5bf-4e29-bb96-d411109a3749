// src/routes/fragmentRoutes.ts
import { Router } from "express";
import { craftTicketWithFragment, FragmentType } from "../services/fragmentService";
import { walletAuthMiddleware } from "../middlewares/walletAuth";
import { MyRequest } from "../types/customRequest";
import { ajv, tFromRequest, formatValidationErrors } from "../i18n";
import { languageMiddleware } from "../middlewares/languageMiddleware";
import { successResponse, errorResponse } from "../utils/responseUtil";

const router = Router();

// 应用语言中间件到所有路由
router.use(languageMiddleware);

// 定义碎片制作门票请求体验证模式
const craftTicketSchema = {
  type: "object",
  properties: {
    fragmentType: { 
      type: "string", 
      enum: ["fragment_green", "fragment_blue", "fragment_purple", "fragment_gold"] 
    },
    quantity: { type: "integer", minimum: 1 },
    isFree: { type: "boolean", default: false }
  },
  required: ["fragmentType", "quantity"]
};

const validateCraftTicket = ajv.compile(craftTicketSchema);

/**
 * POST /api/fragment/craft-ticket
 * 使用碎片制作门票
 * body: { fragmentType, quantity }
 * header: { Authorization: "Bearer <token>" }
 */
//@ts-ignore
router.post("/craft-ticket", walletAuthMiddleware, async (req, res) => {
  try {
    const myReq = req as MyRequest;

    // 验证请求体
    const valid = validateCraftTicket(req.body);
    if (!valid) {
      return res.status(400).json(errorResponse(
        tFromRequest(req, "errors.paramValidation"),
        formatValidationErrors(validateCraftTicket.errors || [], req.language)
      ));
    }

    const { userId, walletId } = myReq.user!;
    const { fragmentType, quantity } = req.body;

    const isFree = true; //固定免费门票

    // 将字符串转换为枚举类型
    let fragmentEnum: FragmentType;
    switch (fragmentType) {
      case "fragment_green":
        fragmentEnum = FragmentType.GREEN;
        break;
      case "fragment_blue":
        fragmentEnum = FragmentType.BLUE;
        break;
      case "fragment_purple":
        fragmentEnum = FragmentType.PURPLE;
        break;
      case "fragment_gold":
        fragmentEnum = FragmentType.GOLD;
        break;
      default:
        return res.status(400).json(errorResponse(tFromRequest(req, "errors.invalidFragmentType")));
    }

    const wallet = await craftTicketWithFragment(userId!, walletId!, fragmentEnum, quantity, isFree);
    res.json(successResponse(
      {
        balance: {
          ticket: wallet?.ticket,
          free_ticket: wallet?.free_ticket,
          fragment_green: wallet?.fragment_green,
          fragment_blue: wallet?.fragment_blue,
          fragment_purple: wallet?.fragment_purple,
          fragment_gold: wallet?.fragment_gold
        }
      },
      tFromRequest(req, "success.fragmentCraft")
    ));
  } catch (err: any) {
    console.error("Error crafting ticket:", err.message);
    res.status(400).json(errorResponse(err.message));
  }
});

export default router;