// src/routes/freeTicketTransferRoutes.ts
import { Router } from "express";
import { transferFreeTicket, getRemainingDailyTransferLimit, batchTransferFreeTickets } from "../services/freeTicketTransferService";
import { walletAuthMiddleware } from "../middlewares/walletAuth";
import { MyRequest } from "../types/customRequest";
import { ajv, tFromRequest, formatValidationErrors } from "../i18n";
import { languageMiddleware } from "../middlewares/languageMiddleware";
import { successResponse, errorResponse } from "../utils/responseUtil";

const router = Router();

// 应用语言中间件到所有路由
router.use(languageMiddleware);

// 定义转账免费门票请求体验证模式
const transferFreeTicketSchema = {
  type: "object",
  properties: {
    toWalletAddress: { type: "string" },
    amount: { type: "integer", minimum: 1 }
  },
  required: ["toWalletAddress", "amount"]
};

const validateTransferFreeTicket = ajv.compile(transferFreeTicketSchema);

// 定义批量转账免费门票请求体验证模式
const batchTransferFreeTicketsSchema = {
  type: "object",
  properties: {
    transfers: {
      type: "array",
      items: {
        type: "object",
        properties: {
          toWalletAddress: { type: "string" },
          amount: { type: "integer", minimum: 1 }
        },
        required: ["toWalletAddress", "amount"]
      },
      minItems: 1
    }
  },
  required: ["transfers"]
};

const validateBatchTransferFreeTickets = ajv.compile(batchTransferFreeTicketsSchema);

/**
 * POST /api/free-ticket/transfer
 * 转账免费门票
 * body: { toWalletAddress, amount }
 * header: { Authorization: "Bearer <token>" }
 */
//@ts-ignore
router.post("/transfer", walletAuthMiddleware, async (req, res) => {
  try {
    const myReq = req as MyRequest;

    // 验证请求体
    const valid = validateTransferFreeTicket(req.body);
    if (!valid) {
      return res.status(400).json(errorResponse(
        tFromRequest(req, "errors.paramValidation"),
        formatValidationErrors(validateTransferFreeTicket.errors || [], req.language)
      ));
    }

    const { userId, walletId } = myReq.user!;
    const { toWalletAddress, amount } = req.body;

    const result = await transferFreeTicket(userId!, walletId!, toWalletAddress, amount);
    res.json(successResponse(
      {
        fromWalletId: walletId,
        toWalletAddress,
        amount,
        timestamp: result.data.timestamp
      },
      tFromRequest(req, "success.transferFreeTicketSuccess")
    ));
  } catch (err: any) {
    res.status(400).json(errorResponse(err.message));
  }
});

/**
 * GET /api/free-ticket/remaining-limit
 * 获取今日剩余可转账的免费门票数量
 * header: { Authorization: "Bearer <token>" }
 */
//@ts-ignore
router.get("/remaining-limit", walletAuthMiddleware, async (req, res) => {
  try {
    const myReq = req as MyRequest;
    const { userId, walletId } = myReq.user!;

    const remainingLimit = await getRemainingDailyTransferLimit(userId!, walletId!);
    res.json(successResponse(
      { remainingLimit },
      tFromRequest(req, "success.getRemainingTransferLimit")
    ));
  } catch (err: any) {
    res.status(400).json(errorResponse(err.message));
  }
});

/**
 * POST /api/free-ticket/batch-transfer
 * 批量转账免费门票
 * body: { transfers: [{ toWalletAddress, amount }] }
 * header: { Authorization: "Bearer <token>" }
 */
//@ts-ignore
router.post("/batch-transfer", walletAuthMiddleware, async (req, res) => {
  try {
    const myReq = req as MyRequest;

    // 验证请求体
    const valid = validateBatchTransferFreeTickets(req.body);
    if (!valid) {
      return res.status(400).json(errorResponse(
        tFromRequest(req, "errors.paramValidation"),
        formatValidationErrors(validateBatchTransferFreeTickets.errors || [], req.language)
      ));
    }

    const { userId, walletId } = myReq.user!;
    const { transfers } = req.body;

    const result = await batchTransferFreeTickets(userId!, walletId!, transfers);
    res.json(successResponse(
      {
        fromWalletId: walletId,
        transfers: result.data.transfers,
        totalAmount: result.data.totalAmount,
        timestamp: result.data.timestamp
      },
      tFromRequest(req, "success.transferFreeTicketSuccess")
    ));
  } catch (err: any) {
    res.status(400).json(errorResponse(err.message));
  }
});

export default router;