// src/routes/gemLeaderboardRoutes.ts
import { Router } from "express";
import { getGemLeaderboard } from "../services/gemLeaderboardService";
import { walletAuthMiddleware } from "../middlewares/walletAuth";
import { MyRequest } from "../types/customRequest";
import { ajv, tFromRequest, formatValidationErrors } from "../i18n";
import { languageMiddleware } from "../middlewares/languageMiddleware";
import { successResponse, errorResponse } from "../utils/responseUtil";

const router = Router();

// 应用语言中间件到所有路由
router.use(languageMiddleware);

// 定义查询参数验证模式
const leaderboardQuerySchema = {
  type: "object",
  properties: {
    limit: { type: "string", pattern: "^[0-9]+$" },
    offset: { type: "string", pattern: "^[0-9]+$" }
  }
};

const validateLeaderboardQuery = ajv.compile(leaderboardQuerySchema);

/**
 * @api {get} /api/gem-leaderboard 获取Gem排行榜
 * @apiName GetGemLeaderboard
 * @apiGroup GemLeaderboard
 * @apiParam {Number} [limit=100] 限制返回的记录数量
 * @apiParam {Number} [offset=0] 分页偏移量
 * @apiSuccess {Array} data.leaderboard 排行榜数据
 * @apiSuccess {Object} data.pagination 分页信息
 * @apiSuccess {Object} data.userInfo 用户个人信息（包含用户排名和gem数量）
 */
//@ts-ignore
router.get("/", walletAuthMiddleware, async (req, res) => {
  try {
    const valid = validateLeaderboardQuery(req.query);
    if (!valid) {
      return res.status(400).json(errorResponse(
        tFromRequest(req, "errors.paramValidation"),
        formatValidationErrors(validateLeaderboardQuery.errors || [], req.language)
      ));
    }

    const myReq = req as MyRequest;
    const { walletId } = myReq.user!;
    const limit = req.query.limit ? parseInt(req.query.limit as string) : 100;
    const offset = req.query.offset ? parseInt(req.query.offset as string) : 0;

    if (isNaN(limit) || limit <= 0) {
      return res.status(400).json(errorResponse(
        tFromRequest(req, "errors.invalidLimit")
      ));
    }

    if (isNaN(offset) || offset < 0) {
      return res.status(400).json(errorResponse(
        tFromRequest(req, "errors.invalidOffset")
      ));
    }

    const result = await getGemLeaderboard(limit, offset, walletId);

    res.json(successResponse(result));
  } catch (error) {
    console.error("获取Gem排行榜失败:", error);
    res.status(500).json(errorResponse(
      tFromRequest(req, "errors.getGemLeaderboardFailed"),
      (error as Error).message
    ));
  }
});

export default router;