// src/routes/kolProgressRoutes.ts
import express from "express";
import { walletAuthMiddleware } from "../middlewares/walletAuth";
import { MyRequest } from "../types/customRequest";
import {
  getPersonalKolProgress,
  getTeamKolProgress,
  getDailyPromotionProgress,
  getUserGameStats
} from "../services/kolProgressService";
import { ajv, tFromRequest, formatValidationErrors } from "../i18n";
import { languageMiddleware } from "../middlewares/languageMiddleware";
import { successResponse, errorResponse } from "../utils/responseUtil";

const router = express.Router();

// 应用语言中间件到所有路由
router.use(languageMiddleware);

/**
 * @api {get} /api/kol-progress/personal 获取个人KOL等级进度
 * @apiName GetPersonalKolProgress
 * @apiGroup KolProgress
 * @apiSuccess {Object} data 个人KOL等级进度数据
 */
router.get("/personal", walletAuthMiddleware, async (req, res) => {
  try {
    const myReq = req as MyRequest;
    const { userId } = myReq.user!;

    const progress = await getPersonalKolProgress(userId);

    res.json(successResponse(
      progress,
      tFromRequest(req, "success.getPersonalKolProgress")
    ));
  } catch (error) {
    console.error("获取个人KOL等级进度失败:", error);
    res.status(500).json(errorResponse(
      tFromRequest(req, "errors.getPersonalKolProgressFailed"),
      (error as Error).message
    ));
  }
});

/**
 * @api {get} /api/kol-progress/team 获取团队KOL等级进度
 * @apiName GetTeamKolProgress
 * @apiGroup KolProgress
 * @apiSuccess {Object} data 团队KOL等级进度数据
 */
router.get("/team", walletAuthMiddleware, async (req, res) => {
  try {
    const myReq = req as MyRequest;
    const { userId } = myReq.user!;

    const progress = await getTeamKolProgress(userId);

    res.json(successResponse(
      progress,
      tFromRequest(req, "success.getTeamKolProgress")
    ));
  } catch (error) {
    console.error("获取团队KOL等级进度失败:", error);
    res.status(500).json(errorResponse(
      tFromRequest(req, "errors.getTeamKolProgressFailed"),
      (error as Error).message
    ));
  }
});

/**
 * @api {get} /api/kol-progress/daily-promotion 今日和总推广tree-view
 */
//@ts-ignore
router.get("/daily-promotion", walletAuthMiddleware, async (req, res) => {
  try {
    const myReq = req as MyRequest;

    let { userId, walletId } = myReq.user!;

    //walletId 可以传递参数
    if (req.query.walletId) {
      walletId = Number(req.query.walletId as string);
    }

    if (!walletId) {
      return res.status(400).json(errorResponse(
        tFromRequest(req, "errors.missingWalletId")
      ));
    }

    const progress = await getDailyPromotionProgress(userId, walletId, 1);

    const reffStats = await getUserGameStats(userId, walletId);

    // 添加默认值，确保返回完整的数据结构
    const response = {
      todayGameCount: progress.todayGameCount || 0,
      totalGameCount: progress.totalGameCount || 0,
      todayBetAmount: progress.todayBetAmount || 0,
      totalBetAmount: progress.totalBetAmount || 0,
      reffStats,
      referralUsers: progress.expandedLevelUsers || [],
    };

    res.json(successResponse(
      response,
      tFromRequest(req, "success.getDailyPromotionProgress")
    ));
  } catch (error) {
    console.error("获取每日推广奖励进度失败:", error);
    res.status(500).json(errorResponse(
      tFromRequest(req, "errors.getDailyPromotionProgressFailed"),
      (error as Error).message
    ));
  }
});


export default router;