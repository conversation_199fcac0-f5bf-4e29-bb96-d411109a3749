// src/routes/phrsPaymentRoutes.ts
import { Router } from 'express';
import { phrsPaymentController } from '../controllers/phrsPaymentController';
import { walletAuthMiddleware } from '../middlewares/walletAuth';
import { languageMiddleware } from '../middlewares/languageMiddleware';
import { ajv, tFromRequest, formatValidationErrors } from '../i18n';

const router = Router();

// 应用语言中间件到所有路由
router.use(languageMiddleware);

// PHRS购买请求验证模式
const purchaseWithPhrsSchema = {
  type: 'object',
  properties: {
    productId: {
      type: 'integer',
      minimum: 1
    }
  },
  required: ['productId'],
  additionalProperties: false
};

const validatePurchaseWithPhrs = ajv.compile(purchaseWithPhrsSchema);

/**
 * 使用PHRS购买IAP道具
 * POST /api/phrs-payment/purchase
 */
router.post('/purchase', walletAuthMiddleware, async (req, res) => {
  try {
    // 验证请求体
    const valid = validatePurchaseWithPhrs(req.body);
    if (!valid) {
      res.status(400).json({
        ok: false,
        message: tFromRequest(req, 'errors.paramValidation'),
        details: formatValidationErrors(validatePurchaseWithPhrs.errors || [], req.language)
      });
      return;
    }

    await phrsPaymentController.purchaseWithPhrs(req as any, res);
  } catch (error: any) {
    console.error('PHRS购买路由错误:', error);
    res.status(500).json({
      ok: false,
      message: tFromRequest(req, 'errors.serverError'),
      details: error.message
    });
  }
});

/**
 * 获取用户PHRS余额和购买历史
 * GET /api/phrs-payment/balance
 */
router.get('/balance', walletAuthMiddleware, async (req, res) => {
  await phrsPaymentController.getPhrsBalance(req as any, res);
});

/**
 * 获取支持PHRS支付的商品列表
 * GET /api/phrs-payment/products
 */
router.get('/products', walletAuthMiddleware, async (req, res) => {
  await phrsPaymentController.getPhrsProducts(req as any, res);
});

/**
 * 健康检查接口
 * GET /api/phrs-payment/health
 */
router.get('/health', (req, res) => {
  res.json({
    ok: true,
    data: {
      service: 'PHRS Payment Service',
      status: 'running',
      timestamp: new Date().toISOString()
    }
  });
});

export default router;
