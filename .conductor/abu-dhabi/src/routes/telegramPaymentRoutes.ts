import express from "express";
import * as jackpotChestController from "../controllers/jackpotChestController";
import { languageMiddleware } from "../middlewares/languageMiddleware";
import { walletAuthMiddleware } from "../middlewares/walletAuth";

const router = express.Router();

// 使用语言中间件
router.use(languageMiddleware);

// 创建自动领取功能的支付链接 - 需要Telegram认证
router.post("/create-star-invoice", walletAuthMiddleware, async (req, res) => {
  await jackpotChestController.createStarInvoiceLink(req, res);
});

// 处理支付成功的回调
router.post("/payment-callback", async (req, res) => {
  await jackpotChestController.handlePaymentCallback(req, res);
});

export default router;