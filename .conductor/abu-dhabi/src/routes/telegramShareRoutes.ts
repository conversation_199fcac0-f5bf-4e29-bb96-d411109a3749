import express from "express";
import * as jackpotChestController from "../controllers/jackpotChestController";
import { languageMiddleware } from "../middlewares/languageMiddleware";
import { telegramAuthMiddleware } from "../middlewares/telegramAuth";

const router = express.Router();

// 使用语言中间件
router.use(languageMiddleware);

// 使用Telegram分享助力链接 - 不需要钱包认证，只需要Telegram认证
router.post("/boost", telegramAuthMiddleware, async (req, res) => {
  await jackpotChestController.useTelegramShareBoostLink(req, res);
});

export default router;