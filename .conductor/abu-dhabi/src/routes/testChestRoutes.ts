// src/routes/testChestRoutes.ts
import express from "express";
import * as testChestController from "../controllers/testChestController";
import { walletAuthMiddleware } from "../middlewares/walletAuth";
import { languageMiddleware } from "../middlewares/languageMiddleware";

const router = express.Router();

// 应用中间件
router.use(walletAuthMiddleware);
router.use(languageMiddleware);


// 创建测试宝箱
router.post("/create-test-chests", async (req, res) => {
  await testChestController.createTestChests(req, res);
});

// 重置宝箱倒计时
router.post("/reset-chest-countdown", async (req, res) => {
  await testChestController.resetChestCountdown(req, res);
});

// 重置每日推荐宝箱领取状态
router.post("/reset-daily-chest-claim", async (req, res) => {
  await testChestController.resetDailyChestClaim(req, res);
});

// 重置任务完成状态
router.post("/reset-task-complete", async (req, res) => {
  await testChestController.resetTaskComplete(req, res);
});

// 直接设置用户referralCount字段值
router.post("/reset-referral-count", async (req, res) => {
  await testChestController.setReferralCount(req, res);
});

// 重置一次性4个宝箱领取状态
router.post("/reset-four-chests-collect", async (req, res) => {
  await testChestController.resetFourChestsCollect(req, res);
});

// 设置奖池金额
router.post("/set-jackpot-pool-amount", async (req, res) => {
  await testChestController.setJackpotPoolAmount(req, res);
});

// 重置所有奖池金额为0
router.post("/reset-all-jackpot-pools", async (req, res) => {
  await testChestController.resetAllJackpotPools(req, res);
});

export default router;