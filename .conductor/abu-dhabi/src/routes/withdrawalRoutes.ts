// src/routes/withdrawalRoutes.ts
import express from "express";
import { getWithdrawalSettings, withdrawUSD, withdrawMOOF, withdrawTON, getMoofBalances } from "../services/withdrawalService";
import { walletAuthMiddleware } from "../middlewares/walletAuth";
import { MyRequest } from "../types/customRequest";
import { UserWallet } from "../models";
import { ajv, tFromRequest, formatValidationErrors } from "../i18n";
import { languageMiddleware } from "../middlewares/languageMiddleware";
import { successResponse, errorResponse } from "../utils/responseUtil";

const router = express.Router();

// 应用语言中间件到所有路由
router.use(languageMiddleware);

/**
 * @api {get} /api/withdrawal/settings 获取提现设置
 * @apiName GetWithdrawalSettings
 * @apiGroup Withdrawal
 * @apiSuccess {Object} data 提现设置数据
 */
router.get("/settings", async (req, res) => {
  try {
    const settings = await getWithdrawalSettings();
    
    res.json(successResponse(
      settings,
      tFromRequest(req, "success.getWithdrawalSettings")
    ));
  } catch (error) {
    console.error("获取提现设置失败:", error);
    res.status(500).json(errorResponse(
      tFromRequest(req, "errors.getWithdrawalSettingsFailed"),
      (error as Error).message
    ));
  }
});

/**
 * @api {post} /api/withdrawal/usd 提现USD
 * @apiName WithdrawUSD
 * @apiGroup Withdrawal
 * @apiParam {Number} amount 提现金额
 * @apiSuccess {Object} data 提现结果
 */

// 定义USD提现请求体验证模式
const withdrawUSDSchema = {
  type: "object",
  properties: {
    amount: { type: "number", minimum: 10 }
  },
  required: ["amount"]
};

const validateWithdrawUSD = ajv.compile(withdrawUSDSchema);

//@ts-ignore
router.post("/usd", walletAuthMiddleware, async (req, res) => {
  try {
    const myReq = req as MyRequest;
    const { userId, walletId } = myReq.user!;
    
    // 验证请求体
    const valid = validateWithdrawUSD(req.body);
    if (!valid) {
      return res.status(400).json(errorResponse(
        tFromRequest(req, "errors.paramValidation"),
        formatValidationErrors(validateWithdrawUSD.errors || [], req.language)
      ));
    }
    
    const { amount } = req.body;
    
    if (!walletId) {
      return res.status(400).json(errorResponse(tFromRequest(req, "errors.walletInfoNotFound")));
    }
    
    // 如果未提供地址，则获取用户钱包地址
    let withdrawalAddress;
    if (!withdrawalAddress) {
      const wallet = await UserWallet.findByPk(walletId);
      if (!wallet || !wallet.walletAddress) {
        return res.status(400).json(errorResponse(tFromRequest(req, "errors.withdrawalAddressNotFound")));
      }
      withdrawalAddress = wallet.walletAddress;
    }
    
    const result = await withdrawUSD(userId, walletId, parseFloat(amount), withdrawalAddress);
    
    res.json(successResponse(
      result,
      tFromRequest(req, "success.withdrawUSDSuccess")
    ));
  } catch (error) {
    console.error("USD提现失败:", error);
    res.status(500).json(errorResponse(
      tFromRequest(req, "errors.withdrawUSDFailed"),
      (error as Error).message
    ));
  }
});

/**
 * @api {post} /api/withdrawal/moof 提现MOOF
 * @apiName WithdrawMOOF
 * @apiGroup Withdrawal
 * @apiParam {Number} amount 提现金额
 * @apiSuccess {Object} data 提现结果
 */

// 定义MOOF提现请求体验证模式
const withdrawMOOFSchema = {
  type: "object",
  properties: {
    amount: { type: "number", minimum: 21 }
  },
  required: ["amount"]
};

const validateWithdrawMOOF = ajv.compile(withdrawMOOFSchema);

//@ts-ignore
router.post("/moof", walletAuthMiddleware, async (req, res) => {
  try {
    const myReq = req as MyRequest;
    const { userId, walletId } = myReq.user!;
    
    // 验证请求体
    const valid = validateWithdrawMOOF(req.body);
    if (!valid) {
      return res.status(400).json(errorResponse(
        tFromRequest(req, "errors.paramValidation"),
        formatValidationErrors(validateWithdrawMOOF.errors || [], req.language)
      ));
    }
    
    const { amount } = req.body;
    
    if (!walletId) {
      return res.status(400).json(errorResponse(tFromRequest(req, "errors.walletInfoNotFound")));
    }
    
    // 如果未提供地址，则获取用户钱包地址
    let withdrawalAddress;
    if (!withdrawalAddress) {
      const wallet = await UserWallet.findByPk(walletId);
      if (!wallet || !wallet.walletAddress) {
        return res.status(400).json(errorResponse(tFromRequest(req, "errors.withdrawalAddressNotFound")));
      }
      withdrawalAddress = wallet.walletAddress;
    }
    
    const result = await withdrawMOOF(userId, walletId, parseFloat(amount), withdrawalAddress);
    
    res.json(successResponse(
      result,
      tFromRequest(req, "success.withdrawMOOFSuccess")
    ));
  } catch (error) {
    console.error("MOOF提现失败:", error);
    res.status(500).json(errorResponse(
      tFromRequest(req, "errors.withdrawMOOFFailed"),
      (error as Error).message
    ));
  }
});

/**
 * @api {post} /api/withdrawal/ton 提现TON
 * @apiName WithdrawTON
 * @apiGroup Withdrawal
 * @apiParam {Number} amount 提现金额
 * @apiSuccess {Object} data 提现结果
 */

// 定义TON提现请求体验证模式
const withdrawTONSchema = {
  type: "object",
  properties: {
    amount: { type: "number", minimum: 1 }
  },
  required: ["amount"]
};

const validateWithdrawTON = ajv.compile(withdrawTONSchema);

//@ts-ignore
router.post("/ton", walletAuthMiddleware, async (req, res) => {
  try {
    const myReq = req as MyRequest;
    const { userId, walletId } = myReq.user!;
    
    // 验证请求体
    const valid = validateWithdrawTON(req.body);
    if (!valid) {
      return res.status(400).json(errorResponse(
        tFromRequest(req, "errors.paramValidation"),
        formatValidationErrors(validateWithdrawTON.errors || [], req.language)
      ));
    }
    
    const { amount } = req.body;
    
    if (!walletId) {
      return res.status(400).json(errorResponse(tFromRequest(req, "errors.walletInfoNotFound")));
    }
    
    // 如果未提供地址，则获取用户钱包地址
    let withdrawalAddress;
    if (!withdrawalAddress) {
      const wallet = await UserWallet.findByPk(walletId);
      if (!wallet || !wallet.walletAddress) {
        return res.status(400).json(errorResponse(tFromRequest(req, "errors.withdrawalAddressNotFound")));
      }
      withdrawalAddress = wallet.walletAddress;
    }
    
    const result = await withdrawTON(userId, walletId, parseFloat(amount), withdrawalAddress);
    
    res.json(successResponse(
      result,
      tFromRequest(req, "success.withdrawTONSuccess")
    ));
  } catch (error) {
    console.error("TON提现失败:", error);
    res.status(500).json(errorResponse(
      tFromRequest(req, "errors.withdrawTONFailed"),
      (error as Error).message
    ));
  }
});

/**
 * @api {get} /api/withdrawal/moof-balances 获取MOOF相关数量
 * @apiName GetMoofBalances
 * @apiGroup Withdrawal
 * @apiSuccess {Object} data MOOF相关数量
 */
//@ts-ignore
router.get("/moof-balances", walletAuthMiddleware, async (req, res) => {
  try {
    const myReq = req as MyRequest;
    const { walletId } = myReq.user!;
    
    if (!walletId) {
      return res.status(400).json(errorResponse(tFromRequest(req, "errors.walletInfoNotFound")));
    }
    
    const balances = await getMoofBalances(walletId);
    
    res.json(successResponse(
      balances,
      tFromRequest(req, "success.getMoofBalancesSuccess")
    ));
  } catch (error) {
    console.error("获取MOOF相关数量失败:", error);
    res.status(500).json(errorResponse(
      tFromRequest(req, "errors.getMoofBalancesFailed"),
      (error as Error).message
    ));
  }
});

export default router;