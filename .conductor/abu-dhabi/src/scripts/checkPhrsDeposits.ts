#!/usr/bin/env ts-node

import dotenv from 'dotenv';
import { connectDB } from '../config/db';
import { PhrsDeposit } from '../models';

// 加载环境变量
dotenv.config();

async function main() {
  console.log('🔍 检查PHRS充值记录');
  console.log('==================');

  try {
    // 连接数据库
    await connectDB();
    console.log('✅ 数据库连接成功');

    // 查询所有充值记录
    const deposits = await PhrsDeposit.findAll({
      order: [['createdAt', 'DESC']],
      limit: 10
    });

    console.log(`📊 找到 ${deposits.length} 条充值记录`);

    if (deposits.length === 0) {
      console.log('⚠️  没有找到任何充值记录');
      return;
    }

    console.log('\n📝 最近的充值记录:');
    deposits.forEach((deposit, index) => {
      console.log(`\n${index + 1}. 记录ID: ${deposit.id}`);
      console.log(`   钱包ID: ${deposit.walletId || 'NULL (未注册用户)'}`);
      console.log(`   用户地址: ${deposit.userAddress}`);
      console.log(`   充值金额: ${deposit.amount} PHRS`);
      console.log(`   交易哈希: ${deposit.transactionHash}`);
      console.log(`   区块号: ${deposit.blockNumber}`);
      console.log(`   状态: ${deposit.status}`);
      console.log(`   确认数: ${deposit.confirmations}`);
      console.log(`   处理时间: ${deposit.processedAt}`);
      if (deposit.errorMessage) {
        console.log(`   错误信息: ${deposit.errorMessage}`);
      }
      console.log(`   创建时间: ${deposit.createdAt}`);
    });

    // 统计信息
    const totalCount = await PhrsDeposit.count();
    const confirmedCount = await PhrsDeposit.count({ where: { status: 'CONFIRMED' } });
    const failedCount = await PhrsDeposit.count({ where: { status: 'FAILED' } });
    const nullWalletCount = await PhrsDeposit.count({ where: { walletId: null } });

    console.log('\n📊 统计信息:');
    console.log(`   总记录数: ${totalCount}`);
    console.log(`   成功记录: ${confirmedCount}`);
    console.log(`   失败记录: ${failedCount}`);
    console.log(`   未注册用户记录: ${nullWalletCount}`);

    // 检查特定交易
    const targetTx = '0xe8713ec116609c9ca63d198c206d9f31dbc0ee13f548d5ce301c715472b62e85';
    const targetDeposit = await PhrsDeposit.findOne({
      where: { transactionHash: targetTx }
    });

    if (targetDeposit) {
      console.log(`\n🎯 目标交易 ${targetTx}:`);
      console.log(`   ✅ 已找到记录 (ID: ${targetDeposit.id})`);
      console.log(`   状态: ${targetDeposit.status}`);
      console.log(`   钱包ID: ${targetDeposit.walletId || 'NULL'}`);
      console.log(`   错误信息: ${targetDeposit.errorMessage || '无'}`);
    } else {
      console.log(`\n❌ 未找到目标交易 ${targetTx} 的记录`);
    }

  } catch (error) {
    console.error('❌ 检查过程中发生错误:', error);
  }

  process.exit(0);
}

// 运行主函数
main().catch((error) => {
  console.error('脚本执行失败:', error);
  process.exit(1);
});
