#!/usr/bin/env ts-node

import dotenv from 'dotenv';
import { connectDB, sequelize } from '../config/db';
import { UserWallet, PhrsDeposit } from '../models';
import readline from 'readline';

// 加载环境变量
dotenv.config();

// 创建命令行接口
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

function askQuestion(question: string): Promise<string> {
  return new Promise((resolve) => {
    rl.question(question, (answer) => {
      resolve(answer);
    });
  });
}

async function main() {
  console.log('🔧 处理未注册用户的充值记录');
  console.log('===============================');

  try {
    // 连接数据库
    await connectDB();
    console.log('✅ 数据库连接成功');

    // 1. 查询未注册用户的充值记录
    const unregisteredDeposits = await PhrsDeposit.findAll({
      where: {
        walletId: 0,
        status: 'FAILED',
        errorMessage: 'User wallet not found'
      },
      order: [['blockNumber', 'DESC']]
    });

    console.log(`📝 找到 ${unregisteredDeposits.length} 条未注册用户的充值记录`);

    if (unregisteredDeposits.length === 0) {
      console.log('✅ 没有需要处理的未注册用户充值记录');
      return;
    }

    // 2. 按地址分组
    const addressGroups = new Map<string, typeof unregisteredDeposits>();
    unregisteredDeposits.forEach(deposit => {
      const address = deposit.userAddress;
      if (!addressGroups.has(address)) {
        addressGroups.set(address, []);
      }
      addressGroups.get(address)!.push(deposit);
    });

    console.log(`📊 涉及 ${addressGroups.size} 个不同的地址`);

    // 3. 显示地址列表
    console.log(`\n📋 未注册地址列表:`);
    const addresses = Array.from(addressGroups.keys());
    addresses.forEach((address, index) => {
      const deposits = addressGroups.get(address)!;
      const totalAmount = deposits.reduce((sum, d) => sum + parseFloat(d.amount.toString()), 0);
      console.log(`${index + 1}. ${address}`);
      console.log(`   充值次数: ${deposits.length}`);
      console.log(`   总金额: ${totalAmount.toFixed(3)} PHRS`);
    });

    // 4. 询问处理方式
    console.log(`\n🔧 处理选项:`);
    console.log(`1. 为所有地址创建用户钱包并处理充值`);
    console.log(`2. 为特定地址创建用户钱包并处理充值`);
    console.log(`3. 标记所有充值为已处理但不创建钱包`);
    console.log(`4. 仅查看详情，不做任何处理`);
    console.log(`5. 退出`);

    const choice = await askQuestion('\n请选择处理方式 (1-5): ');

    switch (choice) {
      case '1':
        await handleAllAddresses(addressGroups);
        break;
      case '2':
        await handleSpecificAddresses(addresses, addressGroups);
        break;
      case '3':
        await markAsProcessed(unregisteredDeposits);
        break;
      case '4':
        await showDetails(addressGroups);
        break;
      case '5':
        console.log('👋 退出程序');
        break;
      default:
        console.log('❌ 无效选择');
    }

  } catch (error) {
    console.error('❌ 处理过程中发生错误:', error);
  } finally {
    rl.close();
  }

  process.exit(0);
}

async function handleAllAddresses(addressGroups: Map<string, any[]>) {
  console.log('\n🔄 为所有地址创建用户钱包...');
  
  const confirm = await askQuestion(`确认为 ${addressGroups.size} 个地址创建用户钱包? (y/N): `);
  if (confirm.toLowerCase() !== 'y') {
    console.log('❌ 操作已取消');
    return;
  }

  const transaction = await sequelize.transaction();
  
  try {
    let createdWallets = 0;
    let processedDeposits = 0;

    for (const [address, deposits] of addressGroups) {
      // 创建用户钱包
      const wallet = await UserWallet.create({
        userId: 0, // 临时用户ID，表示未关联具体用户
        milk: 0, // 初始牛奶为0
        phrsWalletAddress: address.toLowerCase(),
        phrsBalance: '0.000',
      }, { transaction });

      console.log(`✅ 为地址 ${address} 创建了钱包 (ID: ${wallet.id})`);
      createdWallets++;

      // 更新充值记录
      for (const deposit of deposits) {
        await deposit.update({
          walletId: wallet.id,
          status: 'CONFIRMED',
          errorMessage: null,
          processedAt: new Date()
        }, { transaction });

        // 更新钱包余额
        const currentBalance = parseFloat((wallet.phrsBalance || '0').toString());
        const newBalance = currentBalance + parseFloat(deposit.amount.toString());
        await wallet.update({
          phrsBalance: newBalance.toFixed(3),
          lastPhrsUpdateTime: new Date()
        }, { transaction });

        processedDeposits++;
      }
    }

    await transaction.commit();
    
    console.log(`\n🎉 处理完成:`);
    console.log(`   创建钱包: ${createdWallets} 个`);
    console.log(`   处理充值: ${processedDeposits} 条`);

  } catch (error) {
    await transaction.rollback();
    console.error('❌ 处理失败，已回滚:', error);
  }
}

async function handleSpecificAddresses(addresses: string[], addressGroups: Map<string, any[]>) {
  console.log('\n📋 选择要处理的地址:');
  
  const selectedIndices: number[] = [];
  while (true) {
    const input = await askQuestion('输入地址编号 (多个用逗号分隔，输入 "done" 完成选择): ');
    
    if (input.toLowerCase() === 'done') {
      break;
    }

    const indices = input.split(',').map(s => parseInt(s.trim()) - 1);
    for (const index of indices) {
      if (index >= 0 && index < addresses.length && !selectedIndices.includes(index)) {
        selectedIndices.push(index);
        console.log(`✅ 已选择: ${addresses[index]}`);
      }
    }
  }

  if (selectedIndices.length === 0) {
    console.log('❌ 没有选择任何地址');
    return;
  }

  const selectedAddresses = selectedIndices.map(i => addresses[i]);
  const selectedGroups = new Map();
  selectedAddresses.forEach(addr => {
    selectedGroups.set(addr, addressGroups.get(addr));
  });

  await handleAllAddresses(selectedGroups);
}

async function markAsProcessed(deposits: any[]) {
  console.log('\n🔄 标记充值为已处理...');
  
  const confirm = await askQuestion(`确认标记 ${deposits.length} 条充值为已处理? (y/N): `);
  if (confirm.toLowerCase() !== 'y') {
    console.log('❌ 操作已取消');
    return;
  }

  const transaction = await sequelize.transaction();
  
  try {
    for (const deposit of deposits) {
      await deposit.update({
        status: 'PROCESSED_NO_WALLET',
        errorMessage: 'Processed without creating wallet',
        processedAt: new Date()
      }, { transaction });
    }

    await transaction.commit();
    console.log(`✅ 已标记 ${deposits.length} 条充值为已处理`);

  } catch (error) {
    await transaction.rollback();
    console.error('❌ 标记失败，已回滚:', error);
  }
}

async function showDetails(addressGroups: Map<string, any[]>) {
  console.log('\n📝 详细信息:');
  console.log('=====================================');

  for (const [address, deposits] of addressGroups) {
    console.log(`\n📍 地址: ${address}`);
    console.log(`充值记录 (${deposits.length} 条):`);
    
    deposits.forEach((deposit, index) => {
      console.log(`  ${index + 1}. 区块: ${deposit.blockNumber}`);
      console.log(`     金额: ${deposit.amount} PHRS`);
      console.log(`     交易: ${deposit.transactionHash}`);
      console.log(`     时间: ${deposit.blockTimestamp}`);
    });
  }
}

// 运行主函数
main().catch((error) => {
  console.error('脚本执行失败:', error);
  process.exit(1);
});
