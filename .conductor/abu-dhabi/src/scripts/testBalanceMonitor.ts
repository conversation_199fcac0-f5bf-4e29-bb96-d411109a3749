#!/usr/bin/env ts-node

import dotenv from 'dotenv';
import { connectDB } from '../config/db';
import { phrsBalanceMonitor } from '../services/phrsBalanceMonitor';

// 加载环境变量
dotenv.config();

async function main() {
  console.log('🧪 测试PHRS余额监控功能');
  console.log('========================');

  try {
    // 连接数据库
    await connectDB();
    console.log('✅ 数据库连接成功');

    // 手动执行一次余额检查
    console.log('\n🔄 执行余额监控检查...');
    await phrsBalanceMonitor.runBalanceCheck();

    console.log('\n✅ 余额监控测试完成');

  } catch (error) {
    console.error('❌ 测试失败:', error);
    process.exit(1);
  }

  process.exit(0);
}

// 运行主函数
main().catch((error) => {
  console.error('脚本执行失败:', error);
  process.exit(1);
});
