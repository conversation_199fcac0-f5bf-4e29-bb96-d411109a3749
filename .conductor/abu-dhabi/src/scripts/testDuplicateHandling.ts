#!/usr/bin/env ts-node

import dotenv from 'dotenv';
import { connectDB } from '../config/db';
import { phrsDepositService } from '../services/phrsDepositService';
import { PhrsDeposit } from '../models';

// 加载环境变量
dotenv.config();

async function main() {
  console.log('🧪 测试重复交易处理');
  console.log('==================');

  try {
    // 连接数据库
    await connectDB();
    console.log('✅ 数据库连接成功');

    // 检查目标交易的当前状态
    const targetTx = '0xe8713ec116609c9ca63d198c206d9f31dbc0ee13f548d5ce301c715472b62e85';
    console.log(`\n🔍 检查交易 ${targetTx} 的状态...`);

    const existingRecords = await PhrsDeposit.findAll({
      where: { transactionHash: targetTx }
    });

    console.log(`📊 找到 ${existingRecords.length} 条相关记录:`);
    existingRecords.forEach((record, index) => {
      console.log(`   ${index + 1}. ID: ${record.id}, 状态: ${record.status}, 钱包ID: ${record.walletId || 'NULL'}`);
      console.log(`      错误信息: ${record.errorMessage || '无'}`);
      console.log(`      创建时间: ${record.createdAt}`);
    });

    // 测试重复处理
    console.log(`\n🔄 测试重复处理区块 13805521...`);
    
    try {
      await phrsDepositService.testProcessBlocks(13805521);
      console.log('✅ 重复处理测试完成');
    } catch (error) {
      console.error('❌ 重复处理测试失败:', error);
    }

    // 再次检查记录状态
    console.log(`\n🔍 重新检查交易记录...`);
    const updatedRecords = await PhrsDeposit.findAll({
      where: { transactionHash: targetTx }
    });

    console.log(`📊 现在有 ${updatedRecords.length} 条记录:`);
    updatedRecords.forEach((record, index) => {
      console.log(`   ${index + 1}. ID: ${record.id}, 状态: ${record.status}, 钱包ID: ${record.walletId || 'NULL'}`);
      console.log(`      错误信息: ${record.errorMessage || '无'}`);
      console.log(`      更新时间: ${record.updatedAt}`);
    });

    // 验证结果
    if (updatedRecords.length === existingRecords.length) {
      console.log('\n✅ 重复处理测试通过：没有创建重复记录');
    } else {
      console.log('\n❌ 重复处理测试失败：创建了重复记录');
    }

  } catch (error) {
    console.error('❌ 测试失败:', error);
  }

  process.exit(0);
}

// 运行主函数
main().catch((error) => {
  console.error('脚本执行失败:', error);
  process.exit(1);
});
