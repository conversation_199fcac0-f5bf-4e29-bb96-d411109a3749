#!/usr/bin/env ts-node

import dotenv from 'dotenv';
import { connectDB } from '../config/db';
import { PhrsDeposit } from '../models';
import { phrsDepositService } from '../services/phrsDepositService';
import readline from 'readline';

// 加载环境变量
dotenv.config();

// 创建命令行接口
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

function askQuestion(question: string): Promise<string> {
  return new Promise((resolve) => {
    rl.question(question, (answer) => {
      resolve(answer);
    });
  });
}

async function main() {
  console.log('🔍 PHRS历史交易查询测试');
  console.log('========================');

  try {
    // 连接数据库
    await connectDB();
    console.log('✅ 数据库连接成功');

    // 显示当前配置
    console.log('\n📋 当前配置:');
    const historicalStartBlock = process.env.PHRS_HISTORICAL_START_BLOCK;
    console.log(`   PHRS_HISTORICAL_START_BLOCK: ${historicalStartBlock || '未设置 (默认为0)'}`);
    
    // 显示数据库状态
    const totalDeposits = await PhrsDeposit.count();
    console.log(`   数据库中现有充值记录: ${totalDeposits} 条`);
    
    if (totalDeposits > 0) {
      const latestDeposit = await PhrsDeposit.findOne({
        order: [['blockNumber', 'DESC']],
        limit: 1
      });
      const earliestDeposit = await PhrsDeposit.findOne({
        order: [['blockNumber', 'ASC']],
        limit: 1
      });
      
      if (latestDeposit && earliestDeposit) {
        console.log(`   区块范围: ${earliestDeposit.blockNumber} 到 ${latestDeposit.blockNumber}`);
      }
    }

    // 获取服务状态
    const status = phrsDepositService.getStatus();
    console.log(`   监控服务状态: ${status.isListening ? '运行中' : '已停止'}`);
    console.log(`   最后处理区块: ${status.lastProcessedBlock}`);

    // 显示选项
    console.log('\n🔧 测试选项:');
    console.log('1. 清空数据库并从指定区块开始处理历史交易');
    console.log('2. 从当前进度继续处理历史交易');
    console.log('3. 查询指定区块范围的历史交易（不保存到数据库）');
    console.log('4. 显示历史交易统计信息');
    console.log('5. 退出');

    const choice = await askQuestion('\n请选择操作 (1-5): ');

    switch (choice) {
      case '1':
        await clearAndProcessFromBlock();
        break;
      case '2':
        await continueProcessing();
        break;
      case '3':
        await queryBlockRange();
        break;
      case '4':
        await showStatistics();
        break;
      case '5':
        console.log('👋 退出程序');
        break;
      default:
        console.log('❌ 无效选择');
    }

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
  } finally {
    rl.close();
  }

  process.exit(0);
}

async function clearAndProcessFromBlock() {
  console.log('\n🗑️  清空数据库并从指定区块开始处理');
  console.log('=====================================');

  const startBlockInput = await askQuestion('请输入起始区块号 (例如: 13805521): ');
  const startBlock = parseInt(startBlockInput);

  if (isNaN(startBlock) || startBlock < 0) {
    console.log('❌ 无效的区块号');
    return;
  }

  const confirm = await askQuestion(`确认清空所有充值记录并从区块 ${startBlock} 开始处理? (y/N): `);
  if (confirm.toLowerCase() !== 'y') {
    console.log('❌ 操作已取消');
    return;
  }

  try {
    // 清空数据库
    console.log('🗑️  清空现有充值记录...');
    await PhrsDeposit.destroy({ where: {} });
    console.log('✅ 数据库已清空');

    // 设置环境变量（临时）
    process.env.PHRS_HISTORICAL_START_BLOCK = startBlock.toString();
    
    // 重新创建服务实例以使用新的配置
    console.log('🔄 重新初始化监控服务...');
    
    // 手动处理指定区块范围
    await phrsDepositService.testProcessBlocks(startBlock, startBlock + 5000); // 处理5000个区块

  } catch (error) {
    console.error('❌ 处理失败:', error);
  }
}

async function continueProcessing() {
  console.log('\n🔄 继续处理历史交易');
  console.log('===================');

  try {
    console.log('🔄 启动监控服务以处理历史交易...');
    await phrsDepositService.startListening();
    
    console.log('⏳ 等待30秒观察处理进度...');
    await new Promise(resolve => setTimeout(resolve, 30000));
    
    // 停止监控服务
    phrsDepositService.stopListening();
    console.log('⏹️  监控服务已停止');

  } catch (error) {
    console.error('❌ 处理失败:', error);
  }
}

async function queryBlockRange() {
  console.log('\n🔍 查询指定区块范围');
  console.log('==================');

  const fromBlockInput = await askQuestion('请输入起始区块号: ');
  const fromBlock = parseInt(fromBlockInput);

  const toBlockInput = await askQuestion('请输入结束区块号 (留空表示只查询起始区块): ');
  const toBlock = toBlockInput.trim() === '' ? fromBlock : parseInt(toBlockInput);

  if (isNaN(fromBlock) || isNaN(toBlock) || fromBlock < 0 || toBlock < fromBlock) {
    console.log('❌ 无效的区块范围');
    return;
  }

  try {
    console.log(`🔍 查询区块 ${fromBlock} 到 ${toBlock} 的历史交易...`);
    await phrsDepositService.testProcessBlocks(fromBlock, toBlock);

  } catch (error) {
    console.error('❌ 查询失败:', error);
  }
}

async function showStatistics() {
  console.log('\n📊 历史交易统计信息');
  console.log('==================');

  try {
    const totalCount = await PhrsDeposit.count();
    const confirmedCount = await PhrsDeposit.count({ where: { status: 'CONFIRMED' } });
    const failedCount = await PhrsDeposit.count({ where: { status: 'FAILED' } });

    console.log(`📊 总记录数: ${totalCount}`);
    console.log(`✅ 成功记录: ${confirmedCount}`);
    console.log(`❌ 失败记录: ${failedCount}`);

    if (totalCount > 0) {
      const latestDeposit = await PhrsDeposit.findOne({
        order: [['blockNumber', 'DESC']],
        limit: 1
      });
      const earliestDeposit = await PhrsDeposit.findOne({
        order: [['blockNumber', 'ASC']],
        limit: 1
      });

      if (latestDeposit && earliestDeposit) {
        console.log(`📦 区块范围: ${earliestDeposit.blockNumber} 到 ${latestDeposit.blockNumber}`);
        console.log(`⏰ 时间范围: ${earliestDeposit.createdAt} 到 ${latestDeposit.createdAt}`);
      }

      // 显示最近的几条记录
      const recentDeposits = await PhrsDeposit.findAll({
        order: [['createdAt', 'DESC']],
        limit: 5
      });

      console.log('\n📝 最近的充值记录:');
      recentDeposits.forEach((deposit, index) => {
        console.log(`   ${index + 1}. 区块 ${deposit.blockNumber}: ${deposit.amount} PHRS (${deposit.status})`);
      });
    }

  } catch (error) {
    console.error('❌ 获取统计信息失败:', error);
  }
}

// 运行主函数
main().catch((error) => {
  console.error('脚本执行失败:', error);
  process.exit(1);
});
