#!/usr/bin/env ts-node

import dotenv from 'dotenv';
import { ethers } from 'ethers';

// 加载环境变量
dotenv.config();

async function main() {
  console.log('🧪 PHRS监控功能测试');
  console.log('====================');

  const contractAddress = process.env.PHRS_DEPOSIT_CONTRACT_ADDRESS;
  const rpcUrl = process.env.PHAROS_RPC_URL || 'https://api.zan.top/node/v1/pharos/testnet/d42f5024864e431388c182f9e5052d47';

  if (!contractAddress) {
    console.log('❌ 请设置 PHRS_DEPOSIT_CONTRACT_ADDRESS 环境变量');
    return;
  }

  try {
    // 1. 连接网络
    console.log('\n📡 连接网络...');
    const provider = new ethers.JsonRpcProvider(rpcUrl);
    const network = await provider.getNetwork();
    const currentBlock = await provider.getBlockNumber();
    
    console.log(`✅ 网络: ${network.name} (Chain ID: ${network.chainId})`);
    console.log(`📊 当前区块: ${currentBlock}`);

    // 2. 连接合约
    console.log('\n📋 连接合约...');
    const contractABI = [
      "function getContractInfo() external view returns (uint256, uint256, uint256, uint256)",
      "event Deposit(address indexed user, uint256 amount, uint256 timestamp)"
    ];

    const contract = new ethers.Contract(contractAddress, contractABI, provider);
    
    // 3. 获取合约信息
    const contractInfo = await contract.getContractInfo();
    console.log(`✅ 合约连接成功`);
    console.log(`   最小充值金额: ${ethers.formatEther(contractInfo[0])} PHRS`);
    console.log(`   最大充值金额: ${ethers.formatEther(contractInfo[1])} PHRS`);
    console.log(`   合约总余额: ${ethers.formatEther(contractInfo[2])} PHRS`);
    console.log(`   合法充值总额: ${ethers.formatEther(contractInfo[3])} PHRS`);

    // 4. 测试事件查询
    console.log('\n🔍 测试事件查询...');
    const fromBlock = Math.max(0, currentBlock - 1000);
    
    console.log(`📊 查询区块范围: ${fromBlock} 到 ${currentBlock}`);
    
    // 方法1: 使用合约过滤器
    console.log('🔧 方法1: 使用合约过滤器...');
    const filter = contract.filters.Deposit();
    const events = await contract.queryFilter(filter, fromBlock, currentBlock);
    console.log(`📡 找到 ${events.length} 个事件`);

    if (events.length > 0) {
      console.log('📝 最近的事件:');
      events.slice(-3).forEach((event, index) => {
        if ('args' in event && event.args) {
          console.log(`   ${index + 1}. 区块: ${event.blockNumber}`);
          console.log(`      交易: ${event.transactionHash}`);
          console.log(`      用户: ${event.args[0]}`);
          console.log(`      金额: ${ethers.formatEther(event.args[1])} PHRS`);
          console.log(`      时间: ${new Date(Number(event.args[2]) * 1000).toLocaleString()}`);
        }
      });
    }

    // 方法2: 使用原始日志查询
    console.log('\n🔧 方法2: 使用原始日志查询...');
    const depositTopic = ethers.id("Deposit(address,uint256,uint256)");
    const rawFilter = {
      address: contractAddress,
      topics: [depositTopic],
      fromBlock: fromBlock,
      toBlock: currentBlock
    };

    const logs = await provider.getLogs(rawFilter);
    console.log(`📡 原始日志数量: ${logs.length}`);

    // 解析日志
    const parsedEvents = logs.map(log => {
      try {
        return contract.interface.parseLog(log);
      } catch (error) {
        console.log('解析日志失败:', error);
        return null;
      }
    }).filter(event => event !== null);

    console.log(`📊 成功解析的事件: ${parsedEvents.length}`);

    // 5. 测试特定区块范围
    if (events.length > 0) {
      const latestEvent = events[events.length - 1];
      const testFromBlock = latestEvent.blockNumber;
      const testToBlock = currentBlock;

      console.log(`\n🎯 测试特定区块范围: ${testFromBlock} 到 ${testToBlock}`);
      
      const testEvents = await contract.queryFilter(filter, testFromBlock, testToBlock);
      console.log(`📡 特定范围内的事件: ${testEvents.length}`);
    }

    // 6. 测试轮询逻辑
    console.log('\n🔄 模拟轮询逻辑...');
    let lastProcessedBlock = currentBlock - 10;
    
    for (let i = 0; i < 3; i++) {
      const newCurrentBlock = await provider.getBlockNumber();
      
      if (newCurrentBlock > lastProcessedBlock) {
        const pollFromBlock = lastProcessedBlock + 1;
        console.log(`   轮询 ${i + 1}: 检查区块 ${pollFromBlock} 到 ${newCurrentBlock}`);
        
        const pollEvents = await contract.queryFilter(filter, pollFromBlock, newCurrentBlock);
        console.log(`   找到 ${pollEvents.length} 个新事件`);
        
        lastProcessedBlock = newCurrentBlock;
      } else {
        console.log(`   轮询 ${i + 1}: 没有新区块`);
      }
      
      // 等待2秒
      await new Promise(resolve => setTimeout(resolve, 2000));
    }

    console.log('\n✅ 监控功能测试完成!');
    console.log('====================');
    
    if (events.length > 0) {
      console.log('🎉 事件查询功能正常，监控服务应该能够正常工作');
    } else {
      console.log('⚠️  最近1000个区块中没有充值事件，这可能是正常的');
      console.log('   建议执行一次充值测试来验证监控功能');
    }

  } catch (error) {
    console.error('❌ 测试失败:', error);
    if (error instanceof Error) {
      console.error('错误详情:', error.message);
      console.error('错误堆栈:', error.stack);
    }
  }
}

// 运行主函数
main().catch((error) => {
  console.error('脚本执行失败:', error);
  process.exit(1);
});
