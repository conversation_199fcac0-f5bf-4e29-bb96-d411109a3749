import { Transaction, Op } from 'sequelize';
import { sequelize } from '../config/db';
import { TaskConfig, TaskType } from '../models/TaskConfig';
import { UserTaskStatus, TaskStatusType } from '../models/UserTaskStatus';
import { UserWallet } from '../models/UserWallet';
import { FarmPlot } from '../models/FarmPlot';
import { DeliveryLine } from '../models/DeliveryLine';
import { Chest } from '../models/Chest';
import { processOpenChest } from './chestService';

export interface TaskListResponse {
  tasks: any[];
  total: number;
  mainTask?: any; // 主界面显示的单个任务
}

export interface ClaimResult {
  success: boolean;
  message: string;
  rewards: { [key: string]: number };
  chestRewards?: any[]; // 宝箱开启后的奖励详情
  updatedBalance: {
    gems: number;
    diamonds: number; // 新增钻石字段
  };
}

export class NewTaskService {
  /**
   * 初始化用户任务状态
   * @param walletId 用户钱包ID
   */
  public async initializeUserTasks(walletId: number): Promise<void> {
    const transaction = await sequelize.transaction();
    
    try {
      // 获取所有激活的任务配置
      const taskConfigs = await TaskConfig.findAll({ 
        where: { isActive: true },
        transaction
      });
      
      if (taskConfigs.length === 0) {
        await transaction.commit();
        return;
      }
      
      const userTaskStatuses = [];
      
      for (const config of taskConfigs) {
        // 检查是否已存在任务状态
        const existingStatus = await UserTaskStatus.findOne({
          where: { walletId, taskId: config.id },
          transaction
        });
        
        if (!existingStatus) {
          userTaskStatuses.push({
            walletId,
            taskId: config.id,
            status: 'not_accepted' as TaskStatusType,
            currentProgress: 0,
            targetProgress: config.getTargetProgress()
          });
        }
      }
      
      // 批量创建任务状态
      if (userTaskStatuses.length > 0) {
        await UserTaskStatus.bulkCreate(userTaskStatuses, { transaction });
      }
      
      await transaction.commit();
      
      // 自动接取可接取的任务
      await this.autoAcceptTasks(walletId);
      
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }

  /**
   * 获取用户任务列表
   * @param walletId 用户钱包ID
   * @param forMainUI 是否为主界面请求（只返回优先级最高的单个任务）
   * @param showAll 是否显示所有任务（包括未接取的）
   */
  public async getUserTasks(walletId: number, forMainUI: boolean = false, showAll: boolean = false): Promise<TaskListResponse> {
    // 确保用户任务已初始化
    await this.initializeUserTasks(walletId);
    
    // 更新所有任务进度
    await this.updateAllTaskProgress(walletId);
    
    // 构建查询条件
    const whereCondition: any = { walletId };

    if (!showAll) {
      // 默认只显示已接取和已完成的任务
      whereCondition.status = { [Op.in]: ['accepted', 'completed'] };
    }
    // 如果showAll为true，则显示所有状态的任务

    // 获取用户任务状态
    const userTasks = await UserTaskStatus.findAll({
      where: whereCondition,
      include: [{
        model: TaskConfig,
        as: 'TaskConfig',
        where: { isActive: true }
      }],
      order: [
        // 按状态排序：已完成 > 进行中
        [sequelize.literal(`CASE
          WHEN status = 'completed' THEN 1
          WHEN status = 'accepted' THEN 2
          ELSE 3
        END`), 'ASC'],
        // 同状态按ID排序
        ['taskId', 'ASC']
      ]
    });
    
    const tasks = userTasks.map(userTask => ({
      ...userTask.toTaskJSON(),
      taskConfig: {
        id: userTask.TaskConfig!.id,
        describe: userTask.TaskConfig!.describe,
        type: userTask.TaskConfig!.type,
        typeDescription: userTask.TaskConfig!.getTypeDescription(),
        parameterDescription: userTask.TaskConfig!.getParameterDescription(),
        rewards: userTask.TaskConfig!.getRewards(),
        hasRewards: userTask.TaskConfig!.hasRewards()
      }
    }));
    
    if (forMainUI) {
      // 主界面只返回优先级最高的单个任务
      const mainTask = tasks.length > 0 ? tasks[0] : null;
      return {
        tasks: [],
        total: 0,
        mainTask
      };
    }
    
    return {
      tasks,
      total: tasks.length
    };
  }

  /**
   * 领取任务奖励
   * @param walletId 用户钱包ID
   * @param taskId 任务ID
   */
  public async claimTaskReward(walletId: number, taskId: number): Promise<ClaimResult> {
    const transaction = await sequelize.transaction();
    
    try {
      // 获取用户任务状态
      const userTask = await UserTaskStatus.findOne({
        where: { walletId, taskId },
        include: [{
          model: TaskConfig,
          as: 'TaskConfig'
        }],
        transaction,
        lock: transaction.LOCK.UPDATE
      });
      
      if (!userTask) {
        throw new Error('任务不存在');
      }
      
      if (!userTask.canClaimReward()) {
        throw new Error('任务不可领取奖励');
      }
      
      const config = userTask.TaskConfig!;
      const rewards: { [key: string]: number } = {};
      let chestRewards: any[] | undefined;
      
      // 获取用户钱包（加锁）
      const userWallet = await UserWallet.findByPk(walletId, {
        transaction,
        lock: transaction.LOCK.UPDATE
      });
      
      if (!userWallet) {
        throw new Error('用户不存在');
      }
      
      // 发放钻石奖励（新的货币类型）
      if (config.diamond > 0) {
        const currentDiamonds = Number(userWallet.diamond) || 0;
        userWallet.diamond = currentDiamonds + config.diamond;
        rewards.diamond = config.diamond;
      }
      
      // 发放金币奖励（对应gem字段）
      if (config.coin > 0) {
        const currentGems = Number(userWallet.gem) || 0;
        userWallet.gem = currentGems + config.coin;
        rewards.coin = config.coin;
      }
      
      // 发放宝箱奖励（自动开启）
      if (config.box > 0) {
        chestRewards = await this.grantAndOpenChests(userWallet.userId!, walletId, config.box, transaction);
        rewards.box = config.box;
      }
      
      // 发放道具奖励（暂时记录，具体实现根据道具系统）
      if (config.item > 0) {
        await this.grantItem(walletId, config.item, 1, transaction);
        rewards.item = config.item;
      }
      
      // 保存用户钱包
      await userWallet.save({ transaction });
      
      // 更新任务状态为已领取
      userTask.claimReward();
      await userTask.save({ transaction });
      
      // 自动接取新的可接取任务
      await this.autoAcceptTasks(walletId, transaction);
      
      await transaction.commit();
      
      const result: ClaimResult = {
        success: true,
        message: '奖励领取成功',
        rewards,
        updatedBalance: {
          gems: Number(userWallet.gem) || 0,
          diamonds: Number(userWallet.diamond) || 0
        }
      };

      // 如果有宝箱奖励，添加到结果中
      if (chestRewards) {
        result.chestRewards = chestRewards;
      }

      return result;
      
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }

  /**
   * 更新所有任务进度
   * @param walletId 用户钱包ID
   * @param transaction 可选的事务
   */
  public async updateAllTaskProgress(walletId: number, transaction?: Transaction): Promise<void> {
    const tx = transaction || await sequelize.transaction();
    
    try {
      // 获取所有已接取的任务
      const acceptedTasks = await UserTaskStatus.findAll({
        where: { walletId, status: 'accepted' },
        include: [{
          model: TaskConfig,
          as: 'TaskConfig'
        }],
        transaction: tx
      });
      
      for (const userTask of acceptedTasks) {
        const config = userTask.TaskConfig!;
        let newProgress = 0;
        
        // 根据任务类型计算进度
        switch (config.type) {
          case TaskType.UNLOCK_AREA:
            newProgress = await this.getUnlockProgress(walletId, config.price1, tx);
            break;
          case TaskType.UPGRADE_FARM:
            newProgress = await this.getFarmPlotUpgradeProgress(walletId, config.price1, config.price2, tx);
            break;
          case TaskType.UPGRADE_DELIVERY:
            newProgress = await this.getDeliveryLineUpgradeProgress(walletId, config.price2, tx);
            break;
          case TaskType.INVITE_FRIENDS:
            newProgress = await this.getReferralProgress(walletId, config.price2, tx);
            break;
        }
        
        // 更新进度
        const hasChanged = userTask.updateProgress(newProgress);
        if (hasChanged) {
          await userTask.save({ transaction: tx });
        }
      }
      
      // 自动接取新的可接取任务
      await this.autoAcceptTasks(walletId, tx);
      
      if (!transaction) {
        await tx.commit();
      }
      
    } catch (error) {
      if (!transaction) {
        await tx.rollback();
      }
      throw error;
    }
  }

  /**
   * 自动接取可接取的任务
   * @param walletId 用户钱包ID
   * @param transaction 可选的事务
   */
  private async autoAcceptTasks(walletId: number, transaction?: Transaction): Promise<void> {
    const tx = transaction || await sequelize.transaction();
    
    try {
      // 获取所有未接取的任务
      const unacceptedTasks = await UserTaskStatus.findAll({
        where: { walletId, status: 'not_accepted' },
        include: [{
          model: TaskConfig,
          as: 'TaskConfig'
        }],
        transaction: tx
      });
      
      for (const userTask of unacceptedTasks) {
        const config = userTask.TaskConfig!;
        
        // 检查前置条件
        if (config.condition === 0) {
          // 无前置条件，直接接取
          userTask.acceptTask();
          await userTask.save({ transaction: tx });
        } else {
          // 检查前置任务是否已完成
          const prerequisiteTask = await UserTaskStatus.findOne({
            where: {
              walletId,
              taskId: config.condition,
              status: { [Op.in]: ['completed', 'claimed'] }
            },
            transaction: tx
          });
          
          if (prerequisiteTask) {
            userTask.acceptTask();
            await userTask.save({ transaction: tx });
          }
        }
      }
      
      if (!transaction) {
        await tx.commit();
      }
      
    } catch (error) {
      if (!transaction) {
        await tx.rollback();
      }
      throw error;
    }
  }

  /**
   * 获取解锁区域进度
   */
  private async getUnlockProgress(walletId: number, targetAreaId: number, transaction?: Transaction): Promise<number> {
    const farmPlot = await FarmPlot.findOne({
      where: {
        walletId,
        plotNumber: targetAreaId,
        isUnlocked: true
      },
      transaction
    });

    return farmPlot ? 1 : 0;
  }

  /**
   * 获取牧场升级进度
   */
  private async getFarmPlotUpgradeProgress(
    walletId: number,
    areaId: number,
    targetLevel: number,
    transaction?: Transaction
  ): Promise<number> {
    const farmPlot = await FarmPlot.findOne({
      where: {
        walletId,
        plotNumber: areaId
      },
      transaction
    });

    if (!farmPlot) return 0;

    return Math.min(farmPlot.level, targetLevel);
  }

  /**
   * 获取流水线升级进度
   */
  private async getDeliveryLineUpgradeProgress(
    walletId: number,
    targetLevel: number,
    transaction?: Transaction
  ): Promise<number> {
    const deliveryLine = await DeliveryLine.findOne({
      where: { walletId },
      transaction
    });

    if (!deliveryLine) return 0;

    return Math.min(deliveryLine.level, targetLevel);
  }

  /**
   * 获取邀请好友进度
   */
  private async getReferralProgress(
    walletId: number,
    targetCount: number,
    transaction?: Transaction
  ): Promise<number> {
    const referralCount = await UserWallet.count({
      where: {
        referrerWalletId: walletId
      },
      transaction
    });

    return Math.min(referralCount, targetCount);
  }

  /**
   * 发放宝箱奖励
   */
  private async grantChests(walletId: number, count: number, transaction: Transaction): Promise<void> {
    const chests = [];
    for (let i = 0; i < count; i++) {
      chests.push({
        userId: 0, // 暂时设为0，根据实际需要调整
        walletId,
        isOpened: false,
        type: 'task'
      });
    }

    await Chest.bulkCreate(chests, { transaction });
  }

  /**
   * 发放并自动开启宝箱奖励
   */
  private async grantAndOpenChests(userId: number, walletId: number, count: number, transaction: Transaction): Promise<any[]> {
    const chestRewards = [];

    for (let i = 0; i < count; i++) {
      // 创建宝箱
      const chest = await Chest.create({
        userId,
        walletId,
        isOpened: false,
        type: 'task'
      }, { transaction });

      // 自动开启宝箱
      const openResult = await processOpenChest({
        userId,
        walletId,
        chestId: chest.id,
        transaction
      });

      chestRewards.push(openResult);
    }

    return chestRewards;
  }

  /**
   * 发放道具奖励
   */
  private async grantItem(walletId: number, itemId: number, quantity: number, _transaction: Transaction): Promise<void> {
    // 这里需要根据实际的道具系统实现
    // 暂时只记录日志
    console.log(`为用户 ${walletId} 发放道具 ${itemId} x${quantity}`);
  }

  /**
   * 初始化所有用户的任务状态（用于配置更新后）
   */
  public async initializeAllUserTasks(): Promise<void> {
    try {
      // 获取所有用户钱包ID
      const userWallets = await UserWallet.findAll({
        attributes: ['id'],
        raw: true
      });

      console.log(`开始为 ${userWallets.length} 个用户初始化任务状态...`);

      // 批量处理，避免一次性处理太多用户
      const batchSize = 100;
      for (let i = 0; i < userWallets.length; i += batchSize) {
        const batch = userWallets.slice(i, i + batchSize);

        await Promise.all(
          batch.map(wallet =>
            this.initializeUserTasks(wallet.id).catch(error => {
              console.error(`初始化用户 ${wallet.id} 任务失败:`, error);
            })
          )
        );

        console.log(`已处理 ${Math.min(i + batchSize, userWallets.length)} / ${userWallets.length} 个用户`);
      }

      console.log('所有用户任务状态初始化完成');

    } catch (error) {
      console.error('批量初始化用户任务失败:', error);
      throw error;
    }
  }
}
