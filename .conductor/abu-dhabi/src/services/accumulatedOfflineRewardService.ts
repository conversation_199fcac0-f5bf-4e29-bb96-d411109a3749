// src/services/accumulatedOfflineRewardService.ts

import { UserWallet } from '../models/UserWallet';
import { FarmPlot } from '../models/FarmPlot';
import { sequelize } from '../config/db';
import { Transaction } from 'sequelize';
import { createBigNumber, formatToThreeDecimalsNumber } from '../utils/bigNumberConfig';

/**
 * 累积离线奖励服务
 * 实现离线奖励累积机制，如果用户不领取奖励，奖励会持续累积
 */
export class AccumulatedOfflineRewardService {

  /**
   * 计算并更新累积的离线奖励
   * 新机制：完全基于 lastActiveTime 判定离线状态，不依赖 lastOfflineRewardCalculation
   * @param walletId 钱包ID
   * @param transaction 数据库事务
   * @returns 累积的奖励信息
   */
  static async calculateAndUpdateAccumulatedRewards(
    walletId: number,
    transaction?: Transaction
  ): Promise<{
    totalAccumulatedGems: number;
    newlyAccumulatedGems: number;
    isOffline: boolean;
    offlineTimeInSeconds: number;
    currentTime: Date;
  }> {
    const useTransaction = transaction || await sequelize.transaction();
    const shouldCommit = !transaction;

    try {
      // 获取用户钱包信息
      const wallet = await UserWallet.findOne({
        where: { id: walletId },
        transaction: useTransaction
      });

      if (!wallet) {
        throw new Error('用户钱包不存在');
      }

      const now = new Date();

      // 如果用户从未活跃过，不计算离线奖励
      if (!wallet.lastActiveTime) {
        if (shouldCommit) await useTransaction.commit();
        return {
          totalAccumulatedGems: formatToThreeDecimalsNumber(wallet.accumulatedOfflineGems || 0),
          newlyAccumulatedGems: 0,
          isOffline: false,
          offlineTimeInSeconds: 0,
          currentTime: now
        };
      }

      // 计算离线时间（基于 lastActiveTime）
      const lastActiveDate = wallet.lastActiveTime instanceof Date ? wallet.lastActiveTime : new Date(wallet.lastActiveTime);
      const offlineTimeInSeconds = Math.floor((now.getTime() - lastActiveDate.getTime()) / 1000);

      // 判断是否离线（超过2分钟）
      const isOffline = offlineTimeInSeconds >= 120;

      // 如果用户在线，不累积新的离线奖励
      if (!isOffline) {
        if (shouldCommit) await useTransaction.commit();
        return {
          totalAccumulatedGems: formatToThreeDecimalsNumber(wallet.accumulatedOfflineGems || 0),
          newlyAccumulatedGems: 0,
          isOffline: false,
          offlineTimeInSeconds,
          currentTime: now
        };
      }

      // 用户确实离线，计算离线奖励
      // 计算从 lastActiveTime 到现在的离线奖励
      const newOfflineReward = await this.calculateOfflineRewardForPeriod(
        walletId,
        offlineTimeInSeconds,
        useTransaction
      );

      // 更新累积奖励（累加到现有奖励中）
      const currentAccumulated = createBigNumber(wallet.accumulatedOfflineGems || 0);
      const newAccumulated = currentAccumulated.plus(newOfflineReward);

      // 只更新累积奖励，不更新 lastActiveTime（保持用户的真实离线状态）
      wallet.accumulatedOfflineGems = formatToThreeDecimalsNumber(newAccumulated);

      await wallet.save({ transaction: useTransaction });

      if (shouldCommit) await useTransaction.commit();

      return {
        totalAccumulatedGems: formatToThreeDecimalsNumber(newAccumulated),
        newlyAccumulatedGems: formatToThreeDecimalsNumber(newOfflineReward),
        isOffline: true,
        offlineTimeInSeconds,
        currentTime: now
      };

    } catch (error) {
      if (shouldCommit) {
        try {
          await useTransaction.rollback();
        } catch (rollbackError) {
          // 事务可能已经提交，忽略回滚错误
        }
      }
      throw error;
    }
  }

  /**
   * 计算指定时间段的离线奖励
   * 使用基于farm_configs表的offline字段计算
   * @param walletId 钱包ID
   * @param timeInSeconds 时间段（秒）
   * @param transaction 数据库事务
   * @returns 奖励数量
   */
  private static async calculateOfflineRewardForPeriod(
    walletId: number,
    timeInSeconds: number,
    transaction: Transaction
  ): Promise<number> {
    try {
      // 限制最大离线时间为24小时
      const maxOfflineTimeSeconds = 24 * 60 * 60; // 24小时
      const effectiveOfflineTimeSeconds = Math.min(timeInSeconds, maxOfflineTimeSeconds);

      // 获取用户所有已解锁的农场区域
      const farmPlots = await FarmPlot.findAll({
        where: {
          walletId,
          isUnlocked: true
        },
        order: [['plotNumber', 'ASC']],
        transaction
      });

      if (farmPlots.length === 0) {
        return 0;
      }

      // 获取激活的农场配置
      const { FarmConfig } = require('../models/FarmConfig');
      const activeConfigs = await FarmConfig.getActiveConfigs();

      if (activeConfigs.length === 0) {
        console.warn('没有找到激活的农场配置，使用降级方案');
        return 0;
      }

      // 创建配置映射 grade -> config
      const configMap = new Map<number, any>();
      activeConfigs.forEach((config: any) => {
        configMap.set(config.grade, config);
      });

      let totalOfflineReward = createBigNumber(0);

      // 遍历每个已解锁的农场区域
      for (const farmPlot of farmPlots) {
        const config = configMap.get(farmPlot.level);

        if (!config) {
          console.warn(`农场区域 ${farmPlot.plotNumber} 等级 ${farmPlot.level} 没有找到对应配置`);
          continue;
        }

        // 计算该农场区域的离线收益
        // 离线收益 = offline字段值（每秒收益率） × 离线时间（秒）
        const plotOfflineReward = createBigNumber(config.offline).multipliedBy(effectiveOfflineTimeSeconds);
        totalOfflineReward = totalOfflineReward.plus(plotOfflineReward);
      }

      return formatToThreeDecimalsNumber(totalOfflineReward);

    } catch (error) {
      console.error('计算离线奖励失败:', error);
      return 0;
    }
  }

  /**
   * 领取累积的离线奖励
   * @param walletId 钱包ID
   * @param transaction 数据库事务
   * @returns 领取的奖励信息
   */
  static async claimAccumulatedRewards(
    walletId: number,
    transaction?: Transaction
  ): Promise<{
    claimedGems: number;
    remainingGems: number;
  }> {
    const useTransaction = transaction || await sequelize.transaction();
    const shouldCommit = !transaction;

    try {
      // 先更新累积奖励
      await this.calculateAndUpdateAccumulatedRewards(walletId, useTransaction);

      // 获取最新的钱包信息
      const wallet = await UserWallet.findOne({
        where: { id: walletId },
        transaction: useTransaction
      });

      if (!wallet) {
        throw new Error('用户钱包不存在');
      }

      const accumulatedGems = formatToThreeDecimalsNumber(wallet.accumulatedOfflineGems || 0);
      
      if (accumulatedGems <= 0) {
        if (shouldCommit) await useTransaction.commit();
        return {
          claimedGems: 0,
          remainingGems: 0
        };
      }

      // 将累积奖励添加到用户的GEM余额
      const currentGemBN = createBigNumber(wallet.gem || 0);
      const accumulatedGemBN = createBigNumber(accumulatedGems);
      const newGemBN = currentGemBN.plus(accumulatedGemBN);
      
      // 领取奖励后，用户变为活跃状态
      const now = new Date();
      wallet.gem = newGemBN.toFixed(3);
      wallet.accumulatedOfflineGems = 0; // 清空累积奖励
      wallet.lastActiveTime = now; // 更新活跃时间，用户重新变为在线状态

      await wallet.save({ transaction: useTransaction });

      if (shouldCommit) await useTransaction.commit();

      return {
        claimedGems: accumulatedGems,
        remainingGems: 0
      };

    } catch (error) {
      if (shouldCommit) {
        try {
          await useTransaction.rollback();
        } catch (rollbackError) {
          // 事务可能已经提交，忽略回滚错误
        }
      }
      throw error;
    }
  }

  /**
   * 获取累积奖励信息（不领取）
   * @param walletId 钱包ID
   * @returns 累积奖励信息
   */
  static async getAccumulatedRewardInfo(walletId: number): Promise<{
    totalAccumulatedGems: number;
    isOffline: boolean;
    offlineTime: number;
    lastActiveTime: Date | null;
    lastCalculationTime: Date | null;
    farmProductionPerSecond: number;
    deliveryProcessingPerSecond: number;
    milkProduced: number;
    milkProcessed: number;
    hasVip: boolean;
    hasSpeedBoost: boolean;
    speedBoostMultiplier: number;
  }> {
    try {
      // 计算并更新累积奖励（内部会管理自己的事务）
      const rewardInfo = await this.calculateAndUpdateAccumulatedRewards(walletId);

      // 获取用户钱包信息
      const wallet = await UserWallet.findOne({
        where: { id: walletId }
      });

      if (!wallet) {
        throw new Error('用户钱包不存在');
      }

      const lastActiveTime = wallet.lastActiveTime;
      const lastActiveDate = lastActiveTime instanceof Date ? lastActiveTime : (lastActiveTime ? new Date(lastActiveTime) : null);

      // 获取农场和出货线的基本信息用于兼容原有API格式
      const farmProductionInfo = await this.calculateFarmProductionInfo(walletId);
      const deliveryInfo = await this.calculateDeliveryInfo(walletId);

      // 计算基于离线时间的牛奶生产和处理量（用于API兼容性）
      const effectiveOfflineTime = Math.min(rewardInfo.offlineTimeInSeconds, 24 * 60 * 60); // 最大24小时
      const milkProduced = farmProductionInfo.farmProductionPerSecond * effectiveOfflineTime;
      const milkProcessed = Math.min(milkProduced, deliveryInfo.deliveryProcessingPerSecond * effectiveOfflineTime);

      return {
        totalAccumulatedGems: rewardInfo.totalAccumulatedGems,
        isOffline: rewardInfo.isOffline,
        offlineTime: rewardInfo.offlineTimeInSeconds,
        lastActiveTime: lastActiveDate,
        lastCalculationTime: null, // 新机制不再使用此字段
        farmProductionPerSecond: farmProductionInfo.farmProductionPerSecond,
        deliveryProcessingPerSecond: deliveryInfo.deliveryProcessingPerSecond,
        milkProduced: formatToThreeDecimalsNumber(milkProduced),
        milkProcessed: formatToThreeDecimalsNumber(milkProcessed),
        hasVip: farmProductionInfo.hasVip,
        hasSpeedBoost: farmProductionInfo.hasSpeedBoost,
        speedBoostMultiplier: farmProductionInfo.speedBoostMultiplier
      };

    } catch (error) {
      throw error;
    }
  }

  /**
   * 计算农场生产信息（用于API兼容性）
   * @param walletId 钱包ID
   * @returns 农场生产信息
   */
  private static async calculateFarmProductionInfo(walletId: number): Promise<{
    farmProductionPerSecond: number;
    hasVip: boolean;
    hasSpeedBoost: boolean;
    speedBoostMultiplier: number;
  }> {
    try {
      // 获取用户所有已解锁的农场区域
      const farmPlots = await FarmPlot.findAll({
        where: {
          walletId,
          isUnlocked: true
        }
      });

      if (farmPlots.length === 0) {
        return {
          farmProductionPerSecond: 0,
          hasVip: false,
          hasSpeedBoost: false,
          speedBoostMultiplier: 1.0
        };
      }

      // 计算总的每秒牛奶产量
      let totalProductionPerSecond = 0;
      for (const farmPlot of farmPlots) {
        // 使用农场区块的计算方法
        totalProductionPerSecond += farmPlot.calculateTotalProductionPerSecond();
      }

      // 检查VIP和加成状态（简化实现）
      const { UserWallet } = require('../models/UserWallet');
      const wallet = await UserWallet.findOne({ where: { id: walletId } });

      // 这里可以根据实际的VIP和加成逻辑进行调整
      const hasVip = false; // 简化实现
      const hasSpeedBoost = false; // 简化实现
      const speedBoostMultiplier = 1.0;

      return {
        farmProductionPerSecond: formatToThreeDecimalsNumber(totalProductionPerSecond),
        hasVip,
        hasSpeedBoost,
        speedBoostMultiplier
      };

    } catch (error) {
      console.error('计算农场生产信息失败:', error);
      return {
        farmProductionPerSecond: 0,
        hasVip: false,
        hasSpeedBoost: false,
        speedBoostMultiplier: 1.0
      };
    }
  }

  /**
   * 计算出货线信息（用于API兼容性）
   * @param walletId 钱包ID
   * @returns 出货线信息
   */
  private static async calculateDeliveryInfo(walletId: number): Promise<{
    deliveryProcessingPerSecond: number;
  }> {
    try {
      const { DeliveryLine } = require('../models/DeliveryLine');
      const deliveryLine = await DeliveryLine.findOne({ where: { walletId } });

      if (!deliveryLine) {
        return {
          deliveryProcessingPerSecond: 0
        };
      }

      // 计算出货线每秒处理能力
      // 处理能力 = 1 / deliverySpeed (每秒处理的方块数)
      const processingPerSecond = deliveryLine.deliverySpeed > 0 ? (1 / deliveryLine.deliverySpeed) : 0;

      // 每个方块需要的牛奶量（假设为固定值，可根据实际配置调整）
      const milkPerBlock = 100; // 这个值可能需要根据实际游戏配置调整
      const milkProcessingPerSecond = processingPerSecond * milkPerBlock;

      return {
        deliveryProcessingPerSecond: formatToThreeDecimalsNumber(milkProcessingPerSecond)
      };

    } catch (error) {
      console.error('计算出货线信息失败:', error);
      return {
        deliveryProcessingPerSecond: 0
      };
    }
  }
}
