// src/services/chestAccelerationService.ts
import { sequelize } from "../config/db";
import { ChestCountdown, UserChestAcceleration } from "../models";
import { Op, Transaction } from "sequelize";
import dayjs from "dayjs";
import { t } from "../i18n";

// 加速限制配置
const ACCELERATION_CONFIG = {
  MAX_SECONDS_PER_ACCELERATION: 10, // 单次最大加速秒数
  MAX_SECONDS_PER_DAY: 7200, // 每天最大加速秒数 (2小时 = 7200秒)
};

/**
 * 加速宝箱倒计时
 * @param userId 用户ID
 * @param walletId 钱包ID
 * @param seconds 要加速的秒数
 * @param transaction 事务对象
 * @returns 加速结果
 */
export async function accelerateChestCountdown(
  userId: number,
  walletId: number,
  seconds: number,
  transaction?: Transaction
) {
  // 创建本地事务如果没有提供
  const trans = transaction || await sequelize.transaction();
  
  try {
    // 验证加速秒数
    if (seconds <= 0) {
      throw new Error(t('errors.invalidAccelerationSeconds') || "加速秒数必须大于0");
    }
    
    // 限制单次加速秒数
    const accelerationSeconds = Math.min(seconds, ACCELERATION_CONFIG.MAX_SECONDS_PER_ACCELERATION);
    
    // 获取今天的日期（不包含时间）
    const today = dayjs().startOf('day').toDate();
    
    // 查找或创建用户今天的加速记录
    let [accelerationRecord, created] = await UserChestAcceleration.findOrCreate({
      where: {
        userId,
        walletId,
        accelerationDate: today
      },
      defaults: {
        userId,
        walletId,
        accelerationDate: today,
        totalAccelerationSeconds: 0
      },
      transaction: trans
    });
    
    // 检查用户今天的加速限制
    const newTotalSeconds = accelerationRecord.totalAccelerationSeconds + accelerationSeconds;
    if (newTotalSeconds > ACCELERATION_CONFIG.MAX_SECONDS_PER_DAY) {
      throw new Error(t('errors.accelerationLimitExceeded'));
    }
    
    // 查找用户的倒计时记录
    const countdown = await ChestCountdown.findOne({
      where: { userId, walletId },
      transaction: trans
    });
    
    if (!countdown) {
      throw new Error(t('errors.countdownNotFound') || "未找到倒计时记录");
    }
    
    // 验证倒计时是否正在进行（nextAvailableTime大于当前时间）
    const now = new Date();
    if (new Date(countdown.nextAvailableTime) <= now) {
      throw new Error(t('errors.countdownNotActive') || "宝箱倒计时未激活或已结束，无法加速");
    }
    
    // 计算新的倒计时时间
    const currentNextAvailableTime = new Date(countdown.nextAvailableTime);
    const newNextAvailableTime = new Date(currentNextAvailableTime.getTime() - (accelerationSeconds * 1000));
    
    // 更新倒计时记录
    await countdown.update({
      nextAvailableTime: newNextAvailableTime
    }, { transaction: trans });
    
    // 更新用户的加速记录
    await accelerationRecord.update({
      totalAccelerationSeconds: newTotalSeconds
    }, { transaction: trans });
    
    // 如果使用本地事务，提交事务
    if (!transaction) {
      await trans.commit();
    }
    
    // 计算剩余可加速时间（秒）
    const remainingAccelerationSeconds = ACCELERATION_CONFIG.MAX_SECONDS_PER_DAY - newTotalSeconds;
    
    return {
      success: true,
      acceleratedSeconds: accelerationSeconds,
      newNextAvailableTime,
      totalAcceleratedToday: newTotalSeconds,
      remainingAccelerationSeconds,
      message: t('success.chestAccelerated') || "宝箱倒计时已加速"
    };
  } catch (error: any) {
    // 如果使用本地事务，回滚事务
    if (!transaction) {
      await trans.rollback();
    }
    throw error;
  }
}

/**
 * 获取用户今日的加速使用情况
 * @param userId 用户ID
 * @param walletId 钱包ID
 * @returns 加速使用情况
 */
export async function getUserAccelerationStatus(userId: number, walletId: number) {
  // 获取今天的日期（不包含时间）
  const today = dayjs().startOf('day').toDate();
  
  // 查找用户今天的加速记录
  const accelerationRecord = await UserChestAcceleration.findOne({
    where: {
      userId,
      walletId,
      accelerationDate: today
    }
  });
  
  // 计算已使用和剩余的加速时间
  const usedSeconds = accelerationRecord ? accelerationRecord.totalAccelerationSeconds : 0;
  const remainingSeconds = ACCELERATION_CONFIG.MAX_SECONDS_PER_DAY - usedSeconds;
  
  return {
    usedAccelerationSeconds: usedSeconds,
    remainingAccelerationSeconds: remainingSeconds,
    maxAccelerationPerDay: ACCELERATION_CONFIG.MAX_SECONDS_PER_DAY,
    maxAccelerationPerTime: ACCELERATION_CONFIG.MAX_SECONDS_PER_ACCELERATION
  };
}