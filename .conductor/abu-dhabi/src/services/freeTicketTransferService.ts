// src/services/freeTicketTransferService.ts

import { UserWallet } from "../models/UserWallet";
import { FreeTicketTransfer } from "../models/FreeTicketTransfer";
import { Op, Sequelize } from "sequelize";
import { recordWalletHistory } from "./walletHistoryService";
import { t } from "../i18n";
import { sequelize } from "../config/db";
import BigNumber from "bignumber.js";

// 每日免费门票转账限制
const DAILY_TRANSFER_LIMIT = 6;

/**
 * 检查用户今日已转账的免费门票数量
 * @param fromUserId 发送方用户ID
 * @param fromWalletId 发送方钱包ID
 * @returns 今日已转账数量
 */
async function getTodayTransferCount(fromUserId: number, fromWalletId: number): Promise<number> {
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  
  const tomorrow = new Date(today);
  tomorrow.setDate(tomorrow.getDate() + 1);
  
  const count = await FreeTicketTransfer.sum('amount', {
    where: {
      fromUserId,
      fromWalletId,
      createdAt: {
        [Op.gte]: today,
        [Op.lt]: tomorrow
      }
    }
  });
  
  return count || 0;
}

/**
 * 转账免费门票
 * @param fromUserId 发送方用户ID
 * @param fromWalletId 发送方钱包ID
 * @param toWalletAddress 接收方钱包地址
 * @param amount 转账数量
 * @returns 转账结果
 */
export async function transferFreeTicket(
  fromUserId: number,
  fromWalletId: number,
  toWalletAddress: string,
  amount: number
) {
  // 验证参数
  if (!toWalletAddress) {
    throw new Error(t("errors.receiverWalletAddressEmpty"));
  }

  if (!amount || amount <= 0 || !Number.isInteger(amount)) {
    throw new Error(t("errors.freeTicketAmountMustBePositiveInteger"));
  }

  // 检查每日转账限制
  const todayTransferCount = await getTodayTransferCount(fromUserId, fromWalletId);
  if (todayTransferCount > DAILY_TRANSFER_LIMIT) {
    throw new Error(t("errors.freeTicketDailyLimitReached", { dailyLimit: DAILY_TRANSFER_LIMIT, remaining: DAILY_TRANSFER_LIMIT - todayTransferCount }));
  }

  // 开始事务
  const transaction = await sequelize.transaction();

  try {
    // 1. 查找发送方钱包
    const fromWallet = await UserWallet.findByPk(fromWalletId, { transaction });
    if (!fromWallet) {
      throw new Error(t("errors.senderWalletNotExist"));
    }

    // 2. 检查免费门票余额是否足够
    if ((fromWallet.free_ticket || 0) < amount) {
      throw new Error(t("errors.insufficientFreeTickets"));
    }

    // 3. 查找接收方钱包
    const toWallet = await UserWallet.findOne({
      where: { walletAddress: toWalletAddress },
      transaction
    });

    if (!toWallet) {
      throw new Error(t("errors.receiverWalletNotExist"));
    }

    // 4. 不能转给自己
    if (fromWallet.id === toWallet.id) {
      throw new Error(t("errors.cannotTransferToSelf"));
    }

    // 5. 执行转账 - 使用原子操作
    // 扣除发送方免费门票 (使用原子操作)
    const [affectedRows] = await UserWallet.decrement('free_ticket', {
      by: amount,
      where: {
        id: fromWalletId,
        free_ticket: { [Op.gte]: amount } // 确保余额充足
      },
      transaction
    });

    console.log('affectedRows',affectedRows);
    

    // 检查是否成功扣款 - 使用decrement返回的受影响行数
    if (!affectedRows || affectedRows.length === 0) {
      throw new Error(t("errors.transferFailedInsufficientFreeTickets"));
    }

    // 增加接收方免费门票 (使用原子操作)
    await UserWallet.increment('free_ticket', {
      by: amount,
      where: { id: toWallet.id },
      transaction
    });

    // 6. 记录转账历史
    await FreeTicketTransfer.create({
      fromUserId,
      fromWalletId,
      toUserId: toWallet.userId,
      toWalletId: toWallet.id,
      amount
    }, { transaction });

    // 7. 记录钱包历史 - 发送方
    await recordWalletHistory(
      fromUserId,
      fromWalletId,
      "free_ticket",
      amount,
      "Transfer Free Ticket",
      "out",
      "free_ticket",
      "free_ticket",
      t("walletHistory.transferFreeTicketOut", { amount, address: toWalletAddress }),
      t("walletHistory.transferFreeTicketOutDev", { address: toWalletAddress })
    );

    // 8. 记录钱包历史 - 接收方
    await recordWalletHistory(
      toWallet.userId,
      toWallet.id,
      "free_ticket",
      amount,
      "Receive Free Ticket",
      "in",
      "free_ticket",
      "free_ticket",
      t("walletHistory.transferFreeTicketIn", { amount, address: fromWallet.walletAddress || fromWalletId }),
      t("walletHistory.transferFreeTicketInDev", { address: fromWallet.walletAddress || fromWalletId })
    );

    // 提交事务
    await transaction.commit();

    return {
      success: true,
      message: t("success.transferFreeTicketSuccess"),
      data: {
        fromWalletId,
        toWalletAddress,
        amount,
        timestamp: new Date(),
      },
    };
  } catch (error) {
    // 回滚事务
    await transaction.rollback();
    throw error;
  }
}

/**
 * 获取用户今日剩余可转账的免费门票数量
 * @param userId 用户ID
 * @param walletId 钱包ID
 * @returns 剩余可转账数量
 */
export async function getRemainingDailyTransferLimit(userId: number, walletId: number): Promise<number> {
  const todayTransferCount = await getTodayTransferCount(userId, walletId);
  return Math.max(0, DAILY_TRANSFER_LIMIT - todayTransferCount);
}

/**
 * 批量转账免费门票到多个钱包地址
 * @param fromUserId 发送方用户ID
 * @param fromWalletId 发送方钱包ID
 * @param transfers 转账信息数组，每个元素包含接收方钱包地址和转账数量
 * @returns 批量转账结果
 */
export async function batchTransferFreeTickets(
  fromUserId: number,
  fromWalletId: number,
  transfers: Array<{ toWalletAddress: string; amount: number }>
) {
  // 验证参数
  if (!transfers || !Array.isArray(transfers) || transfers.length === 0) {
    throw new Error(t("errors.transferFreeTicketFailed"));
  }

  // 计算总转账数量
  let totalAmount = 0;
  for (const transfer of transfers) {
    if (!transfer.toWalletAddress) {
      throw new Error(t("errors.receiverWalletAddressEmpty"));
    }

    if (!transfer.amount || transfer.amount <= 0 || !Number.isInteger(transfer.amount)) {
      throw new Error(t("errors.freeTicketAmountMustBePositiveInteger"));
    }

    totalAmount = new BigNumber(totalAmount).plus(transfer.amount).toNumber();
  }

  // 检查每日转账限制
  const todayTransferCount = await getTodayTransferCount(fromUserId, fromWalletId);
  if (todayTransferCount + totalAmount > DAILY_TRANSFER_LIMIT) {
    throw new Error(t("errors.freeTicketDailyLimitReached", { dailyLimit: DAILY_TRANSFER_LIMIT, remaining: DAILY_TRANSFER_LIMIT - todayTransferCount }));
  }

  // 开始事务
  const transaction = await sequelize.transaction();

  try {
    // 1. 查找发送方钱包
    const fromWallet = await UserWallet.findByPk(fromWalletId, { transaction });
    if (!fromWallet) {
      throw new Error(t("errors.senderWalletNotExist"));
    }

    // 2. 检查免费门票余额是否足够
    if ((fromWallet.free_ticket || 0) < totalAmount) {
      throw new Error(t("errors.insufficientFreeTickets"));
    }

    // 3. 查找所有接收方钱包并验证
    const receiverWallets = new Map();
    for (const transfer of transfers) {
      // 如果已经查询过该钱包地址，跳过
      if (receiverWallets.has(transfer.toWalletAddress)) {
        continue;
      }

      const toWallet = await UserWallet.findOne({
        where: { walletAddress: transfer.toWalletAddress },
        transaction
      });

      if (!toWallet) {
        throw new Error(t("errors.receiverWalletNotExist"));
      }

      // 4. 不能转给自己
      if (fromWallet.id === toWallet.id) {
        throw new Error(t("errors.cannotTransferToSelf"));
      }

      receiverWallets.set(transfer.toWalletAddress, toWallet);
    }

    // 5. 执行转账 - 使用原子操作
    // 扣除发送方免费门票 (使用原子操作)
    const [affectedRows] = await UserWallet.decrement('free_ticket', {
      by: totalAmount,
      where: {
        id: fromWalletId,
        free_ticket: { [Op.gte]: totalAmount } // 确保余额充足
      },
      transaction
    });

    // 检查是否成功扣款 - 使用decrement返回的受影响行数
    if (!affectedRows || affectedRows.length === 0) {
      throw new Error(t("errors.transferFailedInsufficientFreeTickets"));
    }

    // 6. 为每个接收方增加免费门票并记录转账历史
    const results = [];
    for (const transfer of transfers) {
      const toWallet = receiverWallets.get(transfer.toWalletAddress);
      
      // 增加接收方免费门票 (使用原子操作)
      await UserWallet.increment('free_ticket', {
        by: transfer.amount,
        where: { id: toWallet.id },
        transaction
      });

      // 记录转账历史
      await FreeTicketTransfer.create({
        fromUserId,
        fromWalletId,
        toUserId: toWallet.userId,
        toWalletId: toWallet.id,
        amount: transfer.amount
      }, { transaction });

      // 记录钱包历史 - 发送方
      await recordWalletHistory(
        fromUserId,
        fromWalletId,
        "free_ticket",
        transfer.amount,
        "Transfer Free Ticket",
        "out",
        "free_ticket",
        "free_ticket",
        t("walletHistory.transferFreeTicketOut", { amount: transfer.amount, address: transfer.toWalletAddress }),
        t("walletHistory.transferFreeTicketOutDev", { address: transfer.toWalletAddress })
      );

      // 记录钱包历史 - 接收方
      await recordWalletHistory(
        toWallet.userId,
        toWallet.id,
        "free_ticket",
        transfer.amount,
        "Receive Free Ticket",
        "in",
        "free_ticket",
        "free_ticket",
        t("walletHistory.transferFreeTicketIn", { amount: transfer.amount, address: fromWallet.walletAddress || fromWalletId }),
        t("walletHistory.transferFreeTicketInDev", { address: fromWallet.walletAddress || fromWalletId })
      );

      results.push({
        toWalletAddress: transfer.toWalletAddress,
        amount: transfer.amount,
        success: true
      });
    }

    // 提交事务
    await transaction.commit();

    return {
      success: true,
      message: t("success.transferFreeTicketSuccess"),
      data: {
        fromWalletId,
        transfers: results,
        totalAmount,
        timestamp: new Date(),
      },
    };
  } catch (error) {
    // 回滚事务
    await transaction.rollback();
    throw error;
  }
}