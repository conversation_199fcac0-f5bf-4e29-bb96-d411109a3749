// src/services/taskService.ts
import { Tasks } from "../models/Tasks";
import { UserTaskComplete } from "../models/UserTaskComplete";
import { Chest } from "../models/Chest";
import { User } from "../models/User";
import { Op, Transaction } from "sequelize";
import { t } from "../i18n";
import { sequelize } from "../config/db";
import { processOpenChest } from "./chestService";

import { convertToChinaTime } from "../utils/date";
/**
 * 用户完成某个任务 -> 给宝箱奖励
 * @param userId 当前登录用户ID
 * @param taskId 任务ID
 */
export async function completeTask(
  userId: number,
  taskId: number,
  walletId: number
) {
  // 使用事务确保操作的原子性
  const transaction = await sequelize.transaction();
  
  try {
    // 1) 找到任务
    const task = await Tasks.findByPk(taskId, { transaction });
    if (!task) {
      throw new Error(t("errors.taskNotFound"));
    }

    // 2) 查找用户最近一次完成此任务
    const lastRecord = await UserTaskComplete.findOne({
      where: { userId, taskId },
      order: [["completeTime", "DESC"]],
      transaction
    });

    // 3) 根据 task.repeatInterval 判断能否再完成

    //   - once: 只能完成一次
    if (task.repeatInterval === "once") {
      if (lastRecord) {
        throw new Error(t("errors.taskOnlyOnce"));
      }
    }

    //   - day: 每日可完成一次 => 比较日期
    if (task.repeatInterval === "day") {
      if (lastRecord) {
        const lastDate = convertToChinaTime(
          lastRecord.completeTime,
          "YYYY-MM-DD"
        );
        const today = convertToChinaTime(new Date().getTime(), "YYYY-MM-DD");

        console.log("lastDate", lastDate, "today", today);

        if (lastDate === today) {
          throw new Error(t("errors.taskAlreadyCompleted"));
        }
      }
    }

    //  你也可以扩展 "week", "month" 等

    // 4) 如果判定可以完成 -> 创建宝箱(奖励)
    // 对于taskId为2或3的任务，创建两个宝箱
    let chestReward;
    
    if (taskId === 2 || taskId === 3) {
      // 创建第一个宝箱
      const chest1 = await Chest.create({
        userId,
        walletId,
        isOpened: false,
        type: 'task'  // 添加类型：任务宝箱
      }, { transaction });
      
      // 创建第二个宝箱
      const chest2 = await Chest.create({
        userId,
        walletId,
        isOpened: false,
        type: 'task'  // 添加类型：任务宝箱
      }, { transaction });
      
      // 更新用户状态（如果是任务2）
      if (taskId === 2) {
        await User.update(
          {
            hasFollowedChannel: true,
          },
          {
            where: {
              id: userId,
            },
            transaction
          }
        );
      }
      
      // 5) 在 UserTaskComplete 表插入一条记录
      const record = await UserTaskComplete.create({
        userId,
        taskId,
        walletId,
        completeTime: new Date(convertToChinaTime(new Date().getTime())),
      }, { transaction });
      
      // 6) 直接打开两个宝箱
      // 打开第一个宝箱
      const chestReward1 = await processOpenChest({
        userId,
        walletId,
        chestId: chest1.id,
        chestType: 'task',
        transaction
      });
      
      // 打开第二个宝箱
      const chestReward2 = await processOpenChest({
        userId,
        walletId,
        chestId: chest2.id,
        chestType: 'task',
        transaction
      });
      
      // 合并两个宝箱的奖励信息
      chestReward = {
        openedCount: chestReward1.openedCount + chestReward2.openedCount,
        chestIds: [...chestReward1.chestIds, ...chestReward2.chestIds],
        rewards: [...chestReward1.rewards, ...chestReward2.rewards],
        summary: {
          ticket: (chestReward1.summary.ticket || 0) + (chestReward2.summary.ticket || 0),
          fragment_green: (chestReward1.summary.fragment_green || 0) + (chestReward2.summary.fragment_green || 0),
          fragment_blue: (chestReward1.summary.fragment_blue || 0) + (chestReward2.summary.fragment_blue || 0),
          fragment_purple: (chestReward1.summary.fragment_purple || 0) + (chestReward2.summary.fragment_purple || 0),
          fragment_gold: (chestReward1.summary.fragment_gold || 0) + (chestReward2.summary.fragment_gold || 0),
          ton: (chestReward1.summary.ton || 0) + (chestReward2.summary.ton || 0),
          gem: (chestReward1.summary.gem || 0) + (chestReward2.summary.gem || 0)
        },
        levelSummary: {
          level1: (chestReward1.levelSummary.level1 || 0) + (chestReward2.levelSummary.level1 || 0),
          level2: (chestReward1.levelSummary.level2 || 0) + (chestReward2.levelSummary.level2 || 0),
          level3: (chestReward1.levelSummary.level3 || 0) + (chestReward2.levelSummary.level3 || 0),
          level4: (chestReward1.levelSummary.level4 || 0) + (chestReward2.levelSummary.level4 || 0)
        },
        shareLinks: [...(chestReward1.shareLinks || []), ...(chestReward2.shareLinks || [])],
        jackpotWinner: chestReward1.jackpotWinner || chestReward2.jackpotWinner
      };
    } else {
      // 对于其他任务，仍然只创建一个宝箱
      const chest = await Chest.create({
        userId,
        walletId,
        isOpened: false,
        type: 'task'  // 添加类型：任务宝箱
      }, { transaction });
      
      // 5) 在 UserTaskComplete 表插入一条记录
      const record = await UserTaskComplete.create({
        userId,
        taskId,
        walletId,
        completeTime: new Date(convertToChinaTime(new Date().getTime())),
      }, { transaction });
      
      // 6) 直接打开刚创建的宝箱
      chestReward = await processOpenChest({
        userId,
        walletId,
        chestId: chest.id,
        chestType: 'task',
        transaction
      });
    }
    
    // 提交事务
    await transaction.commit();

    return {
      message: t("success.taskCompleted"),
      task,
      chestReward // 返回宝箱奖励信息
    };
  } catch (error) {
    // 发生错误时回滚事务
    await transaction.rollback();
    console.error('完成任务失败:', error);
    throw error;
  }
}
/**
 * 返回一个数组，包含所有任务及其“是否已完成 / 是否可完成”等状态
 */
export async function getTaskListWithStatus(userId: number) {
  // 1) 查出全部任务
  const allTasks = await Tasks.findAll();

  const result = [];

  // 2) 对每个任务，去 user_task_complete 找最近一条完成记录
  for (const task of allTasks) {
    // 找最近的完成记录
    const lastRecord = await UserTaskComplete.findOne({
      where: { userId, taskId: task.id },
      order: [["completeTime", "DESC"]], // 最新的那条
    });

    // 默认值
    let isCompleted = false; // 表示“当前周期”或“一次性”是否已完成
    let canComplete = true; // 表示现在是否可以再做
    let lastCompleteTime = lastRecord ? lastRecord.completeTime : null;

    // 根据 task.repeatInterval 判断
    if (task.repeatInterval === "once") {
      // 只能完成一次
      if (lastRecord) {
        isCompleted = true;
        canComplete = false;
      }
    } else if (task.repeatInterval === "day") {
      // 每日任务 => 如果当天已做就不行
      if (lastRecord) {
        const lastDate = convertToChinaTime(
          lastRecord.completeTime,
          "YYYY-MM-DD"
        );
        const today = convertToChinaTime(new Date().getTime(), "YYYY-MM-DD");
        if (lastDate === today) {
          isCompleted = true;
          canComplete = false;
        }
      }
    }
    // 你也可以扩展"week","month"等逻辑

    result.push({
      id: task.id,
      name: task.name,
      type: task.type,
      repeatInterval: task.repeatInterval,
      isCompleted,
      canComplete,
      lastCompleteTime,
    });
  }

  return result;
}
