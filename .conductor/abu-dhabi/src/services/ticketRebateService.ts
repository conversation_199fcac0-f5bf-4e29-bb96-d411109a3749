import { User, UserWallet, WalletHistory } from "../models";
import { sequelize } from "../config/db";
import { TICKET_REBATE_CONFIG } from "../config/consts";
import { Op } from "sequelize";
import dayjs from "dayjs";
import { GameHistory } from "../models";
import { PendingRebate } from "../models/PendingRebate";
import { t } from "../i18n";
import BigNumber from 'bignumber.js';

/**
 * 检查用户今日游戏轮数是否达到解锁全部返利的条件
 * @param walletId 用户ID
 * @returns 返回用户今日游戏轮数
 */
export async function checkDailyGameRounds(walletId: number): Promise<number> {
  const today = dayjs().startOf('day').toDate();
  const tomorrow = dayjs().endOf('day').toDate();
  
  const gameCount = await GameHistory.count({
    where: {
      walletId,
      createdAt: {
        [Op.between]: [today, tomorrow]
      }
    }
  });
  
  return gameCount;
}

/**
 * 检查用户是否有资格领取返利
 * @param walletId 用户ID
 * @returns 是否有资格领取返利
 */
export async function isEligibleForRebate(walletId: number): Promise<boolean> {
  const gameRounds = await checkDailyGameRounds(walletId);
  return gameRounds >= TICKET_REBATE_CONFIG.MIN_DAILY_ROUNDS;
}

/**
 * 处理门票成功游戏后的返利
 * @param userId 门票成功游戏后的用户ID
 * @param ticketAmount 门票数量
 * @param ticketPrice 每张门票的价格
 */
export async function processTicketRebate(
  walletId: number,
  ticketAmount: number,
  ticketPrice: number
): Promise<void> {
  const transaction = await sequelize.transaction();
  
  try {
    // 计算总门票价值
    const totalTicketValue = ticketAmount * ticketPrice;
    
    // 计算可返利的总金额 (30%)
    const totalRebateAmount = totalTicketValue * TICKET_REBATE_CONFIG.TOTAL_REBATE_PERCENTAGE;
    
    // 获取用户的推荐链
    let currentWalletId = walletId;
    let level = 1;
    
    // 处理5个级别的返利
    while (level <= 5 && currentWalletId) {
      // 查找当前用户的推荐人
      const userWallet = await UserWallet.findByPk(currentWalletId, { transaction });
      if (!userWallet || !userWallet.referrerWalletId) {
        break; // 没有推荐人，结束循环
      }
      
      const referrerId = userWallet.referrerWalletId;
      
      // 查找推荐人的钱包
      const referrerWallet = await UserWallet.findOne({
        where: { id: referrerId },
        transaction
      });
      
      if (referrerWallet) {
        // 计算该级别的返利金额
        const levelPercentage = TICKET_REBATE_CONFIG.LEVEL_PERCENTAGES[level as keyof typeof TICKET_REBATE_CONFIG.LEVEL_PERCENTAGES];
        const levelRebateAmount = totalRebateAmount * levelPercentage;
        
        if (levelRebateAmount > 0) {    
            // 存储为待领取的返利
            await PendingRebate.create({
              userId: referrerWallet.userId,
              walletId: referrerWallet.id,
              amount: levelRebateAmount,
              level: level,
              sourceUserId: walletId
            }, { transaction });
          
        }
      }
      
      // 更新当前用户ID为推荐人ID，继续处理上一级
      currentWalletId = referrerId;
      level++;
    }
    
    await transaction.commit();
  } catch (error) {
    await transaction.rollback();
    console.error("处理门票返利失败:", error);
    throw error;
  }
}

/**
 * 获取wallet用户的待领取返利总额
 * @param walletId wallet用户
 * @returns 待领取返利总额
 */
export async function getPendingRebateAmount(walletId: number): Promise<number> {
  const pendingRebates = await PendingRebate.findAll({
    where: { walletId }
  });
  
  return pendingRebates.reduce((sum, rebate) => new BigNumber(sum).plus(rebate.amount).toNumber(), 0);
}

/**
 * 领取所有待领取的返利
 * @param userId 用户ID
 * @returns 领取的总金额
 */
export async function claimPendingRebates(walletId: number): Promise<number> {
  const transaction = await sequelize.transaction();
  
  try {
    // 检查用户是否有资格领取返利
    const isEligible = await isEligibleForRebate(walletId);
    if (!isEligible) {
      throw new Error(t("errors.notCompletedThreeRounds"));
    }
    
    // 获取所有待领取的返利
    const pendingRebates = await PendingRebate.findAll({
      where: { walletId },
      transaction
    });
    
    if (pendingRebates.length === 0) {
      await transaction.commit();
      return 0;
    }
    
    // 计算总金额
    const totalAmount = pendingRebates.reduce((sum, rebate) => new BigNumber(sum).plus(rebate.amount).toNumber(), 0);
    
    // 获取用户钱包
    const wallet = await UserWallet.findOne({
      where: { id: walletId },
      transaction
    });
    
    if (!wallet) {
      throw new Error(t("errors.userWalletNotFound"));
    }
    
    // 增加用户的USDT余额
    await UserWallet.increment("usd", {
      by: totalAmount,
      where: { id: wallet.id },
      transaction
    });
    
    // 记录钱包历史
    await WalletHistory.create({
      userId:wallet.userId,
      walletId: wallet.id,
      amount: totalAmount,
      currency: "usd",
      reference: "Claimed Ticket Rebate",
      action: "in",
      category: "usd",
      credit_type: "usd",
      fe_display_remark: t("email.rebate.displayRemark"),
      developer_remark: t("email.rebate.developerRemark", { count: pendingRebates.length })
    }, { transaction });
    
    // 创建系统公告
    try {
      const Announcement = sequelize.models.Announcement;
      if (Announcement) {
        await Announcement.create({
          userId: wallet.userId,
          walletId: wallet.id,
          title: t("email.rebate.title"),
          content: t("email.rebate.content", { count: pendingRebates.length, amount: new BigNumber(totalAmount).toFixed(2) }),
          type: "rebate_settlement",
          isRead: false
        }, { transaction });
      }
    } catch (error) {
      console.error("创建返利结算公告失败:", error);
      // 不影响主流程继续执行
    }
    
    // 删除已领取的返利记录
    await PendingRebate.destroy({
      where: { walletId },
      transaction
    });
    
    await transaction.commit();
    return totalAmount;
  } catch (error) {
    await transaction.rollback();
    console.error("领取返利失败:", error);
    throw error;
  }
}

/**
 * 检查并自动领取返利
 * 当用户完成3轮游戏时自动触发
 * @param userId 用户ID
 */
export async function checkAndClaimRebates(userId: number): Promise<void> {
  try {
    const isEligible = await isEligibleForRebate(userId);
    if (isEligible) {
      const pendingAmount = await getPendingRebateAmount(userId);
      if (pendingAmount > 0) {
        await claimPendingRebates(userId);
      }
    }
  } catch (error) {
    console.error("自动领取返利失败:", error);
  }
}
/**
 * 每日结算所有符合条件的用户返利
 * 在每天12点自动执行
 */
export async function dailyRebateSettlement(): Promise<void> {
  try {
    console.log("[返利结算] 开始执行每日返利结算...");
    
    // 获取所有有待领取返利的用户
    const pendingRebates = await PendingRebate.findAll({
      attributes: ['walletId'],
      group: ['walletId']
    });
    
    const walletIds = pendingRebates.map(rebate => rebate.walletId);
    console.log(`[返利结算] 找到${walletIds.length}个有待领取返利的用户`);
    
    // 对每个用户检查是否符合条件并结算
    let settledCount = 0;
    for (const walletId of walletIds) {
      try {
        const isEligible = await isEligibleForRebate(walletId);
        if (isEligible) {
          const amount = await claimPendingRebates(walletId);
          if (amount > 0) {
            settledCount++;
            console.log(`[返利结算] 用户${walletId}结算成功，金额: ${amount}`);
          }
        } else {
          console.log(`[返利结算] 用户${walletId}未完成3轮游戏，跳过结算`);
        }
      } catch (error) {
        console.error(`[返利结算] 处理用户${walletId}时出错:`, error);
      }
    }
    
    console.log(`[返利结算] 每日结算完成，成功结算${settledCount}个用户`);
  } catch (error) {
    console.error("[返利结算] 执行每日结算时出错:", error);
  }
}

/**
 * 获取用户每日推荐返利详情
 * @param walletId 钱包ID
 * @param days 查询天数（默认7天）
 */
export async function getDailyRebateDetails(walletId: number, days: number = 7): Promise<any> {
  // 获取指定天数的日期范围
  const endDate = dayjs().endOf('day').toDate();
  const startDate = dayjs().subtract(days - 1, 'day').startOf('day').toDate();
  
  // 获取日期范围内的所有日期
  const dateRange = [];
  for (let i = 0; i < days; i++) {
    dateRange.push(dayjs().subtract(i, 'day').format('YYYY-MM-DD'));
  }
  
  // 获取用户在日期范围内已领取的返利记录
  const claimedRebates = await WalletHistory.findAll({
    where: {
      walletId,
      action: 'in',
      reference: 'Claimed Ticket Rebate',
      createdAt: {
        [Op.between]: [startDate, endDate]
      }
    },
    order: [['createdAt', 'DESC']]
  });
  
  // 获取用户当前待领取的返利
  const pendingRebates = await PendingRebate.findAll({
    where: { walletId },
    include: [
      {
        model: User,
        as: 'sourceUser',
        attributes: ['id', 'username', 'photoUrl']
      }
    ]
  });
  
  // 获取用户的5层下线信息
  const downlineInfo = await getDownlineRebateInfo(walletId);
  
  // 构建每日返利数据
  const dailyRebates = dateRange.map(date => {
    const dayStart = dayjs(date).startOf('day').toDate();
    const dayEnd = dayjs(date).endOf('day').toDate();
    
    // 查找当天的领取记录
    const dayRebates = claimedRebates.filter(rebate => 
      rebate.createdAt >= dayStart && rebate.createdAt <= dayEnd
    );
    
    // 计算当天领取的总金额
    const totalAmount = dayRebates.reduce((sum, rebate) => new BigNumber(sum).plus(rebate.amount).toNumber(), 0);
    
    // 判断当天是否已领取
    const isClaimed = dayRebates.length > 0;
    
    return {
      date,
      isClaimed,
      amount: totalAmount,
      records: dayRebates.map(rebate => ({
        id: rebate.id,
        amount: rebate.amount,
        createdAt: rebate.createdAt,
        remark: rebate.fe_display_remark || rebate.developer_remark
      }))
    };
  });
  
  // 检查今日是否有资格领取返利
  const isEligibleToday = await isEligibleForRebate(walletId);
  
  return {
    dailyRebates,
    pendingRebates: {
      total: pendingRebates.reduce((sum, rebate) => new BigNumber(sum).plus(rebate.amount).toNumber(), 0),
      count: pendingRebates.length,
      canClaim: isEligibleToday && pendingRebates.length > 0,
      details: pendingRebates.map(rebate => ({
        id: rebate.id,
        amount: rebate.amount,
        level: rebate.level,
        //@ts-ignore
        sourceUser: rebate.sourceUser ? {
          //@ts-ignore
          id: rebate.sourceUser.id,
          //@ts-ignore
          username: rebate.sourceUser.username,
          //@ts-ignore
          photoUrl: rebate.sourceUser.photoUrl
        } : null
      }))
    },
    downlineInfo
  };
}

/**
 * 获取用户每日推广返利历史
 */
export async function getDailyPromotionRebateHistory(
  walletId: number, 
  page: number = 1,
  pageSize: number = 10
): Promise<any> {
  // 获取今日日期和时间范围
  const today = dayjs();


  // 获取历史返利记录
  const claimedRebates = await WalletHistory.findAndCountAll({
    where: {
      walletId,
      action: 'in',
      reference: 'Claimed Ticket Rebate'
    },
    order: [['createdAt', 'DESC']],
    limit: pageSize,
    offset: (page - 1) * pageSize
  });

  // 按日期分组统计历史记录
  const dailyStats = new Map();
  
  // 处理历史记录
  claimedRebates.rows.forEach(rebate => {
    const date = dayjs(rebate.createdAt).format('YYYY-MM-DD');
    if (!dailyStats.has(date)) {
      dailyStats.set(date, {
        date,
        totalAmount: 0
      });
    }
    const dayStats = dailyStats.get(date);
    dayStats.totalAmount = new BigNumber(dayStats.totalAmount).plus(rebate.amount).toNumber();
  });

  // 获取今日待领取返利和资格状态
  const pendingTotal = await getPendingRebateAmount(walletId);
  const isEligibleToday = await isEligibleForRebate(walletId);

  // 如果有待领取返利，添加今日记录
  const todayDate = today.format('YYYY-MM-DD');
  if (pendingTotal > 0 || isEligibleToday) {
    dailyStats.set(todayDate, {
      date: todayDate,
      totalAmount: pendingTotal,
      isPending: true,
      canClaim: isEligibleToday
    });
  }

  // 转换为数组并按日期排序
  const dailyRecords = Array.from(dailyStats.values()).sort((a, b) => 
    dayjs(b.date).valueOf() - dayjs(a.date).valueOf()
  );

  return {
    dailyRecords,
    pagination: {
      total: claimedRebates.count + (pendingTotal > 0 ? 1 : 0), // 总记录数加上今天（如果有待领取返利）
      currentPage: page,
      pageSize,
      totalPages: Math.ceil(claimedRebates.count / pageSize)
    }
  };
}

/**
 * 获取用户的下线推广统计
 * @param walletId 钱包ID
 */
async function getPromotionDownlineStats(walletId: number): Promise<any> {
  // 获取用户信息
  const wallet = await UserWallet.findByPk(walletId);
  if (!wallet) {
    return [];
  }
  
  const userId = wallet.userId;
  
  // 获取今日的开始和结束时间
  const today = dayjs();
  const startOfDay = today.startOf('day').toDate();
  const endOfDay = today.endOf('day').toDate();
  
  // 获取用户的5层下线统计
  const result = [];
  
  for (let level = 1; level <= 5; level++) {
    // 根据层级获取下线用户
    const downlineUsers = await getDownlineUsersByLevel(userId, level);
    const userIds = downlineUsers.map(user => user.id);
    
    if (userIds.length === 0) {
      result.push({
        level,
        userCount: 0,
        gameCount: 0,
        totalBetAmount: 0
      });
      continue;
    }
    
    // 获取这些用户的钱包ID
    const wallets = await UserWallet.findAll({
      where: { userId: { [Op.in]: userIds } },
      attributes: ['id']
    });
    
    const walletIds = wallets.map(wallet => wallet.id);
    
    // 获取今日游戏统计
    const gameStats = await GameHistory.findAll({
      where: {
        walletId: { [Op.in]: walletIds },
        createdAt: { [Op.between]: [startOfDay, endOfDay] }
      },
      attributes: [
        [sequelize.fn('COUNT', sequelize.col('id')), 'gameCount'],
        [sequelize.fn('SUM', sequelize.col('betAmount')), 'totalBetAmount']
      ],
      raw: true
    });
    
    result.push({
      level,
      userCount: userIds.length,
      //@ts-ignore
      gameCount: Number(gameStats[0]?.gameCount || 0),
      //@ts-ignore
      totalBetAmount: new BigNumber(gameStats[0]?.totalBetAmount || 0).toNumber()
    });
  }
  
  return result;
}

/**
 * 根据层级获取下线用户
 * @param userId 用户ID
 * @param targetLevel 目标层级
 */
async function getDownlineUsersByLevel(userId: number, targetLevel: number): Promise<any[]> {
  if (targetLevel === 1) {
    // 直接下线
    return await User.findAll({
      where: { referrerId: userId }
    });
  }
  
  // 获取上一级的下线用户
  const prevLevelUsers = await getDownlineUsersByLevel(userId, targetLevel - 1);
  const prevUserIds = prevLevelUsers.map(user => user.id);
  
  if (prevUserIds.length === 0) {
    return [];
  }
  
  // 获取这些用户的下线
  return await User.findAll({
    where: { referrerId: { [Op.in]: prevUserIds } }
  });
}

/**
 * 获取用户5层下线的返利信息
 * @param walletId 钱包ID
 */
async function getDownlineRebateInfo(walletId: number) {
  // 获取用户信息
  const wallet = await UserWallet.findByPk(walletId);
  if (!wallet) {
    return [];
  }
  
  const userId = wallet.userId;
  
  // 获取5层下线的用户信息和返利贡献
  const result = [];
  
  // 递归获取下线用户
  //@ts-ignore
  async function getDownlineUsers(currentUserId: number, level: number = 1, maxLevel: number = 5) {
    if (level > maxLevel) return [];
    
    // 获取当前用户的直接下线
    // 修改这里，不使用 wallets 别名，而是单独查询钱包信息
    const directDownlines = await User.findAll({
      where: { referrerId: currentUserId }
    });
    
    const levelUsers = [];
    
    // 处理当前级别的用户
    for (const downline of directDownlines) {
      // 获取该用户的钱包
      const downlineWallets = await UserWallet.findAll({
        where: { userId: downline.id }
      });
      
      const walletAddress = downlineWallets.length > 0 ? downlineWallets[0].walletAddress : null;
      
      // 获取该用户贡献的返利金额
      const rebateAmount = await PendingRebate.sum('amount', {
        where: {
          walletId,
          sourceUserId: downline.id
        }
      }) || 0;
      
      // 获取该用户的游戏次数
      const gameCount = await GameHistory.count({
        where: {
          userId: downline.id
        }
      });
      
      levelUsers.push({
        userId: downline.id,
        username: downline.username,
        photoUrl: downline.photoUrl,
        walletAddress,
        rebateAmount,
        gameCount,
        level
      });
      
      // 递归获取下一级用户
      if (level < maxLevel) {
        //@ts-ignore
        const nextLevelUsers = await getDownlineUsers(downline.id, level + 1, maxLevel);
        levelUsers.push(...nextLevelUsers);
      }
    }
    
    return levelUsers;
  }
  
  // 获取所有5层下线
  const allDownlines = await getDownlineUsers(userId);
  
  // 按层级分组
  for (let i = 1; i <= 5; i++) {
    //@ts-ignore
    const levelUsers = allDownlines.filter(user => user.level === i);
    //@ts-ignore
    const levelRebateTotal = levelUsers.reduce((sum, user) => new BigNumber(sum).plus(user.rebateAmount).toNumber(), 0);
    
    result.push({
      level: i,
      userCount: levelUsers.length,
      rebateTotal: levelRebateTotal,
      users: levelUsers
    });
  }
  
  return result;
}