// src/services/userService.ts

import { Op } from "sequelize";
import { t } from "../i18n";
import { redis } from "../config/redis";
import { User } from "../models/User";
import { Chest } from "../models/Chest";
import { generateUniqueCode } from "../utils/random";
import { UserWallet } from "../models/UserWallet";
import { sequelize } from "../config/db";
import { recordWalletHistory } from "./walletHistoryService";
import { generateChestReward } from "./chestService";
// 在文件顶部添加必要的导入
import { v4 as uuidv4 } from "uuid";
import { WalletHistory, Announcement, ShareBoostLink } from "../models";
import { createShareBoostLink, processReferralBoost } from "./jackpotChestService";
// 添加导入
import { handleReferralChests } from '../services/referralService';

/**
 * 注册用户，支持传入可选的 “code”（上级的专属邀请码）
 * @param initData Telegram WebApp 的 initData 字符串
 * @param code 邀请人的专属码 (可空)
 * @returns 注册后的用户实体
 */
export async function registerUser(
  initData: string,
  code?: string,
  network?: string,
  transaction?: any
): Promise<User> {
  if (!initData) {
    throw new Error(t("errors.missingInitData"));
  }

  // 1) 解析 Telegram WebApp 的 initData
  const parsedData = new URLSearchParams(initData);

  const authDateStr = parsedData.get("auth_date") || "0";
  const hash = parsedData.get("hash") || "";
  const userJson = parsedData.get("user");

  let telegramId = "";
  let username = "";
  let firstName = "";
  let lastName = "";
  let photoUrl = "";
  let languageCode = "";
  let allowsWriteToPm = false;
  let isPremium = false; // 标记新注册用户是否 TG Premium

  if (userJson) {
    const telegramUser = JSON.parse(userJson);
    telegramId = String(telegramUser.id || "");
    username = telegramUser.username || "";
    firstName = telegramUser.first_name || "";
    lastName = telegramUser.last_name || "";
    languageCode = telegramUser.language_code || "";
    allowsWriteToPm = telegramUser.allows_write_to_pm || false;
    isPremium = telegramUser.is_premium || false;
    photoUrl = telegramUser.photo_url || "";
  } else {
    console.error("User data not found in initData");
  }

  if (!telegramId) {
    throw new Error(t("errors.invalidTelegramUserId"));
  }

  // 2) 检查数据库是否已有此 Telegram 用户
  let user = await User.findOne({ where: { telegramId }, transaction });
  if (user) {
    // 如果已经存在，就直接返回
    return user;
  }
  let referUser: any;

  if (code) {
    referUser = await UserWallet.findOne({ where: { code }, transaction });
    if (!referUser) {
      throw new Error(t("errors.invitationCodeNotExist"));
    }
  }

  // 5) 创建新用户
  user = await User.create({
    telegramId,
    username,
    firstName,
    lastName,
    photoUrl,
    authDate: parseInt(authDateStr, 10), // 把字符串转成数字
    hash,
    telegram_premium: isPremium,
    hasFollowedChannel: false,
    refWalletAddress: referUser?.walletAddress,
    referrerId: referUser?.userId
  }, { transaction });

  // 确保user不为null后再获取id
  const userId = user?.id;
  if (!userId) {
    throw new Error(t("errors.userNotFound"));
  }

  if (code) {
    await User.increment(
      {
        referralCount: 1,
      },
      {
        where: { id: referUser?.userId },
        transaction
      }
    );

    // 处理推荐宝箱
    await handleReferralChests(
      referUser.userId,
      referUser.id,
      user.telegram_premium!,
      transaction
    );
  }
  return user;
}


/**
 * 标记用户已关注 TG 频道
 */
export async function followChannel(userId: number) {
  const user = await User.findByPk(userId);
  if (!user) {
    throw new Error(t("errors.userNotFound"));
  }
  if (!user.hasFollowedChannel) {
    user.hasFollowedChannel = true;
    await user.save();
  }
  return user;
}


export async function userInfo(userId: number, walletId: number) {
  const user = await User.findByPk(userId);

  if (!user) {
    throw new Error(t("errors.userNotFound"));
  }

  let users = [];

  const wallet = await UserWallet.findByPk(walletId);
  let refUser: any;
  if (user.referrerId) {
    refUser = await User.findByPk(user.referrerId);
  }

  
  users.push({
    referralCount: user.referralCount,
    telegramId: user.telegramId,
    username: user.username,
    firstName: user.firstName,
    lastName: user.lastName,
    photoUrl: user.photoUrl,
    telegram_premium: user.telegram_premium,
    walletAddress: wallet?.walletAddress,
    referral: {
      telegramId: refUser?.telegramId,
      username: refUser?.username,
      firstName: refUser?.firstName,
      lastName: refUser?.lastName,
      photoUrl: refUser?.photoUrl,
      walletAddress: user.refWalletAddress,
    },
    gem: wallet?.gem,
    diamond: wallet?.diamond, // 新增：钻石字段
    ton: wallet?.ton,
    usd: wallet?.usd,
    ticket: wallet?.ticket,
    free_ticket: wallet?.free_ticket,
    moof: wallet?.moof,
    unlockMoof: wallet?.unlockMoof,
    fragment_green:wallet?.fragment_green,
    fragment_blue:wallet?.fragment_blue,
    fragment_purple:wallet?.fragment_purple,
    fragment_gold:wallet?.fragment_gold,
    code: wallet?.code,
    // PHRS相关信息
    phrsBalance: wallet?.phrsBalance || '0',
    phrsWalletAddress: wallet?.phrsWalletAddress,
    lastPhrsUpdateTime: wallet?.lastPhrsUpdateTime,
    createdAt: user.createdAt,
    email: user.email
  });

  return users;
}
// 在文件末尾添加新的函数

/**
 * 绑定用户邮箱
 * @param userId 用户ID
 * @param email 邮箱地址
 * @returns 更新后的用户信息
 */
export async function bindUserEmail(userId: number, email: string) {
  // 1) 查找用户
  const user = await User.findByPk(userId);
  if (!user) {
    throw new Error(t("errors.userNotExist"));
  }

  // 2) 验证邮箱格式
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(email)) {
    throw new Error(t("errors.invalidEmailFormat"));
  }

  // 3) 检查邮箱是否已被其他用户使用
  const existingUser = await User.findOne({ where: { email } });
  if (existingUser && existingUser.id !== userId) {
    throw new Error(t("errors.emailAlreadyBound"));
  }

  // 4) 更新用户邮箱
  user.email = email;
  await user.save();

  // 5) 更新 Redis 缓存
  await redis.hset(`user:${userId}`, {
    email,
  });

  return {
    success: true,
    message: t("success.emailBound"),
    user: {
      id: user.id,
      email: user.email,
    },
  };
}
/**
 * 转账USD
 * @param fromUserId 发送方用户ID
 * @param fromWalletId 发送方钱包ID
 * @param toWalletAddress 接收方钱包地址
 * @param amount 转账金额
 * @returns 转账结果
 */
export async function transferUSD(
  fromUserId: number,
  fromWalletId: number,
  toWalletAddress: string,
  amount: number
) {
  // 验证参数
  if (!toWalletAddress) {
    throw new Error(t("errors.receiverWalletAddressEmpty"));
  }

  if (!amount || amount <= 0) {
    throw new Error(t("errors.transferAmountMustBePositive"));
  }

  // 开始事务
  const transaction = await sequelize.transaction();

  try {
    // 1. 查找发送方钱包
    const fromWallet = await UserWallet.findByPk(fromWalletId, { transaction });
    if (!fromWallet) {
      throw new Error(t("errors.senderWalletNotExist"));
    }

    // 2. 检查余额是否足够
    if ((fromWallet.usd || 0) < amount) {
      throw new Error(t("errors.insufficientBalance"));
    }

    // 3. 查找接收方钱包
    const toWallet = await UserWallet.findOne({
      where: { walletAddress: toWalletAddress },
      transaction
    });

    if (!toWallet) {
      throw new Error(t("errors.receiverWalletNotExist"));
    }

    // 4. 不能转给自己
    if (fromWallet.id === toWallet.id) {
      throw new Error(t("errors.cannotTransferToSelf"));
    }

    // 5. 执行转账 - 使用原子操作
    // 扣除发送方USD (使用原子操作)
    await UserWallet.decrement('usd', {
      by: amount,
      where: {
        id: fromWalletId,
        usd: { [Op.gte]: amount } // 确保余额充足
      },
      transaction
    });

    // 检查是否成功扣款
    const updatedFromWallet = await UserWallet.findByPk(fromWalletId, { transaction });
    if (!updatedFromWallet || (updatedFromWallet.usd === fromWallet.usd)) {
      throw new Error(t("errors.transferFailedInsufficientBalance"));
    }

    // 增加接收方USD (使用原子操作)
    await UserWallet.increment('usd', {
      by: amount,
      where: { id: toWallet.id },
      transaction
    });

    // 6. 记录转账历史 - 发送方
    await recordWalletHistory(
      fromUserId,
      fromWalletId,
      "usd",
      amount,
      "Transfer USD",
      "out",
      "usd",
      "usd",
      t("walletHistory.transferOut", { amount, address: toWalletAddress }),
      t("walletHistory.transferOutDev", { address: toWalletAddress })
    );

    // 7. 记录转账历史 - 接收方
    await recordWalletHistory(
      toWallet.userId,
      toWallet.id,
      "usd",
      amount,
      "Receive USD",
      "in",
      "usd",
      "usd",
      t("walletHistory.transferIn", { amount, address: fromWallet.walletAddress || fromWalletId }),
      t("walletHistory.transferInDev", { address: fromWallet.walletAddress || fromWalletId })
    );

    // 提交事务
    await transaction.commit();

    return {
      success: true,
      message: t("success.transferSuccess"),
      data: {
        fromWalletId,
        toWalletAddress,
        amount,
        timestamp: new Date(),
      },
    };
  } catch (error) {
    // 回滚事务
    await transaction.rollback();
    throw error;
  }
}


/**
 * 获取用户的推荐宝箱数量
 * @param userId 用户ID
 * @param walletId 钱包ID
 * @returns 推荐宝箱数量信息
 */
export async function getReferralChestsCount(userId: number, walletId: number) {
  // 1. 查找用户
  const user = await User.findByPk(userId);
  if (!user) {
    throw new Error(t("errors.userNotExist"));
  }

  // 2. 查找钱包
  const wallet = await UserWallet.findByPk(walletId);
  if (!wallet || wallet.userId !== userId) {
    throw new Error(t("errors.walletNotExist"));
  }

  // 3. 查询未打开的推荐宝箱数量，按来源分组
  const chestCounts = await Chest.findAll({
    where: {
      userId,
      walletId,
      type: "referral",
      isOpened: false
    },
    attributes: [
      'source',
      [sequelize.fn('COUNT', sequelize.col('id')), 'count']
    ],
    group: ['source']
  });
  // 格式化返回结果
  const result = {
    total: 0,
    premium_referral: 0,
    normal_referral: 0
  };

  chestCounts.forEach((item: any) => {
    const count = parseInt(item.getDataValue('count'));
    const source = item.getDataValue('source');
    
    if (source === 'premium_referral') {
      result.premium_referral = count;
    } else if (source === 'normal_referral') {
      result.normal_referral = count;
    }
    result.total += count;
  });

  return result;
}