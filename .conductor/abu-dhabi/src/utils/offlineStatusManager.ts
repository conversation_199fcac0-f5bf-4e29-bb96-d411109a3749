// 统一的离线状态管理工具类
// 解决系统中离线判定时间阈值不一致的问题

/**
 * 离线状态配置常量
 */
export const OFFLINE_CONFIG = {
  // 最小请求间隔（秒）- 防止频繁请求
  MIN_REQUEST_INTERVAL: 1,
  
  // 在线状态阈值（秒）- 超过此时间视为离线
  ONLINE_THRESHOLD: 120, // 2分钟
  
  // 资源更新有效时间窗口（秒）
  RESOURCE_UPDATE_WINDOW: {
    MIN: 1,    // 最小间隔1秒
    MAX: 120   // 最大间隔2分钟
  },
  
  // 离线奖励计算阈值（秒）
  OFFLINE_REWARD_THRESHOLD: 120, // 2分钟
  
  // 最大离线奖励时间（秒）
  MAX_OFFLINE_REWARD_TIME: 24 * 60 * 60 // 24小时
};

/**
 * 离线状态信息接口
 */
export interface OfflineStatusInfo {
  isOnline: boolean;
  isOffline: boolean;
  offlineTimeInSeconds: number;
  offlineTimeInHours: number;
  canReceiveResources: boolean;
  canReceiveOfflineReward: boolean;
  statusReason: string;
}

/**
 * 统一的离线状态管理类
 */
export class OfflineStatusManager {
  
  /**
   * 计算用户的离线状态信息
   * @param lastActiveTime 用户最后活跃时间
   * @param language 语言设置
   * @returns 离线状态信息
   */
  static calculateOfflineStatus(
    lastActiveTime: Date | null, 
    language: string = 'zh'
  ): OfflineStatusInfo {
    
    // 如果没有最后活跃时间，视为新用户
    if (!lastActiveTime) {
      return {
        isOnline: false,
        isOffline: false,
        offlineTimeInSeconds: 0,
        offlineTimeInHours: 0,
        canReceiveResources: false,
        canReceiveOfflineReward: false,
        statusReason: language === 'en' ? 'New user, no activity record' : '新用户，无活动记录'
      };
    }

    const now = new Date();
    const lastActiveDate = lastActiveTime instanceof Date ? lastActiveTime : new Date(lastActiveTime);
    const offlineTimeInSeconds = Math.floor((now.getTime() - lastActiveDate.getTime()) / 1000);
    const offlineTimeInHours = offlineTimeInSeconds / 3600;

    // 判断各种状态
    const isOnline = offlineTimeInSeconds < OFFLINE_CONFIG.ONLINE_THRESHOLD;
    const isOffline = offlineTimeInSeconds >= OFFLINE_CONFIG.ONLINE_THRESHOLD;
    
    // 判断是否可以接收资源更新
    const canReceiveResources = offlineTimeInSeconds >= OFFLINE_CONFIG.RESOURCE_UPDATE_WINDOW.MIN && 
                               offlineTimeInSeconds <= OFFLINE_CONFIG.RESOURCE_UPDATE_WINDOW.MAX;
    
    // 判断是否可以接收离线奖励
    const canReceiveOfflineReward = offlineTimeInSeconds >= OFFLINE_CONFIG.OFFLINE_REWARD_THRESHOLD;

    // 生成状态原因
    let statusReason: string;
    if (offlineTimeInSeconds < OFFLINE_CONFIG.MIN_REQUEST_INTERVAL) {
      statusReason = language === 'en' ? 
        'Too frequent requests (minimum 1 second interval)' : 
        '请求过于频繁（最少间隔1秒）';
    } else if (isOnline) {
      statusReason = language === 'en' ? 
        `Online (last active ${offlineTimeInSeconds} seconds ago)` : 
        `在线状态（${offlineTimeInSeconds}秒前活跃）`;
    } else {
      statusReason = language === 'en' ? 
        `Offline (last active ${Math.floor(offlineTimeInHours * 100) / 100} hours ago)` : 
        `离线状态（${Math.floor(offlineTimeInHours * 100) / 100}小时前活跃）`;
    }

    return {
      isOnline,
      isOffline,
      offlineTimeInSeconds,
      offlineTimeInHours,
      canReceiveResources,
      canReceiveOfflineReward,
      statusReason
    };
  }

  /**
   * 检查是否在有效的资源更新时间窗口内
   * @param lastActiveTime 用户最后活跃时间
   * @returns 是否在有效时间窗口内
   */
  static isInValidResourceUpdateWindow(lastActiveTime: Date | null): {
    isValid: boolean;
    reason: string;
    timeElapsedSeconds: number;
  } {
    if (!lastActiveTime) {
      return {
        isValid: false,
        reason: '新用户，无活动记录',
        timeElapsedSeconds: 0
      };
    }

    const now = new Date();
    const lastActiveDate = lastActiveTime instanceof Date ? lastActiveTime : new Date(lastActiveTime);
    const timeElapsedSeconds = Math.floor((now.getTime() - lastActiveDate.getTime()) / 1000);

    const isValid = timeElapsedSeconds >= OFFLINE_CONFIG.RESOURCE_UPDATE_WINDOW.MIN && 
                   timeElapsedSeconds <= OFFLINE_CONFIG.RESOURCE_UPDATE_WINDOW.MAX;

    let reason: string;
    if (timeElapsedSeconds < OFFLINE_CONFIG.RESOURCE_UPDATE_WINDOW.MIN) {
      reason = `距离上次更新时间太短（最少${OFFLINE_CONFIG.RESOURCE_UPDATE_WINDOW.MIN}秒）`;
    } else if (timeElapsedSeconds > OFFLINE_CONFIG.RESOURCE_UPDATE_WINDOW.MAX) {
      reason = `用户离线（距离上次更新超过${OFFLINE_CONFIG.RESOURCE_UPDATE_WINDOW.MAX / 60}分钟）`;
    } else {
      reason = '在有效时间窗口内';
    }

    return {
      isValid,
      reason,
      timeElapsedSeconds
    };
  }

  /**
   * 计算有效的离线奖励时间
   * @param offlineTimeInSeconds 实际离线时间（秒）
   * @returns 有效的离线奖励时间（秒）
   */
  static calculateEffectiveOfflineRewardTime(offlineTimeInSeconds: number): number {
    // 限制最大离线奖励时间
    return Math.min(offlineTimeInSeconds, OFFLINE_CONFIG.MAX_OFFLINE_REWARD_TIME);
  }

  /**
   * 格式化离线时间显示
   * @param offlineTimeInSeconds 离线时间（秒）
   * @param language 语言设置
   * @returns 格式化的时间字符串
   */
  static formatOfflineTime(offlineTimeInSeconds: number, language: string = 'zh'): string {
    if (offlineTimeInSeconds < 60) {
      return language === 'en' ? `${offlineTimeInSeconds} seconds` : `${offlineTimeInSeconds}秒`;
    } else if (offlineTimeInSeconds < 3600) {
      const minutes = Math.floor(offlineTimeInSeconds / 60);
      return language === 'en' ? `${minutes} minutes` : `${minutes}分钟`;
    } else {
      const hours = Math.floor(offlineTimeInSeconds / 3600);
      const minutes = Math.floor((offlineTimeInSeconds % 3600) / 60);
      if (minutes === 0) {
        return language === 'en' ? `${hours} hours` : `${hours}小时`;
      } else {
        return language === 'en' ? `${hours}h ${minutes}m` : `${hours}小时${minutes}分钟`;
      }
    }
  }
}
