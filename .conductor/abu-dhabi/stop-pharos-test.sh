#!/bin/bash

# Pharos Test 环境停止脚本

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
CONTAINER_NAME="wolf-fun-pharos-test-container"
NETWORK_NAME="pharos_network"

echo -e "${BLUE}🛑 停止 Pharos Test 环境...${NC}"

# 1. 停止应用容器
echo -e "${YELLOW}🛑 停止应用容器...${NC}"
docker stop ${CONTAINER_NAME} 2>/dev/null || echo -e "${YELLOW}⚠️ 应用容器未运行${NC}"
docker rm ${CONTAINER_NAME} 2>/dev/null || echo -e "${YELLOW}⚠️ 应用容器不存在${NC}"

# 2. 停止数据库和 Redis（可选）
read -p "是否同时停止数据库和 Redis？(y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo -e "${YELLOW}🛑 停止数据库和 Redis...${NC}"
    
    # 停止 MySQL
    docker stop mysql-pharos-test 2>/dev/null || echo -e "${YELLOW}⚠️ MySQL 容器未运行${NC}"
    docker rm mysql-pharos-test 2>/dev/null || echo -e "${YELLOW}⚠️ MySQL 容器不存在${NC}"

    # 停止 Redis
    docker stop redis-pharos-test 2>/dev/null || echo -e "${YELLOW}⚠️ Redis 容器未运行${NC}"
    docker rm redis-pharos-test 2>/dev/null || echo -e "${YELLOW}⚠️ Redis 容器不存在${NC}"
    
    # 删除网络
    docker network rm ${NETWORK_NAME} 2>/dev/null || echo -e "${YELLOW}⚠️ 网络不存在或仍被使用${NC}"
fi

# 3. 清理未使用的资源
echo -e "${YELLOW}🧹 清理未使用的资源...${NC}"
docker container prune -f
docker image prune -f

echo -e "${GREEN}✅ Pharos Test 环境已停止${NC}"

# 4. 显示剩余容器
echo -e "${BLUE}📊 剩余相关容器:${NC}"
docker ps -a | grep -E "(pharos-test|wolf-fun)" || echo -e "${GREEN}   没有相关容器在运行${NC}"
