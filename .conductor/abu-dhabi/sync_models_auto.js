const { Sequelize } = require('sequelize');
require('dotenv').config();

/**
 * 自动同步模型到数据库的脚本
 * 可以通过命令行参数控制同步模式
 */
async function syncModelsToDatabase() {
  console.log('🔧 开始自动同步模型到数据库...');
  
  try {
    // 导入编译后的模型
    console.log('📝 导入编译后的模型...');
    const models = require('./dist/models/index.js');
    
    console.log('📊 发现以下模型:');
    Object.keys(models).forEach(modelName => {
      if (modelName !== 'sequelize' && modelName !== 'Sequelize') {
        console.log(`   - ${modelName}`);
      }
    });
    
    if (!models.sequelize) {
      throw new Error('模型中没有找到 sequelize 实例');
    }
    
    const sequelize = models.sequelize;
    
    // 测试数据库连接
    console.log('\n🔗 测试数据库连接...');
    await sequelize.authenticate();
    console.log('✅ 数据库连接成功');
    
    // 显示数据库信息
    console.log('\n🗄️  数据库信息:');
    const [dbInfo] = await sequelize.query('SELECT DATABASE() as current_db, VERSION() as version');
    console.log(`   - 当前数据库: ${dbInfo[0].current_db}`);
    console.log(`   - MySQL版本: ${dbInfo[0].version}`);
    
    // 检查命令行参数
    const args = process.argv.slice(2);
    const isForceMode = args.includes('--force');
    const isAlterMode = args.includes('--alter');
    
    if (isForceMode) {
      console.log('\n⚠️  强制模式：将删除所有现有数据并重建表！');
      console.log('🔄 开始强制重建数据表...');
      
      await sequelize.sync({ 
        force: true,
        logging: process.env.NODE_ENV === 'development' ? console.log : false
      });
      
      console.log('🎉 数据表强制重建完成！');
    } else if (isAlterMode) {
      console.log('\n🔄 开始同步数据表（ALTER模式，保留数据但修改结构）...');
      
      await sequelize.sync({ 
        alter: true,
        logging: process.env.NODE_ENV === 'development' ? console.log : false
      });
      
      console.log('🎉 数据表ALTER同步完成！');
    } else {
      console.log('\n🔄 开始同步数据表（安全模式，仅创建新表）...');
      
      await sequelize.sync({
        logging: process.env.NODE_ENV === 'development' ? console.log : false
      });
      
      console.log('🎉 数据表安全同步完成！');
    }
    
    // 显示创建的表
    console.log('\n📋 数据库中的表:');
    const [tables] = await sequelize.query('SHOW TABLES');
    if (tables.length > 0) {
      tables.forEach(table => {
        const tableName = Object.values(table)[0];
        console.log(`   - ${tableName}`);
      });
      console.log(`\n📊 总共 ${tables.length} 个数据表`);
    } else {
      console.log('   - 暂无数据表');
    }
    
    await sequelize.close();
    console.log('\n✅ 同步完成，数据库连接已关闭');
    
  } catch (error) {
    console.error('❌ 同步失败:', error.message);
    
    if (process.env.NODE_ENV === 'development') {
      console.error('错误详情:', error.stack);
    }
    
    if (error.code === 'MODULE_NOT_FOUND') {
      console.log('\n💡 解决方案：');
      console.log('   1. 编译 TypeScript: npm run build');
      console.log('   2. 确保 dist/models/index.js 存在');
    } else if (error.code === 'ECONNREFUSED') {
      console.log('\n💡 数据库连接失败，请检查：');
      console.log('   1. 数据库服务是否启动');
      console.log('   2. 环境变量配置是否正确');
    }
    
    process.exit(1);
  }
}

// 显示使用帮助
function showHelp() {
  console.log(`
📖 使用说明:

基本用法:
  node sync_models_auto.js                # 安全模式：仅创建新表，不修改现有表
  node sync_models_auto.js --alter        # ALTER模式：修改表结构，保留数据
  node sync_models_auto.js --force        # 强制模式：删除并重建所有表（危险！）

参数说明:
  --alter    修改现有表结构以匹配模型定义（推荐用于开发环境）
  --force    强制删除并重建所有表（会丢失所有数据！）
  --help     显示此帮助信息

示例:
  npm run sync:models              # 安全同步
  npm run sync:models:alter        # ALTER同步
  npm run sync:models:force        # 强制重建（危险）
`);
}

// 处理命令行参数
if (process.argv.includes('--help') || process.argv.includes('-h')) {
  showHelp();
  process.exit(0);
}

// 如果直接运行此文件，则执行同步
if (require.main === module) {
  syncModelsToDatabase();
}

module.exports = { syncModelsToDatabase };