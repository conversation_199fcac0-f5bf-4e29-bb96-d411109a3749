const { Sequelize } = require('sequelize');
require('dotenv').config();

async function syncModelsToDatabase() {
  console.log('🔧 开始同步模型到数据库...');
  
  try {
    // 直接导入编译后的模型
    console.log('📝 导入编译后的模型...');
    const models = require('./dist/models/index.js');
    
    console.log('📊 发现以下模型:');
    Object.keys(models).forEach(modelName => {
      if (modelName !== 'sequelize' && modelName !== 'Sequelize') {
        console.log(`   - ${modelName}`);
      }
    });
    
    // 检查是否有 sequelize 实例
    if (!models.sequelize) {
      throw new Error('模型中没有找到 sequelize 实例');
    }
    
    const sequelize = models.sequelize;
    
    // 测试数据库连接
    console.log('\n🔗 测试数据库连接...');
    await sequelize.authenticate();
    console.log('✅ 数据库连接成功');
    
    // 显示数据库信息
    console.log('\n🗄️  数据库信息:');
    const [dbInfo] = await sequelize.query('SELECT DATABASE() as current_db, VERSION() as version');
    console.log(`   - 当前数据库: ${dbInfo[0].current_db}`);
    console.log(`   - MySQL版本: ${dbInfo[0].version}`);
    
    // 询问用户是否要强制重建表
    const readline = require('readline');
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });
    
    const answer = await new Promise(resolve => {
      rl.question('是否要强制重建所有表？这将删除所有现有数据！(yes/no): ', resolve);
    });
    
    rl.close();
    
    if (answer.toLowerCase() === 'yes') {
      console.log('\n🔄 开始强制重建数据表...');
      
      await sequelize.sync({ 
        force: true,
        logging: (sql) => {
          console.log('📝 执行SQL:', sql);
        }
      });
      
      console.log('\n🎉 数据表强制重建完成！');
    } else {
      console.log('\n🔄 开始同步数据表（不删除现有数据）...');
      
      await sequelize.sync({ 
        alter: true,
        logging: (sql) => {
          console.log('📝 执行SQL:', sql);
        }
      });
      
      console.log('\n🎉 数据表同步完成！');
    }
    
    // 显示创建的表
    console.log('\n📋 数据库中的表:');
    const [tables] = await sequelize.query('SHOW TABLES');
    if (tables.length > 0) {
      tables.forEach(table => {
        const tableName = Object.values(table)[0];
        console.log(`   - ${tableName}`);
      });
    } else {
      console.log('   - 暂无数据表');
    }
    
    await sequelize.close();
    console.log('\n✅ 同步完成，数据库连接已关闭');
    
  } catch (error) {
    console.error('❌ 同步失败:', error.message);
    console.error('错误详情:', error.stack);
    
    if (error.code === 'MODULE_NOT_FOUND') {
      console.log('\n💡 模块未找到，请确保：');
      console.log('   1. 已编译 TypeScript 文件: npx tsc');
      console.log('   2. dist/models/index.js 文件存在');
    }
    
    process.exit(1);
  }
}

// 如果直接运行此文件，则执行同步
if (require.main === module) {
  syncModelsToDatabase();
}

module.exports = { syncModelsToDatabase };
