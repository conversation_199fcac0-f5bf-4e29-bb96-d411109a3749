# Moofun奶牛农场游戏 - 项目概述

## 项目简介
Moofun奶牛农场是一款点击与挂机类游戏，玩家通过升级牧场区（Farm Plot）和出货线（Delivery Line）获得被动宝石（Gem）收入。游戏核心循环包括养牛产奶、出售牛奶获得宝石、升级设施，以及参与排名和赛季奖励。

## 核心游戏机制

### 核心循环（Core Loop）
1. 养牛产奶：每个牧场格子可容纳牛舍，自动产出牛奶
2. 牛奶经由出货线出售，换取宝石（Gem）
3. 使用宝石升级牛舍、解锁新牧场、强化出货线
4. 持续累积产出，提升排名，参与赛季奖励

### 牧场区（Farm Plot）
- 共20个牧场区，每个可升级至20级
- 每次升级解锁一个牛舍，增加产量和产出速度
- 初始牧场区免费，其余需要使用宝石解锁
- 产量和速度随等级提升而增加

### 出货线（Delivery Line）
- 将累积的牛奶自动打包成「牛奶方塊」并出售
- 每个牛奶方块代表固定单位的牛奶，出售后获得宝石
- 可通过升级提高方块单位、价格和出货速度

## 任务分解

本项目已被分解为以下四个主要任务：

1. **牧场区系统实现**（任务文件：01_farm_plot_system.md）
   - 实现牧场区的基础属性、升级机制和解锁逻辑
   - 设计牛奶生产循环系统

2. **出货线系统实现**（任务文件：02_delivery_line_system.md）
   - 实现出货线的基础属性和升级机制
   - 设计自动出货循环系统

3. **核心游戏循环实现**（任务文件：03_core_game_loop.md）
   - 整合牧场区和出货线系统
   - 实现资源流转和游戏平衡
   - 设计离线收益

4. **数据模型与计算逻辑实现**（任务文件：04_data_models_and_calculations.md）
   - 设计游戏数据模型
   - 实现核心计算公式
   - 设计数据持久化和离线收益计算

## 实施计划

### 第一阶段：基础架构
- 完成数据模型设计和基础计算逻辑
- 实现牧场区和出货线的基本功能

### 第二阶段：核心功能
- 完善牧场区和出货线系统
- 整合核心游戏循环
- 实现离线收益计算

### 第三阶段：优化与测试
- 进行游戏平衡性调整
- 实现排名系统和社交功能
- 进行性能优化
- 全面测试和修复问题

## 技术栈建议
- 后端：Node.js + Express
- 数据库：MongoDB/MySQL
- 实时通信：Socket.io（用于排行榜和社交功能）

## 风险评估
1. 游戏平衡性调整可能需要多次迭代
2. 离线收益计算的准确性需要特别关注
3. 高并发访问可能带来性能挑战

## 后续扩展方向
1. 社交功能：好友系统、公会机制
2. 活动系统：限时活动、特殊奖励
3. 成就系统：里程碑奖励
4. 商店系统：特殊道具和加速器