const axios = require('axios');

// 测试配置
const BASE_URL = 'http://localhost:3456';
const TEST_TOKEN = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************.yugH7LmC26ly1v5D8z-DRHbyMd9R1-CQE76PDLCBF6E';

// 创建axios实例
const api = axios.create({
  baseURL: BASE_URL,
  headers: {
    'Authorization': `Bearer ${TEST_TOKEN}`,
    'Content-Type': 'application/json',
    'Accept-Language': 'zh'
  }
});

// 测试函数
async function testBatchUpdateIncrements() {
  console.log('🧪 开始测试修正后的批量资源更新接口（增量模式）...\n');

  try {
    // 1. 测试无参数调用（系统自动计算）
    console.log('📋 测试1: 无参数调用（系统自动计算）');
    try {
      const response1 = await api.post('/api/wallet/batch-update-resources', {});
      console.log('✅ 无参数调用成功');
      console.log('📊 响应数据:', JSON.stringify(response1.data, null, 2));
      console.log('');
    } catch (error) {
      console.log('❌ 无参数调用失败:', error.response?.data || error.message);
      console.log('');
    }

    // 等待5秒避免防刷保护
    console.log('⏳ 等待5秒避免防刷保护...');
    await new Promise(resolve => setTimeout(resolve, 5000));

    // 2. 测试前端增量验证 - 合理请求
    console.log('📋 测试2: 前端增量验证 - 合理请求');
    try {
      const response2 = await api.post('/api/wallet/batch-update-resources', {
        tempGem: 10.000,    // 合理的GEM增量
        tempMilk: 5.000     // 合理的牛奶增量
      });
      console.log('✅ 合理增量请求成功');
      console.log('📊 响应数据:', JSON.stringify(response2.data, null, 2));
      console.log('');
    } catch (error) {
      console.log('❌ 合理增量请求失败:', error.response?.data || error.message);
      console.log('');
    }

    // 等待5秒避免防刷保护
    console.log('⏳ 等待5秒避免防刷保护...');
    await new Promise(resolve => setTimeout(resolve, 5000));

    // 3. 测试前端增量验证 - 不合理请求（GEM增量过大）
    console.log('📋 测试3: 前端增量验证 - 不合理请求（GEM增量过大）');
    try {
      const response3 = await api.post('/api/wallet/batch-update-resources', {
        tempGem: 10000.000,  // 不合理的大增量
        tempMilk: 5.000
      });
      console.log('✅ 不合理增量请求处理成功');
      console.log('📊 响应数据:', JSON.stringify(response3.data, null, 2));
      console.log('');
    } catch (error) {
      console.log('❌ 不合理增量请求处理失败:', error.response?.data || error.message);
      console.log('');
    }

    // 等待5秒避免防刷保护
    console.log('⏳ 等待5秒避免防刷保护...');
    await new Promise(resolve => setTimeout(resolve, 5000));

    // 4. 测试只更新GEM增量
    console.log('📋 测试4: 只更新GEM增量');
    try {
      const response4 = await api.post('/api/wallet/batch-update-resources', {
        tempGem: 5.000
      });
      console.log('✅ 只更新GEM增量成功');
      console.log('📊 响应数据:', JSON.stringify(response4.data, null, 2));
      console.log('');
    } catch (error) {
      console.log('❌ 只更新GEM增量失败:', error.response?.data || error.message);
      console.log('');
    }

    // 等待5秒避免防刷保护
    console.log('⏳ 等待5秒避免防刷保护...');
    await new Promise(resolve => setTimeout(resolve, 5000));

    // 5. 测试只更新PendingMilk增量
    console.log('📋 测试5: 只更新PendingMilk增量');
    try {
      const response5 = await api.post('/api/wallet/batch-update-resources', {
        tempMilk: 10.000
      });
      console.log('✅ 只更新PendingMilk增量成功');
      console.log('📊 响应数据:', JSON.stringify(response5.data, null, 2));
      console.log('');
    } catch (error) {
      console.log('❌ 只更新PendingMilk增量失败:', error.response?.data || error.message);
      console.log('');
    }

    // 等待5秒避免防刷保护
    console.log('⏳ 等待5秒避免防刷保护...');
    await new Promise(resolve => setTimeout(resolve, 5000));

    // 6. 测试负GEM增量（应该失败）
    console.log('📋 测试6: 负GEM增量（应该失败）');
    try {
      const response6 = await api.post('/api/wallet/batch-update-resources', {
        tempGem: -2.000,    // 负GEM增量（不允许）
        tempMilk: 5.000
      });
      console.log('❌ 负GEM增量请求意外成功:', JSON.stringify(response6.data, null, 2));
      console.log('');
    } catch (error) {
      console.log('✅ 负GEM增量请求正确失败:', error.response?.data?.message || error.message);
      console.log('');
    }

    // 等待5秒避免防刷保护
    console.log('⏳ 等待5秒避免防刷保护...');
    await new Promise(resolve => setTimeout(resolve, 5000));

    // 7. 测试负牛奶增量（消耗牛奶，应该成功）
    console.log('📋 测试7: 负牛奶增量（消耗牛奶）');
    try {
      const response7 = await api.post('/api/wallet/batch-update-resources', {
        tempGem: 1.000,     // 正常GEM增量
        tempMilk: -3.000    // 消耗牛奶
      });
      console.log('✅ 负牛奶增量请求成功');
      console.log('📊 响应数据:', JSON.stringify(response7.data, null, 2));
      console.log('');
    } catch (error) {
      console.log('❌ 负牛奶增量请求失败:', error.response?.data || error.message);
      console.log('');
    }

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error.message);
  }

  console.log('🎯 测试完成！');
}

// 运行测试
testBatchUpdateIncrements();
