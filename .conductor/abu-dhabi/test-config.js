const { IapProduct } = require('./dist/models/IapProduct');
const { sequelize } = require('./dist/config/db');

async function testConfig() {
  try {
    await sequelize.authenticate();
    console.log('数据库连接成功');

    // 查询 VIP 会员产品
    const vipProduct = await IapProduct.findOne({
      where: { type: 'vip_membership' }
    });

    if (vipProduct) {
      console.log('\n=== VIP 会员产品 ===');
      console.log('产品ID:', vipProduct.productId);
      console.log('名称:', vipProduct.name);
      console.log('配置:', vipProduct.config);
      
      if (typeof vipProduct.config === 'string') {
        const config = JSON.parse(vipProduct.config);
        console.log('解析后的配置:', config);
        console.log('持续天数:', config.durationDays);
        console.log('出货线速度加成:', config.deliverySpeedBonus);
        console.log('出货线价格加成:', config.blockPriceBonus);
        console.log('牧场区生产速度加成:', config.productionSpeedBonus);
      }
    }

    // 查询特殊套餐产品
    const specialProduct = await IapProduct.findOne({
      where: { type: 'special_offer' }
    });

    if (specialProduct) {
      console.log('\n=== 特殊套餐产品 ===');
      console.log('产品ID:', specialProduct.productId);
      console.log('名称:', specialProduct.name);
      console.log('配置:', specialProduct.config);
      
      if (typeof specialProduct.config === 'string') {
        const config = JSON.parse(specialProduct.config);
        console.log('解析后的配置:', config);
        console.log('套餐内容:', config.bundle);
        
        if (config.bundle) {
          config.bundle.forEach((item, index) => {
            console.log(`道具${index + 1}:`, item.type, `x${item.quantity}`, item.autoUse ? '(自动使用)' : '(添加到背包)');
          });
        }
      }
    }

    await sequelize.close();
  } catch (error) {
    console.error('测试失败:', error);
  }
}

testConfig(); 