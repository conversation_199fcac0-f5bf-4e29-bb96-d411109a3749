const { Sequelize } = require('sequelize');
require('dotenv').config();

async function testDatabaseConnection() {
  console.log('🔧 测试数据库连接...');
  
  try {
    const env = process.env.NODE_ENV || 'development';
    const config = require('./config/config.js')[env];
    
    console.log('📋 数据库配置信息:');
    console.log(`   - 环境: ${env}`);
    console.log(`   - 主机: ${config.host}:${config.port}`);
    console.log(`   - 数据库: ${config.database}`);
    console.log(`   - 用户: ${config.username}`);
    console.log(`   - 方言: ${config.dialect}`);
    
    console.log('\n🔍 环境变量检查:');
    console.log(`   - DB_HOST: ${process.env.DB_HOST}`);
    console.log(`   - DB_PORT: ${process.env.DB_PORT}`);
    console.log(`   - DB_NAME: ${process.env.DB_NAME}`);
    console.log(`   - DB_USER: ${process.env.DB_USER}`);
    console.log(`   - DB_PASS: ${process.env.DB_PASS ? '***已设置***' : '未设置'}`);
    console.log(`   - DB_DIALECT: ${process.env.DB_DIALECT}`);
    
    let sequelize;
    if (config.use_env_variable) {
      sequelize = new Sequelize(process.env[config.use_env_variable], config);
    } else {
      sequelize = new Sequelize(config.database, config.username, config.password, config);
    }
    
    console.log('\n🔗 尝试连接数据库...');
    await sequelize.authenticate();
    console.log('✅ 数据库连接成功！');
    
    // 测试查询
    console.log('\n📊 测试基本查询...');
    const [results, metadata] = await sequelize.query('SELECT 1 as test');
    console.log('✅ 查询测试成功:', results);
    
    // 显示数据库信息
    console.log('\n🗄️  数据库信息:');
    const [dbInfo] = await sequelize.query('SELECT DATABASE() as current_db, VERSION() as version');
    console.log(`   - 当前数据库: ${dbInfo[0].current_db}`);
    console.log(`   - MySQL版本: ${dbInfo[0].version}`);
    
    // 显示现有表
    console.log('\n📋 现有数据表:');
    const [tables] = await sequelize.query('SHOW TABLES');
    if (tables.length > 0) {
      tables.forEach(table => {
        const tableName = Object.values(table)[0];
        console.log(`   - ${tableName}`);
      });
    } else {
      console.log('   - 暂无数据表');
    }
    
    await sequelize.close();
    console.log('\n🎉 数据库连接测试完成！');
    
  } catch (error) {
    console.error('❌ 数据库连接失败:', error.message);
    console.error('错误详情:', error);
    
    if (error.code === 'ECONNREFUSED') {
      console.log('\n💡 连接被拒绝，请检查：');
      console.log('   1. MySQL服务是否正在运行');
      console.log('   2. 主机和端口是否正确');
      console.log('   3. 防火墙设置');
    } else if (error.code === 'ER_ACCESS_DENIED_ERROR') {
      console.log('\n💡 访问被拒绝，请检查：');
      console.log('   1. 用户名和密码是否正确');
      console.log('   2. 用户是否有访问该数据库的权限');
    } else if (error.code === 'ER_BAD_DB_ERROR') {
      console.log('\n💡 数据库不存在，请检查：');
      console.log('   1. 数据库名称是否正确');
      console.log('   2. 数据库是否已创建');
    }
    
    process.exit(1);
  }
}

// 如果直接运行此文件，则执行测试
if (require.main === module) {
  testDatabaseConnection();
}

module.exports = { testDatabaseConnection };
