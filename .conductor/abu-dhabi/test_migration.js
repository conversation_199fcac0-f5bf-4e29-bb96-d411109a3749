const { Sequelize } = require('sequelize');
require('dotenv').config();

async function testMigration() {
  console.log('🔧 测试迁移文件...');
  
  try {
    const env = process.env.NODE_ENV || 'development';
    const config = require('./config/config.js')[env];
    
    let sequelize;
    if (config.use_env_variable) {
      sequelize = new Sequelize(process.env[config.use_env_variable], config);
    } else {
      sequelize = new Sequelize(config.database, config.username, config.password, config);
    }
    
    // 测试数据库连接
    console.log('🔗 测试数据库连接...');
    await sequelize.authenticate();
    console.log('✅ 数据库连接成功');
    
    // 获取 QueryInterface
    const queryInterface = sequelize.getQueryInterface();
    
    // 检查 iap_purchases 表是否存在
    const tables = await queryInterface.showAllTables();
    if (!tables.includes('iap_purchases')) {
      console.log('❌ iap_purchases 表不存在');
      return;
    }
    
    // 检查表结构
    console.log('\n📊 检查 iap_purchases 表结构...');
    const tableDescription = await queryInterface.describeTable('iap_purchases');
    
    console.log('表字段:');
    Object.keys(tableDescription).forEach(field => {
      const fieldInfo = tableDescription[field];
      console.log(`   - ${field}: ${fieldInfo.type} ${fieldInfo.allowNull ? 'NULL' : 'NOT NULL'} ${fieldInfo.defaultValue ? `DEFAULT ${fieldInfo.defaultValue}` : ''}`);
    });
    
    // 检查 statusChecked 字段
    if (tableDescription.statusChecked) {
      console.log('\n✅ statusChecked 字段已存在');
      console.log(`   类型: ${tableDescription.statusChecked.type}`);
      console.log(`   允许NULL: ${tableDescription.statusChecked.allowNull}`);
      console.log(`   默认值: ${tableDescription.statusChecked.defaultValue}`);
    } else {
      console.log('\n❌ statusChecked 字段不存在');
    }
    
    // 测试迁移文件
    console.log('\n🔄 测试迁移文件...');
    
    try {
      // 导入迁移文件
      const migration = require('./migrations/20250115000000-add-status-checked-to-iap-purchases.js');
      
      console.log('📝 执行迁移 up 方法...');
      await migration.up(queryInterface, Sequelize);
      console.log('✅ 迁移 up 方法执行成功');
      
    } catch (error) {
      console.error('❌ 迁移执行失败:', error.message);
    }
    
    await sequelize.close();
    console.log('\n✅ 测试完成，数据库连接已关闭');
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    console.error('错误详情:', error.stack);
    process.exit(1);
  }
}

// 如果直接运行此文件，则执行测试
if (require.main === module) {
  testMigration();
}

module.exports = { testMigration };
