#!/usr/bin/env node

/**
 * 迁移脚本测试工具
 * 用于验证迁移脚本的基本功能是否正常
 */

const fs = require('fs');
const path = require('path');

// 颜色输出
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
};

const log = {
  success: (msg) => console.log(`${colors.green}✓${colors.reset} ${msg}`),
  error: (msg) => console.log(`${colors.red}✗${colors.reset} ${msg}`),
  info: (msg) => console.log(`${colors.blue}ℹ${colors.reset} ${msg}`),
  warning: (msg) => console.log(`${colors.yellow}⚠${colors.reset} ${msg}`),
};

// 测试项目
const tests = [];

// 测试函数
function test(name, fn) {
  tests.push({ name, fn });
}

// 运行所有测试
async function runTests() {
  console.log('\n📋 开始测试迁移脚本...\n');
  
  let passed = 0;
  let failed = 0;
  
  for (const { name, fn } of tests) {
    try {
      await fn();
      log.success(name);
      passed++;
    } catch (error) {
      log.error(`${name}: ${error.message}`);
      failed++;
    }
  }
  
  console.log('\n' + '='.repeat(50));
  console.log(`测试完成: ${colors.green}${passed} 通过${colors.reset}, ${colors.red}${failed} 失败${colors.reset}`);
  console.log('='.repeat(50) + '\n');
  
  if (failed > 0) {
    process.exit(1);
  }
}

// 测试1: 检查迁移脚本文件是否存在
test('迁移脚本文件存在', () => {
  const scriptPath = path.join(__dirname, 'run_local_migrations.js');
  if (!fs.existsSync(scriptPath)) {
    throw new Error('run_local_migrations.js 不存在');
  }
});

// 测试2: 检查迁移目录是否存在
test('迁移目录存在', () => {
  const migrationsDir = path.join(__dirname, 'migrations');
  if (!fs.existsSync(migrationsDir)) {
    throw new Error('migrations 目录不存在');
  }
});

// 测试3: 检查是否有迁移文件
test('存在迁移文件', () => {
  const migrationsDir = path.join(__dirname, 'migrations');
  const files = fs.readdirSync(migrationsDir).filter(f => f.endsWith('.js'));
  if (files.length === 0) {
    throw new Error('没有找到任何迁移文件');
  }
  log.info(`  找到 ${files.length} 个迁移文件`);
});

// 测试4: 检查环境配置示例文件
test('环境配置示例文件存在', () => {
  const envExamplePath = path.join(__dirname, '.env.local.example');
  if (!fs.existsSync(envExamplePath)) {
    throw new Error('.env.local.example 不存在');
  }
});

// 测试5: 检查 shell 脚本
test('Shell 脚本存在且可执行', () => {
  const scriptPath = path.join(__dirname, 'migrate.sh');
  if (!fs.existsSync(scriptPath)) {
    throw new Error('migrate.sh 不存在');
  }
  
  const stats = fs.statSync(scriptPath);
  // 检查是否有执行权限 (owner execute bit)
  if (!(stats.mode & 0o100)) {
    log.warning('  migrate.sh 没有执行权限，请运行: chmod +x migrate.sh');
  }
});

// 测试6: 检查文档文件
test('文档文件完整', () => {
  const docs = [
    'MIGRATION_LOCAL_GUIDE.md',
    'MIGRATION_QUICK_START.md'
  ];
  
  for (const doc of docs) {
    const docPath = path.join(__dirname, doc);
    if (!fs.existsSync(docPath)) {
      throw new Error(`文档 ${doc} 不存在`);
    }
  }
});

// 测试7: 验证迁移脚本语法
test('迁移脚本语法正确', async () => {
  try {
    require('./run_local_migrations.js');
  } catch (error) {
    // 忽略数据库连接错误，只检查语法错误
    if (!error.message.includes('Access denied') && 
        !error.message.includes('ECONNREFUSED') &&
        !error.message.includes('connect')) {
      throw new Error(`语法错误: ${error.message}`);
    }
  }
});

// 测试8: 检查 package.json 中的脚本
test('package.json 包含迁移脚本', () => {
  const packagePath = path.join(__dirname, 'package.json');
  const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
  
  const requiredScripts = [
    'migrate:local',
    'migrate:local:status',
    'migrate:local:rollback'
  ];
  
  for (const script of requiredScripts) {
    if (!packageJson.scripts[script]) {
      throw new Error(`package.json 缺少脚本: ${script}`);
    }
  }
});

// 测试9: 检查迁移文件格式
test('迁移文件格式正确', () => {
  const migrationsDir = path.join(__dirname, 'migrations');
  const files = fs.readdirSync(migrationsDir).filter(f => f.endsWith('.js'));
  
  let validCount = 0;
  for (const file of files.slice(0, 3)) { // 只检查前3个文件
    const migrationPath = path.join(migrationsDir, file);
    try {
      const migration = require(migrationPath);
      if (typeof migration.up !== 'function') {
        log.warning(`  ${file} 缺少 up 方法`);
      } else {
        validCount++;
      }
    } catch (error) {
      log.warning(`  无法加载 ${file}: ${error.message}`);
    }
  }
  
  if (validCount === 0 && files.length > 0) {
    throw new Error('没有找到有效的迁移文件');
  }
});

// 测试10: 检查环境变量
test('环境变量配置检查', () => {
  if (fs.existsSync('.env')) {
    log.info('  .env 文件存在');
    
    // 检查是否配置了基本的数据库变量
    require('dotenv').config();
    const requiredVars = ['DB_HOST', 'DB_NAME', 'DB_USER'];
    const missingVars = requiredVars.filter(v => !process.env[v]);
    
    if (missingVars.length > 0) {
      log.warning(`  缺少环境变量: ${missingVars.join(', ')}`);
    }
  } else {
    log.warning('  .env 文件不存在，使用默认配置');
  }
});

// 运行测试
runTests().catch(console.error);