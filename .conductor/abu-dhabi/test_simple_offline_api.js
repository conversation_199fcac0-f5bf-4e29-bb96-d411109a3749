// 简单测试离线奖励API
const axios = require('axios');

const BASE_URL = 'http://localhost:3456';
const JWT_TOKEN = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************.NJ3RM_PzHkmU5BPkqmSTPweMnjqhegFqeCko6lyH2Fg';

const config = {
  headers: {
    'Authorization': `Bearer ${JWT_TOKEN}`,
    'Content-Type': 'application/json'
  }
};

async function testSimpleOfflineAPI() {
  console.log('🧪 简单测试离线奖励API...');
  
  try {
    console.log('📋 测试获取离线奖励状态...');
    const response = await axios.get(`${BASE_URL}/api/wallet/offline-reward`, config);
    
    console.log('✅ API响应成功');
    console.log('响应状态:', response.status);
    console.log('响应数据:', JSON.stringify(response.data, null, 2));
    
  } catch (error) {
    console.error('❌ 测试失败:');
    if (error.response) {
      console.error('状态码:', error.response.status);
      console.error('响应数据:', error.response.data);
    } else {
      console.error('错误信息:', error.message);
    }
  }
}

testSimpleOfflineAPI();
