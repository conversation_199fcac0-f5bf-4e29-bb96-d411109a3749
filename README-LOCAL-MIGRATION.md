# 本地数据库迁移脚本

本项目提供了一套完整的本地数据库迁移解决方案，支持读取本地环境配置并执行 Sequelize 迁移。

## 🚀 快速开始

### 最简单的使用方式

```bash
# 执行 Kaia 环境迁移
./scripts/quick-migrate.sh kaia up

# 查看迁移状态
./scripts/quick-migrate.sh kaia status

# 或者使用 npm 脚本
npm run migrate:local:kaia
npm run migrate:local:kaia:status
```

## 📁 脚本文件概览

### 核心迁移脚本

| 脚本文件 | 用途 | 推荐场景 |
|---------|------|----------|
| `scripts/quick-migrate.sh` | 快速迁移脚本 | 日常使用，简单快捷 |
| `scripts/local-migration-runner.js` | 功能完整的 Node.js 脚本 | 需要更多控制选项时 |
| `scripts/migration-manager.js` | 高级迁移管理脚本 | 生产环境，需要备份功能 |

### 辅助工具

| 脚本文件 | 用途 |
|---------|------|
| `scripts/test-migration-scripts.js` | 测试所有迁移脚本是否正常工作 |
| `scripts/migration-examples.sh` | 交互式示例演示脚本 |
| `docs/LOCAL_MIGRATION_GUIDE.md` | 详细使用指南 |

## 🛠️ 支持的环境

- **kaia**: 使用 `.env.local.kaia` 配置文件
- **pharos**: 使用 `.env.local.pharos` 配置文件

## 📋 常用命令

### 快速迁移脚本

```bash
# 基本用法
./scripts/quick-migrate.sh [环境] [操作]

# 示例
./scripts/quick-migrate.sh kaia up      # 执行向上迁移
./scripts/quick-migrate.sh kaia status  # 查看状态
./scripts/quick-migrate.sh kaia down    # 回滚迁移
./scripts/quick-migrate.sh pharos up    # Pharos 环境迁移
```

### Node.js 迁移脚本

```bash
# 基本用法
node scripts/local-migration-runner.js [选项]

# 示例
node scripts/local-migration-runner.js --env kaia --status
node scripts/local-migration-runner.js --env kaia --dry-run
node scripts/local-migration-runner.js --env pharos --up
node scripts/local-migration-runner.js --help
```

### 高级迁移管理脚本

```bash
# 基本用法
node scripts/migration-manager.js <操作> [选项]

# 示例
node scripts/migration-manager.js status --env kaia
node scripts/migration-manager.js up --env kaia
node scripts/migration-manager.js down --env kaia --no-backup
node scripts/migration-manager.js reset --env kaia
```

### NPM 脚本快捷方式

```bash
# 迁移操作
npm run migrate:local:kaia           # Kaia 环境迁移
npm run migrate:local:pharos         # Pharos 环境迁移

# 状态查询
npm run migrate:local:kaia:status    # Kaia 状态
npm run migrate:local:pharos:status  # Pharos 状态

# 回滚操作
npm run migrate:local:kaia:down      # Kaia 回滚
npm run migrate:local:pharos:down    # Pharos 回滚
```

## 🧪 测试和验证

```bash
# 测试所有迁移脚本
node scripts/test-migration-scripts.js

# 交互式示例演示
./scripts/migration-examples.sh
```

## 🔧 功能特性

### 快速迁移脚本 (quick-migrate.sh)
- ✅ 简单易用，适合日常操作
- ✅ 自动检查环境和依赖
- ✅ 彩色输出，易于阅读
- ✅ 数据库连接测试

### Node.js 迁移脚本 (local-migration-runner.js)
- ✅ 功能完整，支持更多选项
- ✅ 详细的日志输出
- ✅ 支持干运行模式
- ✅ 支持迁移到指定版本

### 高级迁移管理脚本 (migration-manager.js)
- ✅ 支持自动备份
- ✅ 迁移状态详细显示
- ✅ 支持批量操作
- ✅ 备份文件管理
- ✅ 安全检查

## 📖 详细文档

查看 [docs/LOCAL_MIGRATION_GUIDE.md](docs/LOCAL_MIGRATION_GUIDE.md) 获取详细的使用指南，包括：

- 详细的参数说明
- 故障排除指南
- 最佳实践建议
- 备份和恢复操作

## ⚠️ 注意事项

1. **环境要求**：
   - Node.js >= 22.0.0
   - MySQL 数据库服务正在运行
   - 相应的环境配置文件存在

2. **安全提醒**：
   - 重要操作前建议先使用 `--dry-run` 模式预览
   - 生产环境操作建议使用带备份功能的高级脚本
   - 定期检查迁移状态

3. **权限设置**：
   ```bash
   # 如果脚本没有执行权限，请添加：
   chmod +x scripts/quick-migrate.sh
   chmod +x scripts/migration-examples.sh
   ```

## 🆘 故障排除

### 常见问题

1. **数据库连接失败**
   ```bash
   # 检查 Docker 容器状态
   docker ps
   
   # 启动数据库容器
   npm run docker:start
   ```

2. **环境文件不存在**
   ```bash
   # 检查环境文件
   ls -la .env.local.*
   
   # 从示例文件复制
   cp .env.example .env.local.kaia
   ```

3. **权限问题**
   ```bash
   # 添加执行权限
   chmod +x scripts/quick-migrate.sh
   ```

## 🎯 使用建议

- **日常开发**：使用 `quick-migrate.sh` 或 npm 脚本
- **调试测试**：使用 `local-migration-runner.js` 的干运行模式
- **重要操作**：使用 `migration-manager.js` 的自动备份功能
- **学习了解**：运行 `migration-examples.sh` 查看交互式示例

## 📝 更新日志

- **v1.0.0** (2025-08-04)
  - 初始版本发布
  - 支持 Kaia 和 Pharos 环境
  - 提供三种不同复杂度的迁移脚本
  - 包含完整的测试和文档

---

**作者**: Augment Agent  
**创建时间**: 2025-08-04  
**项目**: MooFun Kaia 本地迁移工具
