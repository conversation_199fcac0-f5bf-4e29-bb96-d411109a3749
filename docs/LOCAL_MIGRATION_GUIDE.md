# 本地数据库迁移指南

本指南介绍如何在本地环境中执行数据库迁移操作。

## 概述

项目提供了三个迁移脚本，适用于不同的使用场景：

1. **quick-migrate.sh** - 快速迁移脚本（推荐日常使用）
2. **local-migration-runner.js** - 功能完整的 Node.js 迁移脚本
3. **migration-manager.js** - 高级迁移管理脚本（支持备份等高级功能）

## 环境配置

项目支持两个本地环境：

- **kaia**: 使用 `.env.local.kaia` 配置文件
- **pharos**: 使用 `.env.local.pharos` 配置文件

## 快速开始

### 1. 使用快速迁移脚本（推荐）

```bash
# 执行 kaia 环境的迁移
./scripts/quick-migrate.sh kaia up

# 执行 pharos 环境的迁移
./scripts/quick-migrate.sh pharos up

# 查看迁移状态
./scripts/quick-migrate.sh kaia status

# 回滚最后一次迁移
./scripts/quick-migrate.sh kaia down
```

### 2. 使用 Node.js 迁移脚本

```bash
# 执行迁移
node scripts/local-migration-runner.js --env kaia --up

# 查看状态
node scripts/local-migration-runner.js --env kaia --status

# 干运行模式
node scripts/local-migration-runner.js --env kaia --dry-run

# 回滚迁移
node scripts/local-migration-runner.js --env kaia --down
```

### 3. 使用高级迁移管理脚本

```bash
# 查看迁移状态
node scripts/migration-manager.js status --env kaia

# 执行迁移（自动备份）
node scripts/migration-manager.js up --env kaia

# 回滚迁移
node scripts/migration-manager.js down --env kaia

# 重置所有迁移
node scripts/migration-manager.js reset --env kaia
```

## 详细使用说明

### 快速迁移脚本 (quick-migrate.sh)

**特点：**
- 简单易用，适合日常操作
- 自动检查环境和依赖
- 彩色输出，易于阅读

**使用方法：**
```bash
./scripts/quick-migrate.sh [环境] [操作]
```

**参数说明：**
- 环境: `kaia` 或 `pharos`
- 操作: `up`（向上迁移）、`down`（向下迁移）、`status`（查看状态）、`undo`（撤销）

**示例：**
```bash
# 执行 kaia 环境的向上迁移
./scripts/quick-migrate.sh kaia up

# 查看 pharos 环境的迁移状态
./scripts/quick-migrate.sh pharos status

# 撤销 kaia 环境的最后一次迁移
./scripts/quick-migrate.sh kaia undo
```

### Node.js 迁移脚本 (local-migration-runner.js)

**特点：**
- 功能完整，支持更多选项
- 详细的日志输出
- 支持干运行模式

**使用方法：**
```bash
node scripts/local-migration-runner.js [选项]
```

**选项说明：**
- `--env <kaia|pharos>`: 指定环境（默认：kaia）
- `--dry-run`: 干运行模式，只显示将要执行的操作
- `--up`: 执行向上迁移（默认）
- `--down`: 执行向下迁移
- `--to <migration>`: 迁移到指定版本
- `--status`: 显示迁移状态
- `--help`: 显示帮助信息

**示例：**
```bash
# 执行 kaia 环境的迁移
node scripts/local-migration-runner.js --env kaia

# 干运行模式查看将要执行的操作
node scripts/local-migration-runner.js --env kaia --dry-run

# 迁移到指定版本
node scripts/local-migration-runner.js --env kaia --to 20250804000000

# 查看帮助
node scripts/local-migration-runner.js --help
```

### 高级迁移管理脚本 (migration-manager.js)

**特点：**
- 支持自动备份
- 迁移状态详细显示
- 支持批量操作
- 备份文件管理

**使用方法：**
```bash
node scripts/migration-manager.js <action> [选项]
```

**操作说明：**
- `status`: 显示迁移状态
- `up`: 执行向上迁移
- `down`: 执行向下迁移
- `reset`: 重置所有迁移

**选项说明：**
- `--env <kaia|pharos>`: 指定环境（默认：kaia）
- `--to <migration>`: 迁移到指定版本
- `--dry-run`: 干运行模式
- `--no-backup`: 不创建备份

**示例：**
```bash
# 查看详细的迁移状态
node scripts/migration-manager.js status --env kaia

# 执行迁移并自动备份
node scripts/migration-manager.js up --env kaia

# 不创建备份的迁移
node scripts/migration-manager.js up --env kaia --no-backup

# 重置所有迁移（危险操作）
node scripts/migration-manager.js reset --env kaia
```

## 常见问题

### 1. 数据库连接失败

**问题：** 脚本报告数据库连接失败

**解决方案：**
1. 检查 Docker 容器是否正在运行
2. 验证环境配置文件中的数据库连接信息
3. 确认数据库服务端口是否正确

```bash
# 检查 Docker 容器状态
docker ps

# 启动数据库容器
npm run docker:start
```

### 2. 环境文件不存在

**问题：** 脚本报告环境文件不存在

**解决方案：**
1. 确认 `.env.local.kaia` 或 `.env.local.pharos` 文件存在
2. 检查文件路径是否正确
3. 从示例文件复制配置

```bash
# 检查环境文件
ls -la .env.local.*

# 从示例文件复制（如果需要）
cp .env.example .env.local.kaia
```

### 3. 权限问题

**问题：** Shell 脚本没有执行权限

**解决方案：**
```bash
# 添加执行权限
chmod +x scripts/quick-migrate.sh
```

### 4. Sequelize CLI 未安装

**问题：** 找不到 sequelize-cli 命令

**解决方案：**
```bash
# 安装 sequelize-cli
npm install sequelize-cli

# 或者全局安装
npm install -g sequelize-cli
```

## 最佳实践

### 1. 迁移前的准备

- 确保数据库服务正在运行
- 检查当前迁移状态
- 重要数据建议先备份

### 2. 选择合适的脚本

- **日常使用**: 推荐使用 `quick-migrate.sh`
- **调试和测试**: 使用 `local-migration-runner.js` 的干运行模式
- **生产环境或重要操作**: 使用 `migration-manager.js` 的自动备份功能

### 3. 安全操作

- 重要操作前先使用 `--dry-run` 模式预览
- 定期检查迁移状态
- 保留数据库备份

### 4. 故障排除

- 查看详细的错误日志
- 检查数据库连接配置
- 验证迁移文件的语法

## 备份和恢复

### 自动备份

使用 `migration-manager.js` 时会自动创建备份：

```bash
# 执行迁移时自动备份
node scripts/migration-manager.js up --env kaia

# 备份文件位置
ls -la backups/migrations/
```

### 手动备份

```bash
# 手动创建备份
mysqldump -h 127.0.0.1 -P 3669 -u wolf -p wolf_kaia > backup_$(date +%Y%m%d_%H%M%S).sql
```

### 恢复备份

```bash
# 从备份恢复
mysql -h 127.0.0.1 -P 3669 -u wolf -p wolf_kaia < backup_20250804_120000.sql
```

## 总结

本地迁移脚本提供了灵活的数据库迁移管理方案：

- **快速操作**: 使用 `quick-migrate.sh`
- **详细控制**: 使用 `local-migration-runner.js`
- **高级功能**: 使用 `migration-manager.js`

选择适合您需求的脚本，并遵循最佳实践，确保数据库迁移的安全和可靠。
