# 管理员修改密码接口文档

## 接口概览

- **接口路径**: `/api/admin/auth/change-password`
- **请求方法**: `POST`
- **接口描述**: 管理员修改密码接口，用于更改当前登录管理员的密码
- **认证要求**: 需要有效的访问令牌（Access Token）
- **速率限制**: 5分钟内最多允许3次修改密码尝试
- **安全措施**: 需要通过安全确认中间件验证

## 请求参数

### 请求头

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| Authorization | string | 是 | Bearer <access_token> |
| Content-Type | string | 是 | application/json |
| Accept-Language | string | 否 | 语言设置，支持 zh-CN、en、ja |

### 请求体

```json
{
  "oldPassword": "current_password",
  "newPassword": "new_password123"
}
```

| 参数名 | 类型 | 必填 | 说明 | 限制 |
|--------|------|------|------|------|
| oldPassword | string | 是 | 当前密码 | - |
| newPassword | string | 是 | 新密码 | 最少6个字符 |

## 响应参数

### 成功响应 (200 OK)

```json
{
  "success": true,
  "data": {},
  "message": "密码修改成功，请重新登录"
}
```

### 响应字段说明

| 字段路径 | 类型 | 说明 |
|----------|------|------|
| success | boolean | 请求是否成功 |
| data | object | 响应数据（空对象） |
| message | string | 响应消息 |

## 错误响应

### 400 Bad Request - 参数错误

缺少参数时：
```json
{
  "success": false,
  "message": "旧密码和新密码不能为空",
  "data": {}
}
```

新密码过短时：
```json
{
  "success": false,
  "message": "新密码长度不能少于6位",
  "data": {}
}
```

旧密码错误时：
```json
{
  "success": false,
  "message": "旧密码错误",
  "data": {}
}
```

### 401 Unauthorized - 未认证

未提供Token或Token无效：
```json
{
  "success": false,
  "message": "未登录或登录已过期",
  "data": {}
}
```

### 403 Forbidden - 权限不足

需要二次验证时（如果启用了安全确认）：
```json
{
  "success": false,
  "message": "需要安全验证",
  "data": {}
}
```

### 404 Not Found - 管理员不存在

```json
{
  "success": false,
  "message": "管理员不存在",
  "data": {}
}
```

### 429 Too Many Requests - 速率限制

```json
{
  "success": false,
  "message": "请求过于频繁，请稍后再试",
  "data": {}
}
```

### 500 Internal Server Error - 服务器错误

```json
{
  "success": false,
  "message": "修改密码失败",
  "data": {}
}
```

## 业务逻辑说明

### 修改密码流程

1. **身份验证**: 验证请求中的访问令牌是否有效
2. **参数验证**: 
   - 检查旧密码和新密码是否为空
   - 验证新密码长度是否至少6个字符
3. **管理员查询**: 根据Token中的adminId查询管理员信息
4. **旧密码验证**: 使用bcrypt验证旧密码是否正确
5. **密码更新**: 
   - 使用bcrypt加密新密码（saltRounds=12）
   - 更新数据库中的密码hash
6. **会话处理**: 
   - 销毁该管理员的所有活跃会话
   - 强制用户重新登录以确保安全
7. **日志记录**: 记录密码修改操作到审计日志

### 密码策略

| 策略项 | 要求 | 说明 |
|--------|------|------|
| 最小长度 | 6个字符 | 新密码必须至少包含6个字符 |
| 加密方式 | bcrypt | 使用bcrypt算法加密，salt rounds为12 |
| 历史记录 | 不检查 | 不验证新密码是否与历史密码重复 |
| 复杂度 | 无特殊要求 | 不强制要求大小写、数字或特殊字符 |
| 有效期 | 无限制 | 密码不会自动过期 |

### 安全特性

1. **二次验证**: 通过securityConfirmationMiddleware进行额外的安全确认
2. **速率限制**: 5分钟内最多3次修改尝试，防止暴力破解
3. **会话清理**: 修改密码后立即销毁所有会话，防止被盗用的Token继续访问
4. **密码加密**: 使用高强度的bcrypt算法（12轮salt）
5. **审计日志**: 所有密码修改操作都会记录到日志系统
6. **Token验证**: 需要有效的访问令牌才能修改密码

### 密码修改后的影响

1. **会话失效**: 所有该管理员的活跃会话将被立即销毁
2. **Token失效**: 所有已签发的访问令牌和刷新令牌将失效
3. **重新登录**: 用户需要使用新密码重新登录
4. **其他设备**: 在其他设备或浏览器上的登录状态也会失效

## 使用示例

### cURL示例

```bash
# 先获取访问令牌
ACCESS_TOKEN="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."

# 修改密码
curl -X POST https://api.example.com/api/admin/auth/change-password \
  -H "Authorization: Bearer $ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -H "Accept-Language: zh-CN" \
  -d '{
    "oldPassword": "old_password123",
    "newPassword": "new_password456"
  }'
```

### JavaScript示例

```javascript
const changePassword = async (oldPassword, newPassword) => {
  const accessToken = localStorage.getItem('accessToken');
  
  if (!accessToken) {
    console.error('未登录');
    return false;
  }
  
  try {
    const response = await fetch('https://api.example.com/api/admin/auth/change-password', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json',
        'Accept-Language': 'zh-CN'
      },
      body: JSON.stringify({
        oldPassword,
        newPassword
      })
    });

    const data = await response.json();
    
    if (data.success) {
      console.log('密码修改成功，需要重新登录');
      
      // 清除本地存储的Token
      localStorage.removeItem('accessToken');
      localStorage.removeItem('refreshToken');
      
      // 跳转到登录页面
      window.location.href = '/admin/login';
      
      return true;
    } else {
      console.error('密码修改失败:', data.message);
      
      // 处理特定错误
      if (response.status === 401) {
        // Token过期，跳转到登录页
        window.location.href = '/admin/login';
      } else if (response.status === 400) {
        // 参数错误，显示错误信息
        alert(data.message);
      }
      
      return false;
    }
  } catch (error) {
    console.error('请求失败:', error);
    return false;
  }
};

// 使用示例
changePassword('old_password123', 'new_password456');
```

### Python示例

```python
import requests
import json

class AdminPasswordManager:
    def __init__(self, base_url, access_token):
        self.base_url = base_url
        self.access_token = access_token
    
    def change_password(self, old_password, new_password):
        """
        修改管理员密码
        
        Args:
            old_password: 当前密码
            new_password: 新密码
            
        Returns:
            tuple: (success: bool, message: str)
        """
        url = f"{self.base_url}/api/admin/auth/change-password"
        
        headers = {
            "Authorization": f"Bearer {self.access_token}",
            "Content-Type": "application/json",
            "Accept-Language": "zh-CN"
        }
        
        payload = {
            "oldPassword": old_password,
            "newPassword": new_password
        }
        
        try:
            response = requests.post(url, headers=headers, json=payload)
            data = response.json()
            
            if data["success"]:
                print(f"密码修改成功: {data['message']}")
                return True, data["message"]
            else:
                print(f"密码修改失败: {data['message']}")
                
                # 处理特定错误
                if response.status_code == 401:
                    print("认证失败，需要重新登录")
                elif response.status_code == 400:
                    print(f"参数错误: {data['message']}")
                elif response.status_code == 429:
                    print("请求过于频繁，请稍后再试")
                
                return False, data["message"]
                
        except requests.exceptions.RequestException as e:
            print(f"请求失败: {e}")
            return False, str(e)
    
    def validate_password(self, password):
        """
        本地验证密码策略
        
        Args:
            password: 待验证的密码
            
        Returns:
            tuple: (valid: bool, message: str)
        """
        if not password:
            return False, "密码不能为空"
        
        if len(password) < 6:
            return False, "密码长度不能少于6位"
        
        return True, "密码符合要求"

# 使用示例
if __name__ == "__main__":
    # 假设已经获取了访问令牌
    access_token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
    base_url = "https://api.example.com"
    
    # 创建密码管理器实例
    password_manager = AdminPasswordManager(base_url, access_token)
    
    # 验证新密码
    new_password = "new_secure_password123"
    valid, message = password_manager.validate_password(new_password)
    
    if valid:
        # 修改密码
        success, msg = password_manager.change_password(
            "old_password123",
            new_password
        )
        
        if success:
            print("密码修改成功，请重新登录")
            # 清理本地Token缓存
            # clear_token_cache()
    else:
        print(f"新密码不符合要求: {message}")
```

### React组件示例

```jsx
import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { message } from 'antd';

const ChangePasswordForm = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    oldPassword: '',
    newPassword: '',
    confirmPassword: ''
  });

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    // 本地验证
    if (!formData.oldPassword || !formData.newPassword) {
      message.error('请填写所有必填字段');
      return;
    }
    
    if (formData.newPassword.length < 6) {
      message.error('新密码长度不能少于6位');
      return;
    }
    
    if (formData.newPassword !== formData.confirmPassword) {
      message.error('两次输入的新密码不一致');
      return;
    }
    
    setLoading(true);
    
    try {
      const response = await fetch('/api/admin/auth/change-password', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('accessToken')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          oldPassword: formData.oldPassword,
          newPassword: formData.newPassword
        })
      });
      
      const data = await response.json();
      
      if (data.success) {
        message.success('密码修改成功，3秒后跳转到登录页...');
        
        // 清除Token
        localStorage.removeItem('accessToken');
        localStorage.removeItem('refreshToken');
        
        // 延迟跳转
        setTimeout(() => {
          navigate('/admin/login');
        }, 3000);
      } else {
        message.error(data.message || '密码修改失败');
        
        if (response.status === 401) {
          // Token过期
          navigate('/admin/login');
        }
      }
    } catch (error) {
      console.error('修改密码失败:', error);
      message.error('网络错误，请稍后重试');
    } finally {
      setLoading(false);
    }
  };
  
  return (
    <form onSubmit={handleSubmit}>
      <div className="form-group">
        <label>当前密码</label>
        <input
          type="password"
          value={formData.oldPassword}
          onChange={(e) => setFormData({
            ...formData,
            oldPassword: e.target.value
          })}
          required
        />
      </div>
      
      <div className="form-group">
        <label>新密码</label>
        <input
          type="password"
          value={formData.newPassword}
          onChange={(e) => setFormData({
            ...formData,
            newPassword: e.target.value
          })}
          minLength={6}
          required
        />
        <small>密码长度至少6个字符</small>
      </div>
      
      <div className="form-group">
        <label>确认新密码</label>
        <input
          type="password"
          value={formData.confirmPassword}
          onChange={(e) => setFormData({
            ...formData,
            confirmPassword: e.target.value
          })}
          required
        />
      </div>
      
      <button type="submit" disabled={loading}>
        {loading ? '修改中...' : '修改密码'}
      </button>
    </form>
  );
};

export default ChangePasswordForm;
```

## 注意事项

1. **安全建议**：
   - 定期修改密码（建议每3个月）
   - 使用强密码（包含大小写字母、数字和特殊字符）
   - 不要使用容易猜测的密码（如生日、电话号码等）
   - 不要在多个系统使用相同的密码
   - 使用密码管理器存储密码

2. **客户端实现建议**：
   - 在前端进行密码强度检查
   - 实现确认密码输入，避免输入错误
   - 显示密码强度指示器
   - 提供"显示密码"选项
   - 修改成功后自动清理本地Token缓存
   - 实现友好的错误提示

3. **服务端配置**：
   - 生产环境应设置更严格的密码策略
   - 考虑实现密码历史记录检查
   - 可以添加密码复杂度要求
   - 考虑实现密码有效期策略
   - 记录所有密码修改操作的审计日志

4. **错误处理**：
   - 不要泄露具体的密码策略细节给潜在攻击者
   - 实现友好的用户提示
   - 记录失败的修改尝试
   - 实现防暴力破解机制

5. **性能优化**：
   - 使用适当的bcrypt rounds（12是推荐值）
   - 实现异步密码验证避免阻塞
   - 使用Redis缓存会话信息

## 相关接口

- [管理员登录](/api/admin/auth/login) - 使用新密码登录
- [管理员登出](/api/admin/auth/logout) - 退出当前会话
- [获取管理员信息](/api/admin/auth/profile) - 查看管理员信息
- [刷新Token](/api/admin/auth/refresh) - 刷新访问令牌
- [获取会话列表](/api/admin/auth/sessions) - 查看所有活跃会话

## 常见问题

### Q1: 忘记密码怎么办？
A: 需要联系超级管理员重置密码，或通过数据库直接更新密码hash。

### Q2: 为什么修改密码后需要重新登录？
A: 这是一项安全措施，确保所有使用旧密码生成的Token都失效，防止被盗用的Token继续访问系统。

### Q3: 可以查看密码修改历史吗？
A: 系统会记录密码修改的审计日志，但不会保存历史密码的具体内容。

### Q4: 修改密码有次数限制吗？
A: 有速率限制，5分钟内最多允许3次修改尝试，防止恶意操作。

### Q5: 新密码可以和旧密码相同吗？
A: 目前系统不检查新旧密码是否相同，但建议使用不同的密码以提高安全性。

## 更新历史

| 版本 | 日期 | 更新内容 |
|------|------|----------|
| v1.0.0 | 2025-08-04 | 初始版本发布 |
