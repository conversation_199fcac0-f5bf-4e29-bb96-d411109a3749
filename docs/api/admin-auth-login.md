# 管理员登录接口文档

## 接口概览

- **接口路径**: `/api/admin/auth/login`
- **请求方法**: `POST`
- **接口描述**: 管理员登录接口，用于验证管理员身份并获取访问令牌
- **速率限制**: 5分钟内最多允许5次登录尝试

## 请求参数

### 请求头

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| Content-Type | string | 是 | application/json |
| Accept-Language | string | 否 | 语言设置，支持 zh-CN、en、ja |

### 请求体

```json
{
  "username": "admin",
  "password": "password123"
}
```

| 参数名 | 类型 | 必填 | 说明 | 示例 |
|--------|------|------|------|------|
| username | string | 是 | 管理员用户名 | admin |
| password | string | 是 | 管理员密码 | password123 |

## 响应参数

### 成功响应 (200 OK)

```json
{
  "success": true,
  "data": {
    "admin": {
      "id": 1,
      "username": "admin",
      "email": "<EMAIL>",
      "realName": "系统管理员",
      "role": "super_admin",
      "status": "active",
      "lastLoginAt": "2025-08-04T10:00:00.000Z",
      "createdAt": "2025-08-01T08:00:00.000Z"
    },
    "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expiresIn": "2h"
  },
  "message": "登录成功"
}
```

### 响应字段说明

| 字段路径 | 类型 | 说明 |
|----------|------|------|
| success | boolean | 请求是否成功 |
| data | object | 响应数据 |
| data.admin | object | 管理员信息 |
| data.admin.id | number | 管理员ID |
| data.admin.username | string | 用户名 |
| data.admin.email | string | 邮箱地址 |
| data.admin.realName | string | 真实姓名（可选） |
| data.admin.role | string | 角色权限，可选值：super_admin、admin、operator |
| data.admin.status | string | 账户状态，可选值：active、inactive、locked |
| data.admin.lastLoginAt | string | 最后登录时间（ISO 8601格式） |
| data.admin.createdAt | string | 账户创建时间（ISO 8601格式） |
| data.accessToken | string | JWT访问令牌，用于后续API调用 |
| data.refreshToken | string | JWT刷新令牌，用于刷新访问令牌 |
| data.expiresIn | string | 访问令牌有效期，默认2小时 |
| message | string | 响应消息 |

## 错误响应

### 400 Bad Request - 缺少参数

```json
{
  "success": false,
  "message": "用户名和密码不能为空",
  "data": {}
}
```

### 401 Unauthorized - 认证失败

```json
{
  "success": false,
  "message": "用户名或密码错误",
  "data": {}
}
```

### 403 Forbidden - 账户不可用

账户被禁用时：
```json
{
  "success": false,
  "message": "账户已被禁用",
  "data": {}
}
```

账户被锁定时（登录失败次数过多）：
```json
{
  "success": false,
  "message": "账户已被锁定，请在 2025-08-04 10:30:00 后重试",
  "data": {}
}
```

### 429 Too Many Requests - 速率限制

```json
{
  "success": false,
  "message": "请求过于频繁，请稍后再试",
  "data": {}
}
```

### 500 Internal Server Error - 服务器错误

```json
{
  "success": false,
  "message": "服务器内部错误",
  "data": {}
}
```

## 业务逻辑说明

### 登录流程

1. **参数验证**: 检查用户名和密码是否为空
2. **查找管理员**: 根据用户名查询管理员账户
3. **账户状态检查**: 
   - 检查账户是否被禁用（status = 'inactive'）
   - 检查账户是否被锁定（lockedUntil > 当前时间）
4. **密码验证**: 使用bcrypt验证密码是否正确
5. **失败处理**: 
   - 密码错误时，增加失败次数（loginFailCount）
   - 失败次数达到5次时，锁定账户30分钟
6. **成功处理**:
   - 重置失败次数
   - 限制并发会话数量（最多3个）
   - 生成访问令牌（2小时有效）和刷新令牌（7天有效）
   - 创建会话记录
   - 更新最后登录时间
   - 记录登录日志

### 角色权限说明

系统支持三种管理员角色：

| 角色 | 值 | 权限级别 | 说明 |
|------|-----|----------|------|
| 超级管理员 | super_admin | 3 | 拥有所有权限 |
| 管理员 | admin | 2 | 拥有大部分管理权限 |
| 操作员 | operator | 1 | 基础操作权限 |

### Token说明

#### Access Token（访问令牌）
- 有效期：2小时
- 用途：用于后续API请求的身份认证
- 使用方式：在请求头中添加 `Authorization: Bearer <access_token>`

#### Refresh Token（刷新令牌）
- 有效期：7天
- 用途：用于刷新访问令牌，避免频繁登录
- 使用接口：`/api/admin/auth/refresh`

#### JWT Token载荷结构

```typescript
{
  adminId: number;      // 管理员ID
  username: string;     // 用户名
  role: string;        // 角色
  tokenId: string;     // 会话标识（UUID）
  type: 'access' | 'refresh';  // Token类型
  iat: number;         // 签发时间
  exp: number;         // 过期时间
}
```

### 安全特性

1. **密码加密**: 使用bcrypt（saltRounds=12）加密存储
2. **速率限制**: 5分钟内最多5次登录尝试
3. **账户锁定**: 连续失败5次后锁定30分钟
4. **会话管理**: 
   - 限制每个用户最多3个并发会话
   - 超过限制时，自动销毁最早的会话
5. **IP记录**: 记录登录IP地址和User-Agent
6. **审计日志**: 所有登录操作都会记录到日志系统

## 使用示例

### cURL示例

```bash
curl -X POST https://api.example.com/api/admin/auth/login \
  -H "Content-Type: application/json" \
  -H "Accept-Language: zh-CN" \
  -d '{
    "username": "admin",
    "password": "password123"
  }'
```

### JavaScript示例

```javascript
const loginAdmin = async () => {
  try {
    const response = await fetch('https://api.example.com/api/admin/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept-Language': 'zh-CN'
      },
      body: JSON.stringify({
        username: 'admin',
        password: 'password123'
      })
    });

    const data = await response.json();
    
    if (data.success) {
      // 保存token
      localStorage.setItem('accessToken', data.data.accessToken);
      localStorage.setItem('refreshToken', data.data.refreshToken);
      
      console.log('登录成功', data.data.admin);
    } else {
      console.error('登录失败', data.message);
    }
  } catch (error) {
    console.error('请求失败', error);
  }
};
```

### Python示例

```python
import requests
import json

def admin_login(username, password):
    url = "https://api.example.com/api/admin/auth/login"
    
    headers = {
        "Content-Type": "application/json",
        "Accept-Language": "zh-CN"
    }
    
    payload = {
        "username": username,
        "password": password
    }
    
    try:
        response = requests.post(url, headers=headers, json=payload)
        data = response.json()
        
        if data["success"]:
            access_token = data["data"]["accessToken"]
            refresh_token = data["data"]["refreshToken"]
            admin_info = data["data"]["admin"]
            
            print(f"登录成功: {admin_info['username']}")
            return access_token, refresh_token
        else:
            print(f"登录失败: {data['message']}")
            return None, None
            
    except requests.exceptions.RequestException as e:
        print(f"请求失败: {e}")
        return None, None

# 使用示例
access_token, refresh_token = admin_login("admin", "password123")
```

## 注意事项

1. **生产环境配置**：
   - 必须设置环境变量 `JWT_SECRET_ADMIN`，不要使用默认值
   - 建议启用HTTPS确保传输安全
   - 配置反向代理时需要设置信任代理（trust proxy）

2. **客户端实现建议**：
   - 安全存储Token（避免存储在localStorage，推荐使用httpOnly cookie）
   - 实现Token自动刷新机制
   - 处理Token过期的重新登录流程
   - 实现请求重试机制

3. **错误处理**：
   - 不要向用户显示具体的错误信息（如"用户名不存在"），统一使用"用户名或密码错误"
   - 实现友好的错误提示
   - 记录错误日志用于排查问题

4. **性能优化**：
   - 实现Token缓存机制
   - 避免频繁登录请求
   - 使用连接池管理数据库连接

## 相关接口

- [刷新Token](/api/admin/auth/refresh) - 刷新访问令牌
- [管理员登出](/api/admin/auth/logout) - 退出登录
- [获取管理员信息](/api/admin/auth/profile) - 获取当前登录管理员信息
- [修改密码](/api/admin/auth/change-password) - 修改管理员密码
- [获取会话列表](/api/admin/auth/sessions) - 查看活跃会话

## 更新历史

| 版本 | 日期 | 更新内容 |
|------|------|----------|
| v1.0.0 | 2025-08-04 | 初始版本发布 |
