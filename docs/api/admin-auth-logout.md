# 管理员登出接口文档

## 接口概述

**接口名称**: 管理员登出  
**接口路径**: `/api/admin/auth/logout`  
**请求方法**: `POST`  
**接口描述**: 管理员登出系统，销毁当前会话，使当前访问令牌失效

## 认证要求

此接口需要管理员认证，必须在请求头中携带有效的访问令牌（Access Token）。

## 请求说明

### 请求头（Headers）

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| Authorization | string | 是 | Bearer Token，格式：`Bearer <access_token>` |
| Accept-Language | string | 否 | 语言设置，支持：zh-CN(中文)、en-US(英文)、ja-JP(日文)，默认为zh-CN |

### 请求参数（Body）

无需请求参数

### 请求示例

```bash
curl -X POST \
  https://api.example.com/api/admin/auth/logout \
  -H 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...' \
  -H 'Content-Type: application/json'
```

## 响应说明

### 成功响应

**状态码**: `200 OK`

**响应格式**:

```json
{
  "success": true,
  "data": {},
  "message": "登出成功"
}
```

### 响应字段说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| success | boolean | 操作是否成功，成功为true |
| data | object | 返回数据对象，登出接口返回空对象 |
| message | string | 操作结果消息 |

### 错误响应

#### 1. 未授权（未登录或Token无效）

**状态码**: `401 Unauthorized`

```json
{
  "success": false,
  "error": "未登录",
  "timestamp": "2025-08-04T14:33:00.000Z"
}
```

#### 2. 服务器内部错误

**状态码**: `500 Internal Server Error`

```json
{
  "success": false,
  "error": "登出失败",
  "timestamp": "2025-08-04T14:33:00.000Z"
}
```

## 业务逻辑说明

1. **会话销毁**: 接口会销毁当前的管理员会话记录
2. **Token失效**: 登出后，当前使用的Access Token立即失效
3. **日志记录**: 系统会记录登出操作日志，包含管理员ID、用户名、Token ID等信息
4. **其他会话不受影响**: 如果管理员在其他设备/浏览器有活跃会话，不会受到影响

## 注意事项

1. **Token立即失效**: 登出后，原Access Token将无法继续使用，需要重新登录获取新的Token
2. **无需刷新Token**: 登出操作不需要使用Refresh Token
3. **中间件保护**: 该接口受`adminAuthMiddleware`保护，会自动验证Token有效性
4. **错误处理**: 即使会话记录不存在，接口仍会返回成功，确保用户能正常登出

## 使用场景

1. **用户主动登出**: 管理员完成操作后主动退出系统
2. **切换账户**: 管理员需要切换到其他账户时先登出当前账户
3. **安全退出**: 在公共设备使用后确保账户安全

## 相关接口

- [管理员登录](/api/admin/auth/login) - 管理员登录获取Token
- [刷新Token](/api/admin/auth/refresh) - 刷新访问令牌
- [获取会话列表](/api/admin/auth/sessions) - 查看当前管理员的所有活跃会话

## 更新日志

| 版本 | 日期 | 更新内容 |
|------|------|----------|
| v1.0.0 | 2025-08-04 | 初始版本，实现基础登出功能 |

---

*最后更新时间: 2025年8月4日*
