# 获取当前管理员信息接口文档

## 接口概述

该接口用于获取当前登录管理员的个人信息。

## 接口信息

- **接口路径**: `/api/admin/auth/profile`
- **请求方法**: `GET`
- **接口说明**: 获取当前登录管理员的详细信息
- **认证要求**: 需要管理员认证（Bearer Token）

## 请求说明

### 请求头

| 参数名称 | 参数类型 | 是否必需 | 参数说明 |
|---------|---------|---------|---------|
| Authorization | string | 是 | Bearer Token，格式：`Bearer <access_token>` |
| Accept-Language | string | 否 | 语言设置，支持：`zh-CN`（中文）、`en`（英文）、`ja`（日文），默认：`zh-CN` |

### 请求示例

```bash
curl --request GET \
  --url 'http://localhost:3456/api/admin/auth/profile' \
  --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...' \
  --header 'Accept-Language: zh-CN'
```

## 响应说明

### 响应状态码

| 状态码 | 说明 |
|-------|------|
| 200 | 获取成功 |
| 401 | 未登录或Token无效 |
| 404 | 管理员不存在 |
| 500 | 服务器内部错误 |

### 成功响应

#### 响应格式

```json
{
  "success": true,
  "data": {
    "admin": {
      "id": 1,
      "username": "admin",
      "email": "<EMAIL>",
      "realName": "张三",
      "role": "super_admin",
      "status": "active",
      "lastLoginAt": "2025-08-04T10:30:00.000Z",
      "createdAt": "2025-08-01T08:00:00.000Z"
    }
  },
  "message": "获取管理员信息成功"
}
```

#### 响应字段说明

| 字段路径 | 类型 | 说明 |
|---------|------|------|
| success | boolean | 请求是否成功 |
| data | object | 响应数据 |
| data.admin | object | 管理员信息对象 |
| data.admin.id | number | 管理员ID |
| data.admin.username | string | 管理员用户名 |
| data.admin.email | string | 管理员邮箱 |
| data.admin.realName | string | 真实姓名（可选） |
| data.admin.role | string | 管理员角色，可选值见下方说明 |
| data.admin.status | string | 账户状态，可选值见下方说明 |
| data.admin.lastLoginAt | string | 最后登录时间（ISO 8601格式） |
| data.admin.createdAt | string | 账户创建时间（ISO 8601格式） |
| message | string | 响应消息 |

### 角色类型说明（role）

| 值 | 说明 | 权限说明 |
|----|------|---------|
| super_admin | 超级管理员 | 拥有系统所有权限 |
| admin | 管理员 | 拥有大部分管理权限 |
| operator | 操作员 | 仅拥有基础操作权限 |

### 账户状态说明（status）

| 值 | 说明 |
|----|------|
| active | 正常激活状态 |
| inactive | 未激活/禁用状态 |
| locked | 账户被锁定（登录失败次数过多或管理员锁定） |

### 失败响应

#### Token无效或未登录（401）

```json
{
  "success": false,
  "message": "未登录",
  "error": "未登录"
}
```

#### 管理员不存在（404）

```json
{
  "success": false,
  "message": "管理员不存在",
  "error": "管理员不存在"
}
```

#### 服务器错误（500）

```json
{
  "success": false,
  "message": "获取管理员信息失败",
  "error": "获取管理员信息失败"
}
```

## 注意事项

1. **认证要求**：该接口需要有效的管理员访问令牌（Access Token）
2. **Token获取**：通过 `/api/admin/auth/login` 接口登录获取
3. **Token刷新**：当Access Token过期时，可使用 `/api/admin/auth/refresh` 接口刷新
4. **敏感信息**：响应中不包含密码等敏感信息
5. **会话管理**：如果会话已过期或被销毁，需要重新登录

## 使用场景

1. **管理后台初始化**：在管理后台页面加载时获取当前管理员信息
2. **权限判断**：根据返回的角色信息判断管理员权限
3. **个人中心**：在个人中心页面展示管理员个人信息
4. **会话验证**：验证当前登录状态是否有效

## 相关接口

- [管理员登录](/api/admin/auth/login) - 获取访问令牌
- [刷新Token](/api/admin/auth/refresh) - 刷新访问令牌
- [修改密码](/api/admin/auth/change-password) - 修改管理员密码
- [获取会话列表](/api/admin/auth/sessions) - 查看当前管理员的所有活跃会话

## 更新历史

| 版本 | 日期 | 更新内容 |
|------|------|---------|
| v1.0.0 | 2025-08-04 | 初始版本发布 |
