# 用户行为统计系统实现文档

## 目录
1. [背景与问题分析](#背景与问题分析)
2. [系统设计概述](#系统设计概述)
3. [基于会话Cookie的启动次数统计](#基于会话cookie的启动次数统计)
4. [游玩时长统计方案](#游玩时长统计方案)
5. [具体实现步骤](#具体实现步骤)
6. [统计查询实现](#统计查询实现)
7. [部署与监控](#部署与监控)
8. [FAQ](#faq)

## 背景与问题分析

### 当前系统存在的问题

1. **启动次数统计不准确**
   ```typescript
   // 当前实现：统计的是日活跃用户数(DAU)，而非启动次数
   const activeUsers = await UserWallet.count({
     where: {
       lastActiveTime: {
         [Op.gte]: startDate,
         [Op.lt]: endDate,
       },
     },
   });
   ```
   - 问题：一个用户多次启动只计算一次
   - 影响：无法准确了解用户使用频率

2. **游玩时长完全未实现**
   ```typescript
   private static async getAveragePlayTime(): Promise<number> {
     // TODO: 实现实际的游玩时长统计
     return 30; // 硬编码返回30分钟
   }
   ```
   - 问题：返回固定值，数据完全虚假
   - 影响：无法了解用户真实使用情况

### 解决方案概述

采用**会话Cookie（Session Cookie）**机制：
- 利用浏览器原生行为：会话Cookie在浏览器关闭时自动清除
- 新Session = 新启动 = 准确的启动次数
- 通过Session活动记录估算游玩时长

## 系统设计概述

### 核心原理

1. **会话Cookie特性**
   - 不设置 `Expires` 或 `Max-Age`
   - 浏览器内存中保存，进程退出即清除
   - 符合 RFC 6265 标准

2. **统计逻辑**
   - 用户打开浏览器访问 → 无Cookie → 创建新Session → 记录一次启动
   - 用户继续使用 → 有Cookie → 使用现有Session → 不记录新启动
   - 用户关闭浏览器 → Cookie自动清除
   - 用户再次打开 → 重复第一步

### 系统架构

```
┌─────────────┐     ┌──────────────┐     ┌─────────────┐
│   Browser   │────▶│ Express App  │────▶│   Redis     │
│  (Cookie)   │     │ (Session MW) │     │ (Session)   │
└─────────────┘     └──────────────┘     └─────────────┘
                            │
                            ▼
                    ┌──────────────┐
                    │    MySQL     │
                    │ (Statistics) │
                    └──────────────┘
```

## 基于会话Cookie的启动次数统计

### 1. Session配置

```typescript
// src/config/sessionConfig.ts
import session from 'express-session';
import RedisStore from 'connect-redis';
import { redisClient } from './redisClient';

export const sessionConfig = {
  // Session存储配置
  store: new RedisStore({ 
    client: redisClient,
    prefix: 'sess:',
    ttl: 24 * 60 * 60, // Redis中保存24小时
    touch: 86400 // 更新TTL的间隔
  }),
  
  // Session基本配置
  name: 'moofun_sid', // Cookie名称
  secret: process.env.SESSION_SECRET || 'your-secret-key',
  resave: false, // 不强制保存未修改的session
  saveUninitialized: false, // 不保存未初始化的session
  rolling: true, // 每次请求都刷新cookie
  proxy: process.env.NODE_ENV === 'production', // 生产环境信任代理
  
  // Cookie配置（关键配置）
  cookie: {
    httpOnly: true, // 防止XSS攻击
    secure: process.env.NODE_ENV === 'production', // HTTPS only in production
    sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax', // 跨域配置
    // 重要：不设置 maxAge 或 expires
    // 这使其成为会话Cookie，浏览器关闭即消失
    maxAge: undefined,
    expires: undefined,
    domain: process.env.COOKIE_DOMAIN // 可选：设置cookie域名
  }
};

// 创建session中间件
export const sessionMiddleware = session(sessionConfig);
```

### 2. 会话追踪中间件

```typescript
// src/middlewares/sessionTracking.ts
import { Request, Response, NextFunction } from 'express';
import { AppLaunch } from '../models/AppLaunch';
import { UserWallet } from '../models/UserWallet';
import { logger } from '../utils/logger';

/**
 * 会话追踪中间件
 * 负责：
 * 1. 检测新会话并记录启动次数
 * 2. 更新用户活跃时间
 * 3. 记录会话活动（可选）
 */
export async function sessionTrackingMiddleware(
  req: Request,
  res: Response,
  next: NextFunction
) {
  try {
    // 只对已认证用户进行追踪
    if (!req.user || !req.user.walletId) {
      return next();
    }

    const walletId = req.user.walletId;
    const sessionId = req.sessionID;

    // 检查是否是新创建的session
    if (!req.session.initialized) {
      // 新session = 新的浏览器启动
      await recordNewLaunch(walletId, sessionId, req);
      
      // 标记session已初始化
      req.session.initialized = true;
      req.session.walletId = walletId;
      req.session.createdAt = new Date();
      req.session.launchCount = (req.session.launchCount || 0) + 1;
      
      logger.info('新会话创建', {
        walletId,
        sessionId,
        userAgent: req.headers['user-agent']
      });
    }

    // 更新session最后活跃时间
    req.session.lastActivityAt = new Date();
    req.session.requestCount = (req.session.requestCount || 0) + 1;

    // 更新用户的lastActiveTime（异步，不阻塞请求）
    updateUserLastActiveTime(walletId).catch(err => {
      logger.error('更新用户活跃时间失败', err);
    });

    next();
  } catch (error) {
    logger.error('会话追踪中间件错误', error);
    // 出错不影响正常请求
    next();
  }
}

/**
 * 记录应用启动
 */
async function recordNewLaunch(
  walletId: number, 
  sessionId: string,
  req: Request
) {
  const launchData = {
    walletId,
    sessionId,
    launchTime: new Date(),
    date: new Date().toISOString().split('T')[0], // YYYY-MM-DD格式
    userAgent: req.headers['user-agent'] || 'Unknown',
    ipAddress: req.ip || req.socket.remoteAddress || 'Unknown',
    referrer: req.headers.referer || 'Direct',
    // 可以添加更多信息
    deviceType: detectDeviceType(req.headers['user-agent']),
    browser: detectBrowser(req.headers['user-agent'])
  };

  await AppLaunch.create(launchData);
  
  logger.info('记录应用启动', {
    walletId,
    sessionId,
    deviceType: launchData.deviceType
  });
}

/**
 * 更新用户最后活跃时间
 */
async function updateUserLastActiveTime(walletId: number) {
  await UserWallet.update(
    { lastActiveTime: new Date() },
    { where: { id: walletId } }
  );
}

/**
 * 检测设备类型（简化版）
 */
function detectDeviceType(userAgent?: string): string {
  if (!userAgent) return 'Unknown';
  
  if (/mobile/i.test(userAgent)) return 'Mobile';
  if (/tablet/i.test(userAgent)) return 'Tablet';
  return 'Desktop';
}

/**
 * 检测浏览器类型（简化版）
 */
function detectBrowser(userAgent?: string): string {
  if (!userAgent) return 'Unknown';
  
  if (/chrome/i.test(userAgent)) return 'Chrome';
  if (/firefox/i.test(userAgent)) return 'Firefox';
  if (/safari/i.test(userAgent)) return 'Safari';
  if (/edge/i.test(userAgent)) return 'Edge';
  return 'Other';
}
```

### 3. 数据库表设计

```sql
-- 应用启动记录表
CREATE TABLE app_launches (
  id INT PRIMARY KEY AUTO_INCREMENT,
  walletId INT NOT NULL COMMENT '用户钱包ID',
  sessionId VARCHAR(128) NOT NULL COMMENT '会话ID',
  launchTime DATETIME NOT NULL COMMENT '启动时间',
  date DATE NOT NULL COMMENT '启动日期（方便按日统计）',
  userAgent VARCHAR(500) COMMENT '用户代理字符串',
  ipAddress VARCHAR(45) COMMENT 'IP地址',
  referrer VARCHAR(500) COMMENT '来源页面',
  deviceType VARCHAR(20) COMMENT '设备类型：Desktop/Mobile/Tablet',
  browser VARCHAR(20) COMMENT '浏览器类型',
  createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
  
  INDEX idx_wallet_date (walletId, date),
  INDEX idx_date (date),
  INDEX idx_session (sessionId),
  INDEX idx_launch_time (launchTime)
) COMMENT='应用启动记录表';
```

## 游玩时长统计方案

### 方案选择

提供两种方案，可根据精度需求选择：

1. **轻量级方案**：基于Session时间戳估算
2. **精确方案**：记录用户活动详情

### 1. 轻量级方案：Session时长记录

```typescript
// src/models/SessionDuration.ts
export interface SessionDurationAttributes {
  id?: number;
  sessionId: string;
  walletId: number;
  startTime: Date;
  lastActivityTime: Date;
  duration: number; // 秒
  pageViews: number;
  status: 'active' | 'ended';
  endReason?: 'timeout' | 'logout' | 'expire';
}

// 定期保存session时长
// src/jobs/saveSessionDurations.ts
import { redisClient } from '../config/redisClient';

export async function saveSessionDurations() {
  try {
    // 获取所有session keys
    const sessionKeys = await redisClient.keys('sess:*');
    
    for (const key of sessionKeys) {
      const sessionData = await redisClient.get(key);
      if (!sessionData) continue;
      
      const session = JSON.parse(sessionData);
      
      // 只处理已初始化的session
      if (session.initialized && session.walletId) {
        const duration = calculateDuration(
          session.createdAt,
          session.lastActivityAt
        );
        
        // 保存或更新时长记录
        await SessionDuration.upsert({
          sessionId: key.replace('sess:', ''),
          walletId: session.walletId,
          startTime: new Date(session.createdAt),
          lastActivityTime: new Date(session.lastActivityAt),
          duration,
          pageViews: session.requestCount || 0,
          status: 'active'
        });
      }
    }
    
    logger.info('Session时长保存完成', {
      processedCount: sessionKeys.length
    });
  } catch (error) {
    logger.error('保存Session时长失败', error);
  }
}

// 每5分钟执行一次
setInterval(saveSessionDurations, 5 * 60 * 1000);
```

### 2. 精确方案：活动记录（可选）

```sql
-- 用户活动记录表（可选，用于精确统计）
CREATE TABLE user_activities (
  id INT PRIMARY KEY AUTO_INCREMENT,
  sessionId VARCHAR(128) NOT NULL,
  walletId INT NOT NULL,
  activityTime DATETIME NOT NULL,
  activityType VARCHAR(50) COMMENT '活动类型：page_view, api_call, game_action等',
  endpoint VARCHAR(255) COMMENT '访问的端点',
  duration INT COMMENT '页面停留时间（秒）',
  metadata JSON COMMENT '额外信息',
  
  INDEX idx_session_time (sessionId, activityTime),
  INDEX idx_wallet_time (walletId, activityTime),
  INDEX idx_activity_date (DATE(activityTime))
) COMMENT='用户活动记录表';
```

## 具体实现步骤

### 1. 安装依赖

```bash
npm install express-session connect-redis redis
npm install --save-dev @types/express-session
```

### 2. 创建数据库迁移

```javascript
// migrations/YYYYMMDDHHMMSS-create-app-launches-table.js
'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('app_launches', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true
      },
      walletId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        comment: '用户钱包ID'
      },
      sessionId: {
        type: Sequelize.STRING(128),
        allowNull: false,
        comment: '会话ID'
      },
      launchTime: {
        type: Sequelize.DATE,
        allowNull: false,
        comment: '启动时间'
      },
      date: {
        type: Sequelize.DATEONLY,
        allowNull: false,
        comment: '启动日期'
      },
      userAgent: {
        type: Sequelize.STRING(500),
        comment: '用户代理'
      },
      ipAddress: {
        type: Sequelize.STRING(45),
        comment: 'IP地址'
      },
      referrer: {
        type: Sequelize.STRING(500),
        comment: '来源'
      },
      deviceType: {
        type: Sequelize.STRING(20),
        comment: '设备类型'
      },
      browser: {
        type: Sequelize.STRING(20),
        comment: '浏览器'
      },
      createdAt: {
        type: Sequelize.DATE,
        defaultValue: Sequelize.NOW
      }
    });

    // 添加索引
    await queryInterface.addIndex('app_launches', ['walletId', 'date']);
    await queryInterface.addIndex('app_launches', ['date']);
    await queryInterface.addIndex('app_launches', ['sessionId']);
    await queryInterface.addIndex('app_launches', ['launchTime']);
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('app_launches');
  }
};
```

### 3. 创建Sequelize模型

```typescript
// src/models/AppLaunch.ts
import { Model, DataTypes, Sequelize } from 'sequelize';

export interface AppLaunchAttributes {
  id?: number;
  walletId: number;
  sessionId: string;
  launchTime: Date;
  date: string;
  userAgent?: string;
  ipAddress?: string;
  referrer?: string;
  deviceType?: string;
  browser?: string;
  createdAt?: Date;
}

export class AppLaunch extends Model<AppLaunchAttributes> implements AppLaunchAttributes {
  public id!: number;
  public walletId!: number;
  public sessionId!: string;
  public launchTime!: Date;
  public date!: string;
  public userAgent?: string;
  public ipAddress?: string;
  public referrer?: string;
  public deviceType?: string;
  public browser?: string;
  public readonly createdAt!: Date;
}

export function initAppLaunch(sequelize: Sequelize): void {
  AppLaunch.init(
    {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true
      },
      walletId: {
        type: DataTypes.INTEGER,
        allowNull: false
      },
      sessionId: {
        type: DataTypes.STRING(128),
        allowNull: false
      },
      launchTime: {
        type: DataTypes.DATE,
        allowNull: false
      },
      date: {
        type: DataTypes.DATEONLY,
        allowNull: false
      },
      userAgent: {
        type: DataTypes.STRING(500)
      },
      ipAddress: {
        type: DataTypes.STRING(45)
      },
      referrer: {
        type: DataTypes.STRING(500)
      },
      deviceType: {
        type: DataTypes.STRING(20)
      },
      browser: {
        type: DataTypes.STRING(20)
      }
    },
    {
      sequelize,
      modelName: 'AppLaunch',
      tableName: 'app_launches',
      timestamps: true,
      updatedAt: false
    }
  );
}
```

### 4. 集成到Express应用

```typescript
// src/app.ts
import express from 'express';
import cors from 'cors';
import { sessionMiddleware } from './config/sessionConfig';
import { sessionTrackingMiddleware } from './middlewares/sessionTracking';
import { walletAuthMiddleware } from './middlewares/walletAuth';

const app = express();

// CORS配置（重要：必须配置credentials才能传递Cookie）
const corsOptions = {
  credentials: true, // 关键：允许携带Cookie
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization'],
  exposedHeaders: ['set-cookie']
};

app.use(cors(corsOptions));

// 基础中间件
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Session中间件（必须在CORS之后）
app.use(sessionMiddleware);

// 需要认证的路由
app.use('/api/*', walletAuthMiddleware);

// 会话追踪中间件（在认证之后）
app.use('/api/*', sessionTrackingMiddleware);

// 路由
app.use('/api/wallet', walletRoutes);
app.use('/api/game', gameRoutes);
// ... 其他路由

export default app;
```

### 5. TypeScript类型定义

```typescript
// src/types/express-session.d.ts
import 'express-session';

declare module 'express-session' {
  interface SessionData {
    initialized?: boolean;
    walletId?: number;
    createdAt?: Date;
    lastActivityAt?: Date;
    requestCount?: number;
    launchCount?: number;
    [key: string]: any;
  }
}
```

## 统计查询实现

### 1. 更新统计服务

```typescript
// src/admin/services/userStatisticsService.ts
import { Op, QueryTypes } from 'sequelize';
import { sequelize } from '../../config/database';
import { AppLaunch } from '../../models/AppLaunch';
import { SessionDuration } from '../../models/SessionDuration';

export class UserStatisticsService {
  /**
   * 获取每日游戏启动次数（真实启动次数）
   */
  public static async getDailyAppOpens(
    startDate: Date, 
    endDate: Date
  ): Promise<number> {
    try {
      const count = await AppLaunch.count({
        where: {
          launchTime: {
            [Op.gte]: startDate,
            [Op.lt]: endDate
          }
        }
      });
      
      return count;
    } catch (error) {
      logger.error('获取每日启动次数失败', error);
      return 0;
    }
  }

  /**
   * 获取每日平均游玩时长（分钟）
   */
  public static async getAveragePlayTime(
    startDate: Date,
    endDate: Date
  ): Promise<number> {
    try {
      // 方案1：从session_durations表查询
      const result = await sequelize.query(`
        SELECT 
          AVG(sd.duration) as avgDurationSeconds,
          COUNT(DISTINCT sd.walletId) as uniqueUsers,
          COUNT(*) as totalSessions
        FROM session_durations sd
        INNER JOIN app_launches al ON sd.sessionId = al.sessionId
        WHERE al.launchTime >= :startDate 
          AND al.launchTime < :endDate
          AND sd.duration > 0
          AND sd.duration < 86400 -- 排除异常数据（超过24小时）
      `, {
        replacements: { startDate, endDate },
        type: QueryTypes.SELECT,
      }) as any[];
      
      // 转换为分钟
      const avgSeconds = result[0]?.avgDurationSeconds || 0;
      return Math.round(avgSeconds / 60);
    } catch (error) {
      logger.error('获取平均游玩时长失败', error);
      return 0; // 返回0而不是硬编码的30
    }
  }

  /**
   * 获取详细的启动统计
   */
  public static async getDetailedLaunchStats(date: string) {
    try {
      // 按小时分布
      const hourlyStats = await sequelize.query(`
        SELECT 
          HOUR(launchTime) as hour,
          COUNT(*) as launches,
          COUNT(DISTINCT walletId) as uniqueUsers
        FROM app_launches
        WHERE date = :date
        GROUP BY HOUR(launchTime)
        ORDER BY hour
      `, {
        replacements: { date },
        type: QueryTypes.SELECT
      });

      // 按设备类型分布
      const deviceStats = await sequelize.query(`
        SELECT 
          deviceType,
          COUNT(*) as launches,
          COUNT(DISTINCT walletId) as uniqueUsers
        FROM app_launches
        WHERE date = :date
        GROUP BY deviceType
      `, {
        replacements: { date },
        type: QueryTypes.SELECT
      });

      // 按浏览器分布
      const browserStats = await sequelize.query(`
        SELECT 
          browser,
          COUNT(*) as launches,
          COUNT(DISTINCT walletId) as uniqueUsers
        FROM app_launches
        WHERE date = :date
        GROUP BY browser
      `, {
        replacements: { date },
        type: QueryTypes.SELECT
      });

      return {
        hourlyDistribution: hourlyStats,
        deviceDistribution: deviceStats,
        browserDistribution: browserStats
      };
    } catch (error) {
      logger.error('获取详细启动统计失败', error);
      return null;
    }
  }

  /**
   * 获取用户留存率（基于真实启动）
   */
  public static async getRetentionRate(cohortDate: Date, daysLater: number) {
    try {
      // 获取队列用户（cohortDate当天首次启动的用户）
      const cohortUsers = await sequelize.query(`
        SELECT DISTINCT walletId
        FROM app_launches al1
        WHERE DATE(al1.launchTime) = :cohortDate
          AND NOT EXISTS (
            SELECT 1 FROM app_launches al2
            WHERE al2.walletId = al1.walletId
              AND DATE(al2.launchTime) < :cohortDate
          )
      `, {
        replacements: { cohortDate },
        type: QueryTypes.SELECT
      }) as any[];

      const cohortSize = cohortUsers.length;
      if (cohortSize === 0) return 0;

      // 计算目标日期
      const targetDate = new Date(cohortDate);
      targetDate.setDate(targetDate.getDate() + daysLater);

      // 获取在目标日期活跃的队列用户数
      const retainedUsers = await sequelize.query(`
        SELECT COUNT(DISTINCT walletId) as count
        FROM app_launches
        WHERE DATE(launchTime) = :targetDate
          AND walletId IN (:walletIds)
      `, {
        replacements: { 
          targetDate,
          walletIds: cohortUsers.map(u => u.walletId)
        },
        type: QueryTypes.SELECT
      }) as any[];

      const retainedCount = retainedUsers[0]?.count || 0;
      return Math.round((retainedCount / cohortSize) * 100);
    } catch (error) {
      logger.error('计算留存率失败', error);
      return 0;
    }
  }
}
```

### 2. 创建统计API

```typescript
// src/routes/admin/statistics.ts
import { Router } from 'express';
import { adminAuthMiddleware } from '../../middlewares/adminAuth';
import { UserStatisticsService } from '../../admin/services/userStatisticsService';

const router = Router();

// 获取每日启动次数
router.get('/daily-launches', adminAuthMiddleware, async (req, res) => {
  try {
    const { date = new Date().toISOString().split('T')[0] } = req.query;
    
    const startDate = new Date(date);
    const endDate = new Date(date);
    endDate.setDate(endDate.getDate() + 1);
    
    const launches = await UserStatisticsService.getDailyAppOpens(
      startDate,
      endDate
    );
    
    res.json({
      success: true,
      data: {
        date,
        launches
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: '获取启动次数失败'
    });
  }
});

// 获取平均游玩时长
router.get('/average-playtime', adminAuthMiddleware, async (req, res) => {
  try {
    const { date = new Date().toISOString().split('T')[0] } = req.query;
    
    const startDate = new Date(date);
    const endDate = new Date(date);
    endDate.setDate(endDate.getDate() + 1);
    
    const avgMinutes = await UserStatisticsService.getAveragePlayTime(
      startDate,
      endDate
    );
    
    res.json({
      success: true,
      data: {
        date,
        averagePlayTimeMinutes: avgMinutes,
        averagePlayTimeFormatted: formatDuration(avgMinutes * 60)
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: '获取游玩时长失败'
    });
  }
});

// 获取详细统计
router.get('/detailed-stats/:date', adminAuthMiddleware, async (req, res) => {
  try {
    const { date } = req.params;
    const stats = await UserStatisticsService.getDetailedLaunchStats(date);
    
    res.json({
      success: true,
      data: stats
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: '获取详细统计失败'
    });
  }
});

function formatDuration(seconds: number): string {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  
  if (hours > 0) {
    return `${hours}小时${minutes}分钟`;
  }
  return `${minutes}分钟`;
}

export default router;
```

## 部署与监控

### 1. 环境变量配置

```bash
# .env
SESSION_SECRET=your-strong-session-secret-key
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your-redis-password
FRONTEND_URL=http://localhost:3000
COOKIE_DOMAIN=.example.com  # 可选：用于子域名共享Cookie
```

### 2. Redis配置优化

```javascript
// src/config/redisClient.ts
import Redis from 'redis';

export const redisClient = Redis.createClient({
  host: process.env.REDIS_HOST,
  port: process.env.REDIS_PORT,
  password: process.env.REDIS_PASSWORD,
  db: 0,
  // 性能优化配置
  enable_offline_queue: false,
  retry_strategy: (options) => {
    if (options.error && options.error.code === 'ECONNREFUSED') {
      return new Error('Redis连接被拒绝');
    }
    if (options.total_retry_time > 1000 * 60 * 60) {
      return new Error('Redis重试超时');
    }
    if (options.attempt > 10) {
      return undefined;
    }
    return Math.min(options.attempt * 100, 3000);
  }
});

// 监听Redis事件
redisClient.on('error', (err) => {
  logger.error('Redis错误', err);
});

redisClient.on('connect', () => {
  logger.info('Redis连接成功');
});
```

### 3. 前端配置

对于前端应用，需要正确配置请求以携带Cookie：

```javascript
// 前端请求配置示例

// 使用 fetch
fetch('https://api.example.com/api/endpoint', {
  method: 'POST',
  credentials: 'include', // 关键：携带Cookie
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify(data)
});

// 使用 axios
import axios from 'axios';

const apiClient = axios.create({
  baseURL: process.env.REACT_APP_API_URL,
  withCredentials: true, // 关键：携带Cookie
  headers: {
    'Content-Type': 'application/json'
  }
});

// 使用 jQuery
$.ajax({
  url: 'https://api.example.com/api/endpoint',
  type: 'POST',
  xhrFields: {
    withCredentials: true // 关键：携带Cookie
  },
  data: JSON.stringify(data),
  contentType: 'application/json'
});
```

### 4. 定期清理任务

```typescript
// src/jobs/cleanupOldData.ts
import { Op } from 'sequelize';

// 清理超过30天的启动记录
export async function cleanupOldLaunches() {
  const thirtyDaysAgo = new Date();
  thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
  
  const deleted = await AppLaunch.destroy({
    where: {
      launchTime: { [Op.lt]: thirtyDaysAgo }
    }
  });
  
  logger.info('清理旧启动记录', { deletedCount: deleted });
}

// 每天凌晨3点执行
cron.schedule('0 3 * * *', cleanupOldLaunches);
```

### 5. CORS与安全配置

```typescript
// src/config/security.ts
export const securityConfig = {
  // CORS白名单
  corsWhitelist: [
    'http://localhost:3000',
    'http://localhost:5173',
    'https://app.example.com',
    'https://admin.example.com'
  ],
  
  // Session安全配置
  sessionSecurity: {
    // 生产环境强制HTTPS
    requireHttps: process.env.NODE_ENV === 'production',
    // Cookie域名（用于子域名共享）
    cookieDomain: process.env.COOKIE_DOMAIN,
    // SameSite策略
    sameSitePolicy: process.env.NODE_ENV === 'production' ? 'none' : 'lax'
  },
  
  // 请求限制
  rateLimits: {
    // 每IP每分钟最多创建20个新Session
    sessionCreation: {
      windowMs: 60 * 1000,
      max: 20
    }
  }
};
```

## FAQ

### Q1: 跨域环境下Session Cookie如何工作？
**A**: 跨域使用Session Cookie需要：
1. **后端CORS配置**：
   - 设置 `credentials: true`
   - 明确指定允许的域名
   - Cookie设置 `sameSite: 'none'` 和 `secure: true`（HTTPS）
2. **前端请求配置**：
   - fetch使用 `credentials: 'include'`
   - axios使用 `withCredentials: true`
3. **注意事项**：
   - 生产环境必须使用HTTPS
   - 某些浏览器限制第三方Cookie

### Q2: 用户使用多个标签页怎么处理？
**A**: 多个标签页共享同一个Session Cookie，因此：
- 只有第一个标签页会记录为新启动
- 所有标签页的活动都会更新同一个Session的活跃时间
- 只有关闭所有标签页（浏览器）才会清除Cookie

### Q3: 如何处理浏览器崩溃的情况？
**A**: 浏览器崩溃时：
- Cookie会被清除（因为是会话Cookie）
- 下次访问会创建新Session，记录为新启动
- 上次的Session通过定期任务标记为结束

### Q4: 游玩时长统计的准确性如何？
**A**: 准确性取决于实现方案：
- 轻量级方案：基于最后活跃时间，误差在5分钟内
- 精确方案：记录每次活动，准确度高但数据量大

### Q5: 如何优化大量用户的性能？
**A**: 性能优化建议：
1. 使用Redis Cluster分片存储Session
2. 启动记录表按月分区
3. 统计数据预聚合，避免实时计算
4. 使用读写分离减轻数据库压力

### Q6: 隐私模式/无痕浏览如何处理？
**A**: 隐私模式下：
- Session Cookie仍然有效
- 关闭隐私窗口时Cookie被清除
- 行为与正常模式一致，可以正确统计

### Q7: 移动端App如何处理？
**A**: 移动端App的处理方案：
1. **WebView方案**：
   - WebView支持Cookie，行为与浏览器类似
   - App关闭=Cookie清除=新启动
2. **原生App方案**：
   - 使用设备ID + 时间戳模拟Session
   - App启动时创建新的"虚拟Session"
   - 通过API传递Session标识

## 总结

本方案通过利用会话Cookie的特性，实现了准确的启动次数统计和合理的游玩时长估算。主要优势：

1. **实现简单**：利用浏览器原生行为，无需复杂逻辑
2. **准确可靠**：真实反映用户启动行为
3. **性能优异**：被动检测，无额外开销
4. **易于维护**：代码清晰，依赖少

建议按照本文档逐步实施，先实现基础功能，再根据需求添加高级特性。
