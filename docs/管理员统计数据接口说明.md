# 管理员统计数据接口说明

## 目录
1. [概述](#概述)
2. [API认证](#api认证)
3. [21项统计数据详解](#21项统计数据详解)
4. [主要API接口](#主要api接口)
5. [快速查询对照表](#快速查询对照表)
6. [使用示例](#使用示例)
7. [注意事项](#注意事项)

## 概述

本文档详细说明了管理员统计系统中21项核心统计数据的获取方式，包括每项数据的含义、所在接口位置、返回格式等信息。

### 基础信息
- **基础路径**: `/api/admin/statistics`
- **认证方式**: JWT Token
- **支持格式**: JSON / Excel
- **时区**: Asia/Shanghai (UTC+8)

## API认证

### 获取管理员Token
```bash
POST /api/admin/auth/login
Content-Type: application/json

{
  "username": "admin",
  "password": "your_password"
}
```

### 在请求中使用Token
```bash
Authorization: Bearer <your_jwt_token>
```

## 21项统计数据详解

### 1. 总每日游戏启动次数
- **含义**: 所有用户每天打开游戏的总次数
- **接口**: `/dashboard` 或 `/users`
- **字段**: `userBehavior.dailyAppOpens`
- **类型**: Number

### 2. 解锁区域2-20的人数
- **含义**: 解锁农场各个区域的玩家人数统计
- **接口**: `/dashboard` 或 `/progress`
- **字段**: `gameProgress.farmAreaUnlocks`
- **格式**:
```json
[
  { "area": 2, "unlockedCount": 150 },
  { "area": 3, "unlockedCount": 120 },
  // ... 直到区域20
]
```

### 3. 升级流水线到各等级的人数
- **含义**: 流水线升级到10,20,25,30,35,40,45,50级的玩家人数
- **接口**: `/dashboard` 或 `/progress`
- **字段**: `gameProgress.deliveryLineLevelDistribution`
- **格式**:
```json
[
  { "level": 10, "count": 200 },
  { "level": 20, "count": 150 },
  // ... 其他等级
]
```

### 4. 每种道具的总使用人数
- **含义**: 使用过各种道具的玩家人数（去重）
- **接口**: `/dashboard`
- **字段**: `itemUsage.items[].totalUsers`

### 5. 每种道具的总使用次数
- **含义**: 各种道具的累计使用次数
- **接口**: `/dashboard`
- **字段**: `itemUsage.items[].totalUsageCount`
- **格式示例**:
```json
{
  "items": [
    {
      "itemType": "speed_boost",
      "itemName": "加速道具",
      "totalUsers": 450,
      "totalUsageCount": 1280
    }
  ]
}
```

### 6. 玩家总充值数
- **含义**: 所有玩家的充值总额（USD）
- **接口**: `/dashboard` 或 `/economy`
- **字段**: `purchase.totalRevenue`
- **类型**: Number (保留2位小数)

### 7. 玩家每日开启APP次数
- **含义**: 与第1项相同
- **接口**: `/dashboard` 或 `/users`
- **字段**: `userBehavior.dailyAppOpens`

### 8. 升级牧场区域1-20到各等级的人数
- **含义**: 各个牧场区域升级到10,20,25,30,35,40,45,50级的人数
- **接口**: `/progress`
- **字段**: `detailed.farmPlotLevels`

### 9. 玩家完成任务总次数
- **含义**: 所有玩家完成任务的累计次数
- **接口**: `/dashboard`
- **字段**: `task.totalTaskCompletions`
- **类型**: Number

### 10. 完成每个任务ID的人数
- **含义**: 完成特定任务的玩家人数
- **接口**: `/dashboard`
- **字段**: `task.taskCompletions`
- **格式**:
```json
[
  {
    "taskId": 1,
    "taskName": "每日登录",
    "completedUsers": 450
  }
]
```

### 11. 玩家邀请好友的总数
- **含义**: 所有玩家发出的邀请总数
- **接口**: `/dashboard` 或 `/social`
- **字段**: `social.totalInvitations`

### 12. 成功邀请到好友不同人数的分布
- **含义**: 邀请1,5,10,20...200人的玩家数量分布
- **接口**: `/dashboard` 或 `/social`
- **字段**: `social.invitationDistribution`
- **格式**:
```json
[
  { "inviteCount": 1, "userCount": 300 },
  { "inviteCount": 5, "userCount": 150 },
  { "inviteCount": 10, "userCount": 80 },
  // ... 直到200人
]
```

### 13. 玩家获取的总箱子数
- **含义**: 所有玩家获得的宝箱总数
- **接口**: `/dashboard`
- **字段**: `reward.totalChestsObtained`

### 14. 每天领取每日宝箱的人数
- **含义**: 领取每日宝箱的玩家人数
- **接口**: `/dashboard`
- **字段**: `reward.dailyChestClaimUsers`

### 15. 玩家数量每日增量
- **含义**: 每天新增的玩家数量
- **接口**: `/dashboard` 或 `/users`
- **字段**: `userBehavior.dailyNewUsers`

### 16. 7日玩家总量
- **含义**: 最近7天活跃的玩家总数
- **接口**: `/dashboard` 或 `/users`
- **字段**: `userBehavior.sevenDayUsers`

### 17. 首日留存
- **含义**: 首日留存率（百分比）
- **接口**: `/dashboard` 或 `/users`
- **字段**: `userBehavior.firstDayRetention`
- **类型**: Number (0-100)

### 18. 七日留存
- **含义**: 七日留存率（百分比）
- **接口**: `/dashboard` 或 `/users`
- **字段**: `userBehavior.sevenDayRetention`
- **类型**: Number (0-100)

### 19. 玩家获取钻石数
- **含义**: 所有玩家获得的钻石总数
- **接口**: `/dashboard`
- **字段**: `reward.totalDiamondsEarned`

### 20. 玩家获取GEM数
- **含义**: 所有玩家获得的宝石总数
- **接口**: `/dashboard`
- **字段**: `reward.totalGemsEarned`

### 21. 每日玩家平均游玩时长
- **含义**: 玩家每日平均游玩时长（分钟）
- **接口**: `/dashboard` 或 `/users`
- **字段**: `userBehavior.averagePlayTime`
- **类型**: Number

## 主要API接口

### 1. 综合统计仪表板（包含所有21项数据）
```
GET /api/admin/statistics/dashboard
Query参数:
  - startDate: 开始日期 (YYYY-MM-DD)
  - endDate: 结束日期 (YYYY-MM-DD)
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "userBehavior": {
      "dailyAppOpens": 5280,
      "averagePlayTime": 45.5,
      "dailyNewUsers": 120,
      "sevenDayUsers": 3200,
      "firstDayRetention": 65.5,
      "sevenDayRetention": 42.3
    },
    "gameProgress": {
      "farmAreaUnlocks": [...],
      "deliveryLineLevelDistribution": [...]
    },
    "itemUsage": {
      "items": [...]
    },
    "purchase": {
      "totalRevenue": 12580.50
    },
    "task": {
      "totalTaskCompletions": 8520,
      "taskCompletions": [...]
    },
    "social": {
      "totalInvitations": 1850,
      "invitationDistribution": [...]
    },
    "reward": {
      "totalChestsObtained": 15600,
      "dailyChestClaimUsers": 850,
      "totalDiamondsEarned": 458000,
      "totalGemsEarned": 2850000
    }
  }
}
```

### 2. Excel导出（包含所有21项数据）
```
GET /api/admin/statistics/export
Query参数:
  - startDate: 开始日期 (可选)
  - endDate: 结束日期 (可选)
```

### 3. 专项统计接口

#### 用户统计（包含第1,7,15-18,21项）
```
GET /api/admin/statistics/users
```

#### 游戏进度统计（包含第2,3,8项）
```
GET /api/admin/statistics/progress
```

#### 经济系统统计（包含第6,19,20项）
```
GET /api/admin/statistics/economy
```

#### 社交系统统计（包含第11,12项）
```
GET /api/admin/statistics/social
```

### 4. 趋势分析接口

#### 用户增长趋势
```
GET /api/admin/statistics/trends/users?days=30
```

#### 收入趋势
```
GET /api/admin/statistics/trends/revenue?days=30
```

#### 任务完成趋势
```
GET /api/admin/statistics/trends/tasks?days=30
```

## 快速查询对照表

| 编号 | 统计项 | 最简接口 | 返回字段 |
|------|--------|----------|----------|
| 1 | 总每日游戏启动次数 | `/dashboard` | `userBehavior.dailyAppOpens` |
| 2 | 解锁区域2-20的人数 | `/dashboard` | `gameProgress.farmAreaUnlocks` |
| 3 | 升级流水线到各等级的人数 | `/dashboard` | `gameProgress.deliveryLineLevelDistribution` |
| 4 | 每种道具的总使用人数 | `/dashboard` | `itemUsage.items[].totalUsers` |
| 5 | 每种道具的总使用次数 | `/dashboard` | `itemUsage.items[].totalUsageCount` |
| 6 | 玩家总充值数 | `/dashboard` | `purchase.totalRevenue` |
| 7 | 玩家每日开启APP次数 | `/dashboard` | `userBehavior.dailyAppOpens` |
| 8 | 升级牧场区域到各等级的人数 | `/progress` | `detailed.farmPlotLevels` |
| 9 | 玩家完成任务总次数 | `/dashboard` | `task.totalTaskCompletions` |
| 10 | 完成每个任务ID的人数 | `/dashboard` | `task.taskCompletions` |
| 11 | 玩家邀请好友的总数 | `/dashboard` | `social.totalInvitations` |
| 12 | 成功邀请到不同人数的分布 | `/dashboard` | `social.invitationDistribution` |
| 13 | 玩家获取的总箱子数 | `/dashboard` | `reward.totalChestsObtained` |
| 14 | 每天领取每日宝箱的人数 | `/dashboard` | `reward.dailyChestClaimUsers` |
| 15 | 玩家数量每日增量 | `/dashboard` | `userBehavior.dailyNewUsers` |
| 16 | 7日玩家总量 | `/dashboard` | `userBehavior.sevenDayUsers` |
| 17 | 首日留存 | `/dashboard` | `userBehavior.firstDayRetention` |
| 18 | 七日留存 | `/dashboard` | `userBehavior.sevenDayRetention` |
| 19 | 玩家获取钻石数 | `/dashboard` | `reward.totalDiamondsEarned` |
| 20 | 玩家获取GEM数 | `/dashboard` | `reward.totalGemsEarned` |
| 21 | 每日玩家平均游玩时长 | `/dashboard` | `userBehavior.averagePlayTime` |

## 使用示例

### 1. 获取管理员Token
```bash
curl -X POST http://localhost:3456/api/admin/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"Admin@123456"}'
```

### 2. 获取所有统计数据
```bash
curl -X GET http://localhost:3456/api/admin/statistics/dashboard \
  -H "Authorization: Bearer <your_token>"
```

### 3. 获取特定时间段数据
```bash
curl -X GET "http://localhost:3456/api/admin/statistics/dashboard?startDate=2025-01-01&endDate=2025-01-31" \
  -H "Authorization: Bearer <your_token>"
```

### 4. 导出Excel报表
```bash
curl -X GET http://localhost:3456/api/admin/statistics/export \
  -H "Authorization: Bearer <your_token>" \
  -o statistics.xlsx
```

### 5. 使用Node.js获取数据
```javascript
const axios = require('axios');

async function getStatistics(token) {
  try {
    const response = await axios.get('http://localhost:3456/api/admin/statistics/dashboard', {
      headers: {
        'Authorization': `Bearer ${token}`
      },
      params: {
        startDate: '2025-01-01',
        endDate: '2025-01-31'
      }
    });
    
    console.log('总充值数:', response.data.data.purchase.totalRevenue);
    console.log('日活用户:', response.data.data.userBehavior.dailyAppOpens);
    console.log('七日留存:', response.data.data.userBehavior.sevenDayRetention + '%');
  } catch (error) {
    console.error('获取统计数据失败:', error);
  }
}
```

## 注意事项

1. **认证要求**: 所有统计接口都需要管理员JWT Token认证
2. **日期格式**: 日期参数使用 `YYYY-MM-DD` 格式
3. **时区**: 所有时间数据基于 Asia/Shanghai (UTC+8) 时区
4. **数据精度**: 
   - 金额类数据保留2位小数
   - 百分比数据范围 0-100
   - 时长以分钟为单位
5. **性能考虑**: 
   - 建议使用日期范围限制查询范围
   - `/dashboard` 接口返回所有数据，数据量较大
   - 如只需特定维度数据，使用专项接口更高效
6. **缓存策略**: 部分统计数据可能有缓存，实时性要求高的场合请注意
7. **并发限制**: 建议控制并发请求数量，避免对服务器造成压力

## 常见问题

### Q1: 如何只获取今天的数据？
```bash
# 使用相同的开始和结束日期
GET /api/admin/statistics/dashboard?startDate=2025-01-05&endDate=2025-01-05
```

### Q2: 如何获取实时数据？
大部分统计数据是实时计算的，但以下数据可能有延迟：
- 留存率数据（需要时间周期计算）
- 部分汇总数据（可能有5-10分钟缓存）

### Q3: Excel导出文件太大怎么办？
建议：
1. 使用日期范围限制数据量
2. 分批导出（按月份）
3. 使用专项接口获取特定数据

### Q4: 如何监控数据变化趋势？
使用趋势分析接口：
- `/trends/users` - 用户增长趋势
- `/trends/revenue` - 收入趋势
- `/trends/tasks` - 任务完成趋势
- `/trends/rewards` - 奖励发放趋势
- `/trends/items` - 道具使用趋势

## 更新日志

- **2025-01-05**: 文档初始版本，包含21项核心统计数据说明
