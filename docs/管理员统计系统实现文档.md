# 管理员统计系统实现文档

## 概述
本文档描述了管理员统计系统的完整实现方案，该系统用于收集和分析游戏中的各项运营数据。

## 系统架构

### 目录结构
```
src/admin/
├── types/
│   └── statistics.ts          # 统计数据类型定义
├── services/                  # 统计服务层
│   ├── userStatisticsService.ts       # 用户行为统计
│   ├── gameProgressStatisticsService.ts # 游戏进度统计
│   ├── purchaseStatisticsService.ts   # 购买/充值统计
│   ├── taskStatisticsService.ts       # 任务统计
│   ├── socialStatisticsService.ts     # 社交系统统计
│   ├── rewardStatisticsService.ts     # 奖励系统统计
│   └── itemStatisticsService.ts       # 道具使用统计
├── controllers/
│   └── statisticsController.ts        # 统计控制器
└── routes/
    └── statisticsRoutes.ts            # 统计路由
```

## 数据分类

### 1. 用户行为统计
- 每日游戏启动次数
- 每日平均游玩时长
- 玩家数量每日增量
- 7日玩家总量
- 首日留存率
- 七日留存率

### 2. 游戏进度统计
- 解锁区域2-20的人数
- 升级流水线到各等级（10,20,25,30,35,40,45,50级）的人数
- 升级牧场区域1-20到各等级的人数

### 3. 道具使用统计
- 每种道具的总使用人数
- 每种道具的总使用次数

### 4. 购买统计
- 玩家总充值数
- ARPU（平均每用户收入）
- ARPPU（平均每付费用户收入）
- 支付方式分布

### 5. 任务统计
- 玩家完成任务总次数
- 完成每个任务ID的人数

### 6. 社交统计
- 玩家邀请好友的总数
- 成功邀请到好友不同人数的用户分布（1,5,10,20,30...200人）

### 7. 奖励统计
- 玩家获取的总箱子数
- 每天领取每日宝箱的人数
- 玩家获取钻石总数
- 玩家获取GEM总数

## API 接口

### 基础路径
`/api/admin/statistics`

### 主要端点

#### 1. 综合统计仪表板
```
GET /api/admin/statistics/dashboard
Query参数:
  - startDate: 开始日期（可选）
  - endDate: 结束日期（可选）
```

#### 2. 用户统计详情
```
GET /api/admin/statistics/users
Query参数:
  - startDate: 开始日期（可选）
  - endDate: 结束日期（可选）
```

#### 3. 游戏进度统计
```
GET /api/admin/statistics/progress
```

#### 4. 经济系统统计
```
GET /api/admin/statistics/economy
Query参数:
  - startDate: 开始日期（可选）
  - endDate: 结束日期（可选）
```

#### 5. 社交系统统计
```
GET /api/admin/statistics/social
```

#### 6. 导出Excel
```
GET /api/admin/statistics/export
Query参数:
  - startDate: 开始日期（可选）
  - endDate: 结束日期（可选）
```

### 趋势分析端点

#### 用户增长趋势
```
GET /api/admin/statistics/trends/users
Query参数:
  - days: 天数（默认30）
```

#### 收入趋势
```
GET /api/admin/statistics/trends/revenue
Query参数:
  - days: 天数（默认30）
```

#### 任务完成趋势
```
GET /api/admin/statistics/trends/tasks
Query参数:
  - days: 天数（默认30）
```

#### 奖励趋势
```
GET /api/admin/statistics/trends/rewards
Query参数:
  - days: 天数（默认30）
```

#### 道具使用趋势
```
GET /api/admin/statistics/trends/items
Query参数:
  - days: 天数（默认30）
```

## 认证要求

所有统计API都需要管理员认证，使用JWT token进行身份验证。

### 获取管理员Token
```bash
POST /api/admin/auth/login
Content-Type: application/json

{
  "username": "admin",
  "password": "your_password"
}
```

### 使用Token访问统计API
```bash
GET /api/admin/statistics/dashboard
Authorization: Bearer <your_jwt_token>
```

## 测试命令

### 1. 测试综合统计仪表板
```bash
# 先登录获取token
curl -X POST http://localhost:3456/api/admin/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"your_password"}'

# 使用token访问统计API
curl -X GET http://localhost:3456/api/admin/statistics/dashboard \
  -H "Authorization: Bearer <your_token>"
```

### 2. 测试日期范围查询
```bash
curl -X GET "http://localhost:3456/api/admin/statistics/dashboard?startDate=2025-01-01&endDate=2025-01-31" \
  -H "Authorization: Bearer <your_token>"
```

### 3. 测试Excel导出
```bash
curl -X GET http://localhost:3456/api/admin/statistics/export \
  -H "Authorization: Bearer <your_token>" \
  -o statistics.xlsx
```

### 4. 测试趋势分析
```bash
# 获取最近7天的用户增长趋势
curl -X GET "http://localhost:3456/api/admin/statistics/trends/users?days=7" \
  -H "Authorization: Bearer <your_token>"

# 获取最近30天的收入趋势
curl -X GET "http://localhost:3456/api/admin/statistics/trends/revenue?days=30" \
  -H "Authorization: Bearer <your_token>"
```

## 数据库依赖

统计系统依赖以下数据表：
- `user_wallets` - 用户钱包信息
- `farm_plots` - 牧场区域信息
- `delivery_lines` - 流水线信息
- `active_boosters` - 活跃道具信息
- `iap_purchases` - 应用内购买记录
- `tasks` - 任务信息
- `user_task_complete` - 用户任务完成记录
- `chests` - 宝箱信息
- `user_login_logs` - 用户登录日志
- `admin_users` - 管理员用户表

## 性能优化

1. **并行查询**: 在综合统计中使用 `Promise.all()` 并行执行多个统计查询
2. **SQL优化**: 使用原生SQL查询和聚合函数减少数据传输
3. **时间范围限制**: 支持日期范围查询，避免查询全量数据
4. **索引优化**: 确保相关字段有适当的数据库索引

## 错误处理

所有统计服务都包含完整的错误处理：
- 使用try-catch捕获异常
- 记录详细的错误日志
- 返回友好的错误响应

## Excel导出功能

导出功能使用ExcelJS库，生成包含所有统计数据的Excel文件：
- 自动格式化列宽
- 包含序号、统计项、数值等列
- 文件名包含导出日期

## 扩展性

系统设计采用模块化架构，便于扩展：
- 每个统计维度独立服务
- 统一的类型定义
- 标准化的API响应格式

## 注意事项

1. **权限控制**: 确保只有授权的管理员可以访问统计数据
2. **数据安全**: 敏感数据应进行脱敏处理
3. **性能监控**: 对于大数据量查询，需要监控响应时间
4. **缓存策略**: 可以考虑对热点数据添加Redis缓存

## 部署检查清单

- [ ] 确认管理员账号已创建
- [ ] 验证JWT密钥配置正确
- [ ] 检查数据库连接配置
- [ ] 确认所有依赖包已安装（特别是exceljs）
- [ ] 测试所有API端点
- [ ] 验证Excel导出功能
- [ ] 检查日志记录是否正常

## 相关命令

### 安装依赖
```bash
npm install exceljs
```

### 启动服务
```bash
npm run dev
```

### 构建生产版本
```bash
npm run build
npm start
```

## 更新日志

### 2025-01-04
- 初始版本实现
- 包含21项核心统计指标
- 实现Excel导出功能
- 添加趋势分析API
