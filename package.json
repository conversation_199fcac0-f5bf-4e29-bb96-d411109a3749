{"name": "wolf_fun", "version": "1.0.0", "main": "dist/app.js", "scripts": {"dev": "cross-env NODE_ENV=development tsnd --respawn src/app.ts", "build": "cross-env NODE_ENV=production tsc", "build:dev": "cross-env NODE_ENV=development tsc", "start": "cross-env NODE_ENV=production node dist/app.js", "start:dev": "cross-env NODE_ENV=development tsnd --respawn src/app.ts", "local:kaia": "cross-env ENV_FILE=.env.local.kaia NODE_ENV=development tsnd --respawn src/app.ts", "local:pharos": "cross-env ENV_FILE=.env.local.pharos NODE_ENV=development tsnd --respawn src/app.ts", "local:kaia:build": "cross-env ENV_FILE=.env.local.kaia NODE_ENV=production node dist/app.js", "local:pharos:build": "cross-env ENV_FILE=.env.local.pharos NODE_ENV=production node dist/app.js", "seed:tasks:local:kaia": "cross-env ENV_FILE=.env.local.kaia npx sequelize-cli db:migrate && cross-env ENV_FILE=.env.local.kaia npx sequelize-cli db:seed --seed 20250120073040-add_task.js --config config/config.js && cross-env ENV_FILE=.env.local.kaia npx sequelize-cli db:seed --seed 20250610000000-add-iap-products.js --config config/config.js", "seed:tasks:local:pharos": "cross-env ENV_FILE=.env.local.pharos npx sequelize-cli db:migrate && cross-env ENV_FILE=.env.local.pharos npx sequelize-cli db:seed --seed 20250120073040-add_task.js --config config/config.js && cross-env ENV_FILE=.env.local.pharos npx sequelize-cli db:seed --seed 20250610000000-add-iap-products.js --config config/config.js", "seed:tasks:docker:kaia": "cross-env ENV_FILE=.env_kaia npx sequelize-cli db:migrate && cross-env ENV_FILE=.env_kaia npx sequelize-cli db:seed --seed 20250120073040-add_task.js --config config/config.js && cross-env ENV_FILE=.env_kaia npx sequelize-cli db:seed --seed 20250610000000-add-iap-products.js --config config/config.js", "seed:tasks:docker:pharos": "cross-env ENV_FILE=.env_pharos npx sequelize-cli db:migrate && cross-env ENV_FILE=.env_pharos npx sequelize-cli db:seed --seed 20250120073040-add_task.js --config config/config.js && cross-env ENV_FILE=.env_pharos npx sequelize-cli db:seed --seed 20250610000000-add-iap-products.js --config config/config.js", "seed:docker": "./init-seed-data-docker.sh both", "seed:docker:kaia": "./init-seed-data-docker.sh kaia", "seed:docker:pharos": "./init-seed-data-docker.sh pharos", "seed:container": "./init-seed-in-container.sh both", "seed:container:kaia": "./init-seed-in-container.sh kaia", "seed:container:pharos": "./init-seed-in-container.sh pharos", "sync:db:local:kaia": "cross-env ENV_FILE=.env.local.kaia node sync_database.js", "sync:db:local:pharos": "cross-env ENV_FILE=.env.local.pharos node sync_database.js", "sync:db:force:local:kaia": "cross-env ENV_FILE=.env.local.kaia node sync_database_force.js", "sync:db:force:local:pharos": "cross-env ENV_FILE=.env.local.pharos node sync_database_force.js", "sync:db:docker": "./sync-database-docker.sh both", "sync:db:docker:kaia": "./sync-database-docker.sh kaia", "sync:db:docker:pharos": "./sync-database-docker.sh pharos", "sync:db:container": "./sync-in-container.sh both", "sync:db:container:kaia": "./sync-in-container.sh kaia", "sync:db:container:pharos": "./sync-in-container.sh pharos", "farm-config:init": "node scripts/init-farm-config-simple.js", "farm-config:init:force": "node scripts/init-farm-config-simple.js --force", "docker:start": "./scripts/docker-manage.sh start-all", "docker:stop": "./scripts/docker-manage.sh stop-all", "docker:restart": "./scripts/docker-manage.sh restart-all", "docker:status": "./scripts/docker-manage.sh status", "docker:logs": "./scripts/docker-manage.sh logs", "docker:kaia": "./scripts/docker-manage.sh start-kaia", "docker:pharos": "./scripts/docker-manage.sh start-pharos", "docker:build:kaia": "./scripts/docker-build.sh kaia", "docker:build:pharos": "./scripts/docker-build.sh pharos", "docker:build:both": "./scripts/docker-build.sh both", "docker:build:kaia:push": "./scripts/docker-build.sh kaia --push", "docker:build:pharos:push": "./scripts/docker-build.sh pharos --push", "deploy:production": "./deploy-production.sh", "deploy:production:kaia": "./deploy-production.sh kaia", "logging:test": "npm run build && node scripts/test-logging-system.js", "logging:migrate": "node scripts/migrate-console-logs.js", "logging:setup": "cp .env.logging.example .env.logging", "deploy:production:pharos": "./deploy-production.sh pharos", "deploy:production:status": "./deploy-production.sh status", "deploy:server": "./deploy-server-production.sh", "deploy:server:skip-build": "./deploy-server-production.sh --skip-build", "deploy:server:server-mode": "./deploy-server-production.sh --server-mode", "deploy:server:simple": "./deploy-server-simple.sh", "deploy:server:simple:dry-run": "./deploy-server-simple.sh --dry-run", "deploy:server:force": "./deploy-server-production.sh --force-config", "deploy:server:dry-run": "./deploy-server-production.sh --dry-run", "deploy:server:quick": "./deploy-server-production.sh --skip-build --skip-sync", "update:code": "./update-code.sh --force-remote", "update:code:kaia": "./update-code.sh kaia", "update:code:pharos": "./update-code.sh pharos", "update:code:dry-run": "./update-code.sh --dry-run", "update:code:force": "./update-code.sh --force", "update:code:force-remote": "./update-code.sh --force-remote", "update:code:skip-git": "./update-code.sh --skip-git", "migrate:docker": "node scripts/docker-migration-manager.js", "migrate:docker:kaia": "node scripts/docker-migration-manager.js --container moofun-kaia-container --database wolf_kaia", "migrate:docker:pharos": "node scripts/docker-migration-manager.js --container moofun-pharos-container --database wolf_pharos", "migrate:docker:dry-run": "node scripts/docker-migration-manager.js --dry-run", "migrate:docker:debug": "node scripts/docker-migration-manager.js --debug", "migrate:status": "node scripts/check-migration-status.js", "migrate:status:files": "node scripts/check-migration-status.js --files", "migrate:status:report": "node scripts/check-migration-status.js --save-report", "migrate:test": "node scripts/test-docker-migration.js", "migrate:docker:simple": "./scripts/docker-migrate.sh", "migrate:docker:simple:kaia": "./scripts/docker-migrate.sh kaia", "migrate:docker:simple:pharos": "./scripts/docker-migrate.sh pharos", "migrate:docker:simple:status": "./scripts/docker-migrate.sh --status", "migrate:docker:simple:dry-run": "./scripts/docker-migrate.sh --dry-run", "update:phrs:now": "node scripts/update-phrs-prices-now.js", "update:phrs:quick": "./scripts/update-phrs-quick.sh", "update:phrs:1000000": "./scripts/update-phrs-quick.sh 1000000", "update:phrs:dry-run": "./scripts/update-phrs-quick.sh 1000000 --dry-run", "diagnose:migration": "./scripts/diagnose-migration-failure.sh", "diagnose:migration:kaia": "./scripts/diagnose-migration-failure.sh moofun-kaia-container", "diagnose:migration:pharos": "./scripts/diagnose-migration-failure.sh moofun-pharos-container", "test:phrs:wallet-lookup": "node scripts/test-phrs-wallet-lookup.js", "fix:server-update": "./server-update-fix.sh", "fix:git-conflicts": "./server-update-fix.sh --fix-conflicts", "fix:reset-remote": "./server-update-fix.sh --reset-hard --backup-local", "fix:timezone": "node fix-timezone-data.js", "rollback": "./rollback.sh", "rollback:kaia": "./rollback.sh kaia", "rollback:pharos": "./rollback.sh pharos", "rollback:list": "./rollback.sh --list-backups", "local:setup": "./scripts/local-dev.sh setup", "local:docker-up": "./scripts/local-dev.sh docker-up", "local:docker-down": "./scripts/local-dev.sh docker-down", "local:db-sync": "./scripts/local-dev.sh db-sync", "local:start-both": "./scripts/local-dev.sh start-both", "local:seed:kaia": "npm run seed:tasks:local:kaia", "local:seed:pharos": "npm run seed:tasks:local:pharos", "docker:seed:kaia": "npm run seed:tasks:docker:kaia", "docker:seed:pharos": "npm run seed:tasks:docker:pharos", "fix:delivery-configs": "./scripts/insert-delivery-configs.sh", "init:game-configs:kaia": "./scripts/init-farm-configs-final.sh wolf_kaia", "init:game-configs:pharos": "./scripts/init-farm-configs-final.sh wolf_pharos", "init:game-configs:both": "./scripts/init-farm-configs-final.sh wolf_kaia && ./scripts/init-farm-configs-final.sh wolf_pharos", "init:game-configs:force": "./scripts/init-farm-configs-final.sh wolf_kaia --force && ./scripts/init-farm-configs-final.sh wolf_pharos --force", "init:task-configs:kaia": "./scripts/init-task-configs.sh kaia", "init:task-configs:pharos": "./scripts/init-task-configs.sh pharos", "init:task-configs:both": "./scripts/init-task-configs.sh both", "init:task-configs:force": "./scripts/init-task-configs.sh both --force", "init:all-configs:kaia": "./scripts/init-all-configs.sh kaia", "init:all-configs:pharos": "./scripts/init-all-configs.sh pharos", "init:all-configs:both": "./scripts/init-all-configs.sh both", "init:all-configs:force": "./scripts/init-all-configs.sh both --force", "init:all-configs:check": "./scripts/init-all-configs.sh both --check", "migrate:local:kaia": "./scripts/quick-migrate.sh kaia up", "migrate:local:pharos": "./scripts/quick-migrate.sh pharos up", "migrate:local:kaia:status": "./scripts/quick-migrate.sh kaia status", "migrate:local:pharos:status": "./scripts/quick-migrate.sh pharos status", "migrate:local:kaia:down": "./scripts/quick-migrate.sh kaia down", "migrate:local:pharos:down": "./scripts/quick-migrate.sh pharos down"}, "keywords": [], "author": "", "license": "ISC", "description": "", "engines": {"node": ">=22.0.0", "npm": ">=10.0.0"}, "dependencies": {"@ton/core": "^0.60.0", "@ton/crypto": "^3.3.0", "@ton/ton": "^15.3.1", "@tonconnect/ui-react": "^2.0.11", "@types/multer": "^2.0.0", "@types/node-polyglot": "^2.5.0", "ajv": "^8.17.1", "ajv-formats": "^3.0.1", "ajv-i18n": "^4.2.0", "axios": "^1.9.0", "bcrypt": "^6.0.0", "bignumber.js": "^9.3.0", "bullmq": "^5.41.4", "connect-redis": "^6.1.3", "cors": "^2.8.5", "cross-env": "^7.0.3", "dayjs": "^1.11.13", "dotenv": "^16.4.7", "ethers": "^6.14.0", "express": "^4.21.2", "express-rate-limit": "^7.5.0", "express-session": "^1.18.2", "grammy": "^1.35.1", "ioredis": "^5.4.2", "jsonwebtoken": "^9.0.2", "multer": "^2.0.2", "mysql2": "^3.14.3", "node-cron": "^3.0.3", "node-polyglot": "^2.6.0", "nodemailer": "^6.10.0", "sequelize": "^6.37.5", "sequelize-cli": "^6.6.2", "uuid": "^11.0.5", "xlsx": "^0.18.5", "zod": "^3.24.1"}, "devDependencies": {"@types/bcrypt": "^6.0.0", "@types/bignumber.js": "^4.0.3", "@types/cors": "^2.8.17", "@types/express": "^5.0.0", "@types/express-session": "^1.18.2", "@types/glob": "^8.1.0", "@types/jest": "^29.5.5", "@types/jsonwebtoken": "^9.0.7", "@types/node": "^22.13.9", "@types/node-cron": "^3.0.11", "@types/nodemailer": "^6.4.17", "@types/supertest": "^2.0.12", "exceljs": "^4.4.0", "jest": "^29.7.0", "supertest": "^6.3.3", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "ts-node-dev": "^2.0.0", "typescript": "^5.7.3"}}