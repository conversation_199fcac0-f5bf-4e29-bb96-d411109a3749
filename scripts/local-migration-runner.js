#!/usr/bin/env node

/**
 * 本地数据库迁移执行脚本
 * 
 * 功能：
 * - 自动读取本地环境配置
 * - 支持指定环境文件（kaia/pharos）
 * - 执行 Sequelize 迁移
 * - 提供详细的执行日志
 * - 支持干运行模式
 * 
 * 使用方法：
 * node scripts/local-migration-runner.js [options]
 * 
 * 选项：
 * --env <kaia|pharos>     指定环境（默认：kaia）
 * --dry-run              干运行模式，只显示将要执行的操作
 * --up                   执行向上迁移（默认）
 * --down                 执行向下迁移
 * --to <migration>       迁移到指定版本
 * --status               显示迁移状态
 * --help                 显示帮助信息
 */

const { execSync, spawn } = require('child_process');
const path = require('path');
const fs = require('fs');
const dayjs = require('dayjs');
const utc = require('dayjs/plugin/utc');
const timezone = require('dayjs/plugin/timezone');

dayjs.extend(utc);
dayjs.extend(timezone);

// 格式化中国时间
function formatChineseTime() {
  return dayjs().tz('Asia/Shanghai').format('YYYY-MM-DD HH:mm:ss.SSS');
}

// 彩色日志输出
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

const logger = {
  info: (message) => console.log(`${colors.cyan}[INFO]${colors.reset} ${formatChineseTime()} ${message}`),
  success: (message) => console.log(`${colors.green}[SUCCESS]${colors.reset} ${formatChineseTime()} ${message}`),
  warn: (message) => console.log(`${colors.yellow}[WARN]${colors.reset} ${formatChineseTime()} ${message}`),
  error: (message) => console.log(`${colors.red}[ERROR]${colors.reset} ${formatChineseTime()} ${message}`),
  debug: (message) => console.log(`${colors.magenta}[DEBUG]${colors.reset} ${formatChineseTime()} ${message}`)
};

// 解析命令行参数
function parseArgs() {
  const args = process.argv.slice(2);
  const options = {
    env: 'kaia',
    dryRun: false,
    direction: 'up',
    to: null,
    status: false,
    help: false
  };

  for (let i = 0; i < args.length; i++) {
    const arg = args[i];
    switch (arg) {
      case '--env':
        options.env = args[++i];
        break;
      case '--dry-run':
        options.dryRun = true;
        break;
      case '--up':
        options.direction = 'up';
        break;
      case '--down':
        options.direction = 'down';
        break;
      case '--to':
        options.to = args[++i];
        break;
      case '--status':
        options.status = true;
        break;
      case '--help':
        options.help = true;
        break;
      default:
        if (arg.startsWith('--')) {
          logger.warn(`未知选项: ${arg}`);
        }
    }
  }

  return options;
}

// 显示帮助信息
function showHelp() {
  console.log(`
${colors.bright}本地数据库迁移执行脚本${colors.reset}

${colors.cyan}使用方法：${colors.reset}
  node scripts/local-migration-runner.js [options]

${colors.cyan}选项：${colors.reset}
  --env <kaia|pharos>     指定环境（默认：kaia）
  --dry-run              干运行模式，只显示将要执行的操作
  --up                   执行向上迁移（默认）
  --down                 执行向下迁移
  --to <migration>       迁移到指定版本
  --status               显示迁移状态
  --help                 显示帮助信息

${colors.cyan}示例：${colors.reset}
  # 执行 kaia 环境的迁移
  node scripts/local-migration-runner.js --env kaia

  # 执行 pharos 环境的迁移
  node scripts/local-migration-runner.js --env pharos

  # 干运行模式查看将要执行的操作
  node scripts/local-migration-runner.js --env kaia --dry-run

  # 查看迁移状态
  node scripts/local-migration-runner.js --env kaia --status

  # 迁移到指定版本
  node scripts/local-migration-runner.js --env kaia --to 20250804000000

  # 回滚迁移
  node scripts/local-migration-runner.js --env kaia --down
`);
}

// 获取环境文件路径
function getEnvFilePath(env) {
  const envFiles = {
    kaia: '.env.local.kaia',
    pharos: '.env.local.pharos'
  };

  const envFile = envFiles[env];
  if (!envFile) {
    throw new Error(`不支持的环境: ${env}。支持的环境: ${Object.keys(envFiles).join(', ')}`);
  }

  const rootDir = path.resolve(__dirname, '..');
  const envFilePath = path.resolve(rootDir, envFile);

  if (!fs.existsSync(envFilePath)) {
    throw new Error(`环境文件不存在: ${envFilePath}`);
  }

  return envFilePath;
}

// 加载环境变量
function loadEnvironment(envFilePath) {
  logger.info(`加载环境配置: ${envFilePath}`);
  
  // 设置 ENV_FILE 环境变量，让 src/config/env.js 加载正确的配置
  process.env.ENV_FILE = path.basename(envFilePath);
  
  // 重新加载环境配置
  delete require.cache[require.resolve('../src/config/env')];
  require('../src/config/env');
  
  logger.success(`环境配置加载成功`);
  logger.debug(`数据库: ${process.env.DB_NAME}@${process.env.DB_HOST}:${process.env.DB_PORT}`);
}

// 检查数据库连接
async function checkDatabaseConnection() {
  logger.info('检查数据库连接...');
  
  const mysql = require('mysql2/promise');
  
  try {
    const connection = await mysql.createConnection({
      host: process.env.DB_HOST,
      port: process.env.DB_PORT,
      user: process.env.DB_USER,
      password: process.env.DB_PASS,
      database: process.env.DB_NAME,
      connectTimeout: 10000
    });
    
    await connection.ping();
    await connection.end();
    
    logger.success('数据库连接正常');
    return true;
  } catch (error) {
    logger.error(`数据库连接失败: ${error.message}`);
    return false;
  }
}

// 执行 Sequelize 命令
function executeSequelizeCommand(command, options = {}) {
  const rootDir = path.resolve(__dirname, '..');
  const configPath = path.resolve(rootDir, 'config/config.js');
  
  const fullCommand = `npx sequelize-cli ${command} --config ${configPath}`;
  
  logger.info(`执行命令: ${fullCommand}`);
  
  if (options.dryRun) {
    logger.warn('干运行模式：命令不会实际执行');
    return;
  }
  
  try {
    const result = execSync(fullCommand, {
      cwd: rootDir,
      stdio: 'inherit',
      env: {
        ...process.env,
        NODE_ENV: process.env.NODE_ENV || 'development'
      }
    });
    
    logger.success('命令执行成功');
    return result;
  } catch (error) {
    logger.error(`命令执行失败: ${error.message}`);
    throw error;
  }
}

// 显示迁移状态
function showMigrationStatus(options) {
  logger.info('获取迁移状态...');
  executeSequelizeCommand('db:migrate:status', options);
}

// 执行迁移
function runMigration(options) {
  let command = 'db:migrate';
  
  if (options.direction === 'down') {
    command = 'db:migrate:undo';
  }
  
  if (options.to) {
    command += `:${options.direction === 'up' ? 'to' : 'undo:to'} --to ${options.to}`;
  }
  
  logger.info(`执行${options.direction === 'up' ? '向上' : '向下'}迁移...`);
  executeSequelizeCommand(command, options);
}

// 主函数
async function main() {
  try {
    const options = parseArgs();
    
    if (options.help) {
      showHelp();
      return;
    }
    
    logger.info(`🚀 开始本地数据库迁移 - 环境: ${options.env}`);
    
    // 获取并加载环境文件
    const envFilePath = getEnvFilePath(options.env);
    loadEnvironment(envFilePath);
    
    // 检查数据库连接
    const isConnected = await checkDatabaseConnection();
    if (!isConnected) {
      logger.error('数据库连接失败，请检查配置和数据库服务状态');
      process.exit(1);
    }
    
    // 根据选项执行相应操作
    if (options.status) {
      showMigrationStatus(options);
    } else {
      runMigration(options);
    }
    
    logger.success('🎉 操作完成！');
    
  } catch (error) {
    logger.error(`❌ 操作失败: ${error.message}`);
    process.exit(1);
  }
}

// 如果直接运行此文件，则执行主函数
if (require.main === module) {
  main();
}

module.exports = {
  main,
  parseArgs,
  loadEnvironment,
  checkDatabaseConnection,
  executeSequelizeCommand
};
