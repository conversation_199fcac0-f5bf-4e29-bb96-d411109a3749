#!/bin/bash

# 本地迁移脚本使用示例
# 演示各种迁移操作的常见用法

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
BOLD='\033[1m'
NC='\033[0m' # No Color

echo -e "${BOLD}${CYAN}本地数据库迁移脚本使用示例${NC}"
echo ""

# 显示菜单
show_menu() {
    echo -e "${BOLD}请选择要演示的操作：${NC}"
    echo ""
    echo "=== 快速迁移脚本 (推荐日常使用) ==="
    echo "1. 查看 Kaia 环境迁移状态"
    echo "2. 查看 Pharos 环境迁移状态"
    echo "3. 执行 Kaia 环境向上迁移"
    echo "4. 执行 Pharos 环境向上迁移"
    echo "5. 回滚 Kaia 环境最后一次迁移"
    echo ""
    echo "=== Node.js 迁移脚本 (功能完整) ==="
    echo "6. Kaia 环境状态查询（详细模式）"
    echo "7. Pharos 环境干运行模式"
    echo "8. 显示帮助信息"
    echo ""
    echo "=== 高级迁移管理脚本 (支持备份) ==="
    echo "9. Kaia 环境详细状态"
    echo "10. Pharos 环境迁移（自动备份）"
    echo "11. 干运行模式预览"
    echo ""
    echo "=== NPM 脚本快捷方式 ==="
    echo "12. 使用 npm 脚本执行迁移"
    echo ""
    echo "=== 测试和验证 ==="
    echo "13. 运行迁移脚本测试"
    echo ""
    echo "0. 退出"
    echo ""
}

# 执行命令并显示
run_command() {
    local cmd="$1"
    local description="$2"
    
    echo -e "${YELLOW}执行命令：${NC}${cmd}"
    echo -e "${BLUE}说明：${NC}${description}"
    echo ""
    
    read -p "按 Enter 键继续执行，或按 Ctrl+C 取消..."
    echo ""
    
    echo -e "${CYAN}--- 命令输出开始 ---${NC}"
    eval "$cmd"
    echo -e "${CYAN}--- 命令输出结束 ---${NC}"
    echo ""
    
    read -p "按 Enter 键返回菜单..."
    clear
}

# 主循环
while true; do
    show_menu
    read -p "请输入选项 (0-13): " choice
    echo ""
    
    case $choice in
        1)
            run_command "./scripts/quick-migrate.sh kaia status" \
                "使用快速迁移脚本查看 Kaia 环境的迁移状态"
            ;;
        2)
            run_command "./scripts/quick-migrate.sh pharos status" \
                "使用快速迁移脚本查看 Pharos 环境的迁移状态"
            ;;
        3)
            echo -e "${RED}警告：这将执行实际的数据库迁移操作！${NC}"
            read -p "确定要继续吗？(y/N): " confirm
            if [[ $confirm =~ ^[Yy]$ ]]; then
                run_command "./scripts/quick-migrate.sh kaia up" \
                    "执行 Kaia 环境的向上迁移"
            else
                echo "操作已取消"
                sleep 1
                clear
            fi
            ;;
        4)
            echo -e "${RED}警告：这将执行实际的数据库迁移操作！${NC}"
            read -p "确定要继续吗？(y/N): " confirm
            if [[ $confirm =~ ^[Yy]$ ]]; then
                run_command "./scripts/quick-migrate.sh pharos up" \
                    "执行 Pharos 环境的向上迁移"
            else
                echo "操作已取消"
                sleep 1
                clear
            fi
            ;;
        5)
            echo -e "${RED}警告：这将回滚最后一次迁移！${NC}"
            read -p "确定要继续吗？(y/N): " confirm
            if [[ $confirm =~ ^[Yy]$ ]]; then
                run_command "./scripts/quick-migrate.sh kaia down" \
                    "回滚 Kaia 环境的最后一次迁移"
            else
                echo "操作已取消"
                sleep 1
                clear
            fi
            ;;
        6)
            run_command "node scripts/local-migration-runner.js --env kaia --status" \
                "使用 Node.js 脚本查看 Kaia 环境的详细迁移状态"
            ;;
        7)
            run_command "node scripts/local-migration-runner.js --env pharos --dry-run" \
                "使用干运行模式预览 Pharos 环境的迁移操作"
            ;;
        8)
            run_command "node scripts/local-migration-runner.js --help" \
                "显示 Node.js 迁移脚本的帮助信息"
            ;;
        9)
            run_command "node scripts/migration-manager.js status --env kaia" \
                "使用高级管理脚本查看 Kaia 环境的详细状态"
            ;;
        10)
            echo -e "${RED}警告：这将执行实际的数据库迁移并创建备份！${NC}"
            read -p "确定要继续吗？(y/N): " confirm
            if [[ $confirm =~ ^[Yy]$ ]]; then
                run_command "node scripts/migration-manager.js up --env pharos" \
                    "执行 Pharos 环境的迁移并自动创建备份"
            else
                echo "操作已取消"
                sleep 1
                clear
            fi
            ;;
        11)
            run_command "node scripts/migration-manager.js up --env kaia --dry-run --no-backup" \
                "使用干运行模式预览迁移操作（不创建备份）"
            ;;
        12)
            echo -e "${BOLD}NPM 脚本快捷方式：${NC}"
            echo ""
            echo "npm run migrate:local:kaia          # Kaia 环境迁移"
            echo "npm run migrate:local:pharos        # Pharos 环境迁移"
            echo "npm run migrate:local:kaia:status   # Kaia 状态查询"
            echo "npm run migrate:local:pharos:status # Pharos 状态查询"
            echo "npm run migrate:local:kaia:down     # Kaia 回滚迁移"
            echo "npm run migrate:local:pharos:down   # Pharos 回滚迁移"
            echo ""
            read -p "选择要执行的 npm 脚本 (输入完整命令，如 'npm run migrate:local:kaia:status'): " npm_cmd
            if [[ -n "$npm_cmd" ]]; then
                run_command "$npm_cmd" "执行 NPM 脚本"
            else
                echo "未输入命令，返回菜单"
                sleep 1
                clear
            fi
            ;;
        13)
            run_command "node scripts/test-migration-scripts.js" \
                "运行迁移脚本测试，验证所有脚本是否正常工作"
            ;;
        0)
            echo -e "${GREEN}感谢使用本地迁移脚本！${NC}"
            exit 0
            ;;
        *)
            echo -e "${RED}无效选项，请重新选择${NC}"
            sleep 1
            clear
            ;;
    esac
done
