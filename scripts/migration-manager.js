#!/usr/bin/env node

/**
 * 高级数据库迁移管理脚本
 * 
 * 功能：
 * - 支持多环境配置
 * - 迁移状态管理
 * - 批量迁移操作
 * - 迁移回滚
 * - 安全检查
 * - 备份功能
 */

const { execSync } = require('child_process');
const path = require('path');
const fs = require('fs');
const mysql = require('mysql2/promise');
const dayjs = require('dayjs');
const utc = require('dayjs/plugin/utc');
const timezone = require('dayjs/plugin/timezone');

dayjs.extend(utc);
dayjs.extend(timezone);

// 配置
const CONFIG = {
  environments: {
    kaia: '.env.local.kaia',
    pharos: '.env.local.pharos'
  },
  backupDir: 'backups/migrations',
  maxBackups: 10
};

// 日志工具
const logger = {
  info: (msg) => console.log(`\x1b[36m[INFO]\x1b[0m ${dayjs().tz('Asia/Shanghai').format('YYYY-MM-DD HH:mm:ss')} ${msg}`),
  success: (msg) => console.log(`\x1b[32m[SUCCESS]\x1b[0m ${dayjs().tz('Asia/Shanghai').format('YYYY-MM-DD HH:mm:ss')} ${msg}`),
  warn: (msg) => console.log(`\x1b[33m[WARN]\x1b[0m ${dayjs().tz('Asia/Shanghai').format('YYYY-MM-DD HH:mm:ss')} ${msg}`),
  error: (msg) => console.log(`\x1b[31m[ERROR]\x1b[0m ${dayjs().tz('Asia/Shanghai').format('YYYY-MM-DD HH:mm:ss')} ${msg}`)
};

class MigrationManager {
  constructor(options = {}) {
    this.env = options.env || 'kaia';
    this.dryRun = options.dryRun || false;
    this.backup = options.backup !== false;
    this.rootDir = path.resolve(__dirname, '..');
    this.connection = null;
  }

  // 加载环境配置
  async loadEnvironment() {
    const envFile = CONFIG.environments[this.env];
    if (!envFile) {
      throw new Error(`不支持的环境: ${this.env}`);
    }

    const envPath = path.resolve(this.rootDir, envFile);
    if (!fs.existsSync(envPath)) {
      throw new Error(`环境文件不存在: ${envPath}`);
    }

    process.env.ENV_FILE = envFile;
    delete require.cache[require.resolve('../src/config/env')];
    require('../src/config/env');

    logger.info(`环境配置已加载: ${envFile}`);
  }

  // 创建数据库连接
  async createConnection() {
    this.connection = await mysql.createConnection({
      host: process.env.DB_HOST,
      port: process.env.DB_PORT,
      user: process.env.DB_USER,
      password: process.env.DB_PASS,
      database: process.env.DB_NAME,
      multipleStatements: true
    });

    await this.connection.ping();
    logger.success('数据库连接成功');
  }

  // 关闭数据库连接
  async closeConnection() {
    if (this.connection) {
      await this.connection.end();
      this.connection = null;
    }
  }

  // 获取迁移状态
  async getMigrationStatus() {
    try {
      // 首先检查表结构
      const [columns] = await this.connection.execute(`
        SELECT COLUMN_NAME
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'SequelizeMeta'
      `, [process.env.DB_NAME]);

      const columnNames = columns.map(col => col.COLUMN_NAME);
      const hasCreatedAt = columnNames.includes('createdAt');

      // 根据表结构选择查询语句
      let query;
      if (hasCreatedAt) {
        query = `SELECT name, createdAt FROM SequelizeMeta ORDER BY createdAt ASC`;
      } else {
        query = `SELECT name FROM SequelizeMeta ORDER BY name ASC`;
      }

      const [rows] = await this.connection.execute(query);
      return rows;
    } catch (error) {
      if (error.code === 'ER_NO_SUCH_TABLE') {
        logger.warn('SequelizeMeta 表不存在，可能还没有执行过迁移');
        return [];
      }
      throw error;
    }
  }

  // 获取可用的迁移文件
  getMigrationFiles() {
    const migrationsDir = path.resolve(this.rootDir, 'src/migrations');
    if (!fs.existsSync(migrationsDir)) {
      return [];
    }

    return fs.readdirSync(migrationsDir)
      .filter(file => file.endsWith('.js'))
      .sort();
  }

  // 创建备份
  async createBackup() {
    if (!this.backup) {
      return null;
    }

    const backupDir = path.resolve(this.rootDir, CONFIG.backupDir);
    if (!fs.existsSync(backupDir)) {
      fs.mkdirSync(backupDir, { recursive: true });
    }

    const timestamp = dayjs().format('YYYYMMDD_HHmmss');
    const backupFile = path.resolve(backupDir, `${this.env}_${timestamp}.sql`);

    logger.info('创建数据库备份...');

    try {
      const command = `mysqldump -h ${process.env.DB_HOST} -P ${process.env.DB_PORT} -u ${process.env.DB_USER} -p${process.env.DB_PASS} ${process.env.DB_NAME} > ${backupFile}`;
      
      if (!this.dryRun) {
        execSync(command, { stdio: 'inherit' });
        logger.success(`备份创建成功: ${backupFile}`);
      } else {
        logger.info(`[DRY RUN] 将创建备份: ${backupFile}`);
      }

      // 清理旧备份
      this.cleanupOldBackups(backupDir);

      return backupFile;
    } catch (error) {
      logger.error(`备份创建失败: ${error.message}`);
      return null;
    }
  }

  // 清理旧备份
  cleanupOldBackups(backupDir) {
    try {
      const files = fs.readdirSync(backupDir)
        .filter(file => file.startsWith(`${this.env}_`) && file.endsWith('.sql'))
        .map(file => ({
          name: file,
          path: path.resolve(backupDir, file),
          mtime: fs.statSync(path.resolve(backupDir, file)).mtime
        }))
        .sort((a, b) => b.mtime - a.mtime);

      if (files.length > CONFIG.maxBackups) {
        const filesToDelete = files.slice(CONFIG.maxBackups);
        filesToDelete.forEach(file => {
          fs.unlinkSync(file.path);
          logger.info(`删除旧备份: ${file.name}`);
        });
      }
    } catch (error) {
      logger.warn(`清理旧备份失败: ${error.message}`);
    }
  }

  // 执行 Sequelize 命令
  executeSequelizeCommand(command) {
    const configPath = path.resolve(this.rootDir, 'config/config.js');
    const fullCommand = `npx sequelize-cli ${command} --config ${configPath}`;

    logger.info(`执行命令: ${fullCommand}`);

    if (this.dryRun) {
      logger.warn('[DRY RUN] 命令不会实际执行');
      return;
    }

    try {
      execSync(fullCommand, {
        cwd: this.rootDir,
        stdio: 'inherit',
        env: {
          ...process.env,
          NODE_ENV: process.env.NODE_ENV || 'development'
        }
      });
      logger.success('命令执行成功');
    } catch (error) {
      logger.error(`命令执行失败: ${error.message}`);
      throw error;
    }
  }

  // 显示状态
  async showStatus() {
    logger.info('获取迁移状态...');
    
    const executedMigrations = await this.getMigrationStatus();
    const availableMigrations = this.getMigrationFiles();

    console.log('\n=== 迁移状态 ===');
    console.log(`环境: ${this.env}`);
    console.log(`数据库: ${process.env.DB_NAME}@${process.env.DB_HOST}:${process.env.DB_PORT}`);
    console.log(`已执行迁移: ${executedMigrations.length}`);
    console.log(`可用迁移: ${availableMigrations.length}`);

    if (executedMigrations.length > 0) {
      console.log('\n已执行的迁移:');
      executedMigrations.forEach(migration => {
        const timeStr = migration.createdAt ?
          ` (${dayjs(migration.createdAt).format('YYYY-MM-DD HH:mm:ss')})` : '';
        console.log(`  ✅ ${migration.name}${timeStr}`);
      });
    }

    const pendingMigrations = availableMigrations.filter(file => 
      !executedMigrations.some(executed => executed.name === file)
    );

    if (pendingMigrations.length > 0) {
      console.log('\n待执行的迁移:');
      pendingMigrations.forEach(migration => {
        console.log(`  ⏳ ${migration}`);
      });
    } else {
      console.log('\n✅ 所有迁移都已执行');
    }
  }

  // 执行向上迁移
  async migrateUp(to = null) {
    logger.info('开始执行向上迁移...');
    
    if (this.backup) {
      await this.createBackup();
    }

    let command = 'db:migrate';
    if (to) {
      command += ` --to ${to}`;
    }

    this.executeSequelizeCommand(command);
    logger.success('向上迁移完成');
  }

  // 执行向下迁移
  async migrateDown(to = null) {
    logger.info('开始执行向下迁移...');
    
    if (this.backup) {
      await this.createBackup();
    }

    let command = 'db:migrate:undo';
    if (to) {
      command += ` --to ${to}`;
    }

    this.executeSequelizeCommand(command);
    logger.success('向下迁移完成');
  }

  // 重置所有迁移
  async reset() {
    logger.warn('开始重置所有迁移...');
    
    if (this.backup) {
      await this.createBackup();
    }

    this.executeSequelizeCommand('db:migrate:undo:all');
    logger.success('迁移重置完成');
  }

  // 运行主逻辑
  async run(action, options = {}) {
    try {
      await this.loadEnvironment();
      await this.createConnection();

      switch (action) {
        case 'status':
          await this.showStatus();
          break;
        case 'up':
          await this.migrateUp(options.to);
          break;
        case 'down':
          await this.migrateDown(options.to);
          break;
        case 'reset':
          await this.reset();
          break;
        default:
          throw new Error(`未知操作: ${action}`);
      }

    } finally {
      await this.closeConnection();
    }
  }
}

// 命令行接口
async function main() {
  const args = process.argv.slice(2);
  
  if (args.includes('--help') || args.includes('-h')) {
    console.log(`
数据库迁移管理脚本

使用方法:
  node scripts/migration-manager.js <action> [options]

操作:
  status    显示迁移状态
  up        执行向上迁移
  down      执行向下迁移
  reset     重置所有迁移

选项:
  --env <kaia|pharos>   指定环境 (默认: kaia)
  --to <migration>      迁移到指定版本
  --dry-run            干运行模式
  --no-backup          不创建备份
  --help               显示帮助

示例:
  node scripts/migration-manager.js status --env kaia
  node scripts/migration-manager.js up --env pharos
  node scripts/migration-manager.js down --to 20250804000000
    `);
    return;
  }

  const action = args[0] || 'status';
  const options = {
    env: 'kaia',
    dryRun: false,
    backup: true,
    to: null
  };

  // 解析参数
  for (let i = 1; i < args.length; i++) {
    switch (args[i]) {
      case '--env':
        options.env = args[++i];
        break;
      case '--to':
        options.to = args[++i];
        break;
      case '--dry-run':
        options.dryRun = true;
        break;
      case '--no-backup':
        options.backup = false;
        break;
    }
  }

  const manager = new MigrationManager(options);
  await manager.run(action, options);
}

if (require.main === module) {
  main().catch(error => {
    logger.error(`执行失败: ${error.message}`);
    process.exit(1);
  });
}

module.exports = MigrationManager;
