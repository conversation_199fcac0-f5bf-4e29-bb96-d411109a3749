#!/bin/bash

# 快速本地迁移脚本
# 使用方法: ./scripts/quick-migrate.sh [kaia|pharos] [up|down|status]

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${CYAN}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

# 显示帮助信息
show_help() {
    echo -e "${BLUE}快速本地迁移脚本${NC}"
    echo ""
    echo "使用方法:"
    echo "  ./scripts/quick-migrate.sh [环境] [操作]"
    echo ""
    echo "环境选项:"
    echo "  kaia     - 使用 .env.local.kaia 配置"
    echo "  pharos   - 使用 .env.local.pharos 配置"
    echo ""
    echo "操作选项:"
    echo "  up       - 执行向上迁移（默认）"
    echo "  down     - 执行向下迁移"
    echo "  status   - 显示迁移状态"
    echo "  undo     - 撤销最后一次迁移"
    echo ""
    echo "示例:"
    echo "  ./scripts/quick-migrate.sh kaia up"
    echo "  ./scripts/quick-migrate.sh pharos status"
    echo "  ./scripts/quick-migrate.sh kaia down"
    echo ""
}

# 检查参数
ENV=${1:-"kaia"}
ACTION=${2:-"up"}

# 验证环境参数
if [[ "$ENV" != "kaia" && "$ENV" != "pharos" ]]; then
    log_error "无效的环境参数: $ENV"
    log_info "支持的环境: kaia, pharos"
    show_help
    exit 1
fi

# 验证操作参数
if [[ "$ACTION" != "up" && "$ACTION" != "down" && "$ACTION" != "status" && "$ACTION" != "undo" ]]; then
    log_error "无效的操作参数: $ACTION"
    log_info "支持的操作: up, down, status, undo"
    show_help
    exit 1
fi

# 显示帮助
if [[ "$1" == "--help" || "$1" == "-h" ]]; then
    show_help
    exit 0
fi

# 获取脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
ROOT_DIR="$(cd "$SCRIPT_DIR/.." && pwd)"

# 设置环境文件
ENV_FILE=".env.local.$ENV"
ENV_PATH="$ROOT_DIR/$ENV_FILE"

log_info "🚀 开始本地数据库迁移"
log_info "环境: $ENV"
log_info "操作: $ACTION"
log_info "配置文件: $ENV_FILE"

# 检查环境文件是否存在
if [[ ! -f "$ENV_PATH" ]]; then
    log_error "环境文件不存在: $ENV_PATH"
    exit 1
fi

log_success "环境文件检查通过"

# 切换到项目根目录
cd "$ROOT_DIR"

# 设置环境变量
export ENV_FILE="$ENV_FILE"
export NODE_ENV="development"

log_info "设置环境变量: ENV_FILE=$ENV_FILE"

# 检查 Node.js 和 npm
if ! command -v node &> /dev/null; then
    log_error "Node.js 未安装或不在 PATH 中"
    exit 1
fi

if ! command -v npm &> /dev/null; then
    log_error "npm 未安装或不在 PATH 中"
    exit 1
fi

log_success "Node.js 和 npm 检查通过"

# 检查 sequelize-cli
if ! npm list sequelize-cli &> /dev/null; then
    log_warn "sequelize-cli 未安装，正在安装..."
    npm install sequelize-cli
fi

log_success "sequelize-cli 检查通过"

# 测试数据库连接
log_info "测试数据库连接..."
if node -e "
const { loadEnvConfig } = require('./src/config/env');
loadEnvConfig('$ENV_FILE');
const mysql = require('mysql2/promise');
(async () => {
  try {
    const connection = await mysql.createConnection({
      host: process.env.DB_HOST,
      port: process.env.DB_PORT,
      user: process.env.DB_USER,
      password: process.env.DB_PASS,
      database: process.env.DB_NAME,
      connectTimeout: 10000
    });
    await connection.ping();
    await connection.end();
    console.log('数据库连接成功');
  } catch (error) {
    console.error('数据库连接失败:', error.message);
    process.exit(1);
  }
})();
"; then
    log_success "数据库连接测试通过"
else
    log_error "数据库连接测试失败"
    exit 1
fi

# 执行相应的操作
case "$ACTION" in
    "up")
        log_info "执行向上迁移..."
        npx sequelize-cli db:migrate --config config/config.js
        log_success "向上迁移完成"
        ;;
    "down")
        log_info "执行向下迁移..."
        npx sequelize-cli db:migrate:undo --config config/config.js
        log_success "向下迁移完成"
        ;;
    "undo")
        log_info "撤销最后一次迁移..."
        npx sequelize-cli db:migrate:undo --config config/config.js
        log_success "迁移撤销完成"
        ;;
    "status")
        log_info "获取迁移状态..."
        npx sequelize-cli db:migrate:status --config config/config.js
        ;;
esac

log_success "🎉 操作完成！"

# 显示当前迁移状态
if [[ "$ACTION" != "status" ]]; then
    echo ""
    log_info "当前迁移状态:"
    npx sequelize-cli db:migrate:status --config config/config.js
fi
