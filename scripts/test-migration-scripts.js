#!/usr/bin/env node

/**
 * 测试本地迁移脚本
 * 验证所有迁移脚本是否能正常工作
 */

const { execSync } = require('child_process');
const path = require('path');
const dayjs = require('dayjs');
const utc = require('dayjs/plugin/utc');
const timezone = require('dayjs/plugin/timezone');

dayjs.extend(utc);
dayjs.extend(timezone);

// 日志工具
const logger = {
  info: (msg) => console.log(`\x1b[36m[INFO]\x1b[0m ${dayjs().tz('Asia/Shanghai').format('YYYY-MM-DD HH:mm:ss')} ${msg}`),
  success: (msg) => console.log(`\x1b[32m[SUCCESS]\x1b[0m ${dayjs().tz('Asia/Shanghai').format('YYYY-MM-DD HH:mm:ss')} ${msg}`),
  warn: (msg) => console.log(`\x1b[33m[WARN]\x1b[0m ${dayjs().tz('Asia/Shanghai').format('YYYY-MM-DD HH:mm:ss')} ${msg}`),
  error: (msg) => console.log(`\x1b[31m[ERROR]\x1b[0m ${dayjs().tz('Asia/Shanghai').format('YYYY-MM-DD HH:mm:ss')} ${msg}`)
};

// 测试配置
const TESTS = [
  {
    name: '快速迁移脚本 - Kaia 状态查询',
    command: './scripts/quick-migrate.sh kaia status',
    timeout: 30000
  },
  {
    name: '快速迁移脚本 - Pharos 状态查询',
    command: './scripts/quick-migrate.sh pharos status',
    timeout: 30000
  },
  {
    name: 'Node.js 迁移脚本 - Kaia 状态查询',
    command: 'node scripts/local-migration-runner.js --env kaia --status',
    timeout: 30000
  },
  {
    name: 'Node.js 迁移脚本 - Pharos 状态查询',
    command: 'node scripts/local-migration-runner.js --env pharos --status',
    timeout: 30000
  },
  {
    name: '高级迁移管理脚本 - Kaia 状态查询',
    command: 'node scripts/migration-manager.js status --env kaia',
    timeout: 30000
  },
  {
    name: '高级迁移管理脚本 - Pharos 状态查询',
    command: 'node scripts/migration-manager.js status --env pharos',
    timeout: 30000
  },
  {
    name: 'Node.js 迁移脚本 - 干运行模式',
    command: 'node scripts/local-migration-runner.js --env kaia --dry-run',
    timeout: 30000
  },
  {
    name: '高级迁移管理脚本 - 干运行模式',
    command: 'node scripts/migration-manager.js up --env kaia --dry-run --no-backup',
    timeout: 30000
  }
];

// 执行单个测试
async function runTest(test) {
  logger.info(`开始测试: ${test.name}`);
  
  try {
    const startTime = Date.now();
    
    const result = execSync(test.command, {
      cwd: path.resolve(__dirname, '..'),
      timeout: test.timeout,
      stdio: 'pipe',
      encoding: 'utf8'
    });
    
    const duration = Date.now() - startTime;
    logger.success(`测试通过: ${test.name} (耗时: ${duration}ms)`);
    
    return {
      name: test.name,
      success: true,
      duration,
      output: result
    };
    
  } catch (error) {
    logger.error(`测试失败: ${test.name}`);
    logger.error(`错误信息: ${error.message}`);
    
    return {
      name: test.name,
      success: false,
      error: error.message,
      output: error.stdout || error.stderr || ''
    };
  }
}

// 运行所有测试
async function runAllTests() {
  logger.info('🚀 开始测试本地迁移脚本');
  
  const results = [];
  let passedCount = 0;
  let failedCount = 0;
  
  for (const test of TESTS) {
    const result = await runTest(test);
    results.push(result);
    
    if (result.success) {
      passedCount++;
    } else {
      failedCount++;
    }
    
    // 测试之间稍作停顿
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  // 输出测试结果摘要
  console.log('\n' + '='.repeat(60));
  console.log('测试结果摘要');
  console.log('='.repeat(60));
  
  logger.info(`总测试数: ${TESTS.length}`);
  logger.success(`通过: ${passedCount}`);
  
  if (failedCount > 0) {
    logger.error(`失败: ${failedCount}`);
  }
  
  console.log('\n详细结果:');
  results.forEach((result, index) => {
    const status = result.success ? '✅ 通过' : '❌ 失败';
    const duration = result.duration ? ` (${result.duration}ms)` : '';
    console.log(`${index + 1}. ${status} ${result.name}${duration}`);
    
    if (!result.success) {
      console.log(`   错误: ${result.error}`);
    }
  });
  
  if (failedCount === 0) {
    logger.success('🎉 所有测试都通过了！');
    process.exit(0);
  } else {
    logger.error('❌ 部分测试失败，请检查错误信息');
    process.exit(1);
  }
}

// 显示帮助信息
function showHelp() {
  console.log(`
本地迁移脚本测试工具

使用方法:
  node scripts/test-migration-scripts.js [选项]

选项:
  --help    显示帮助信息

功能:
  - 测试所有迁移脚本的基本功能
  - 验证环境配置加载
  - 检查数据库连接
  - 确保脚本能正常执行

测试项目:
  1. 快速迁移脚本状态查询 (kaia/pharos)
  2. Node.js 迁移脚本状态查询 (kaia/pharos)
  3. 高级迁移管理脚本状态查询 (kaia/pharos)
  4. 干运行模式测试

注意:
  - 测试需要数据库服务正在运行
  - 测试不会修改数据库，只进行状态查询
  - 如果测试失败，请检查环境配置和数据库连接
  `);
}

// 主函数
async function main() {
  const args = process.argv.slice(2);
  
  if (args.includes('--help') || args.includes('-h')) {
    showHelp();
    return;
  }
  
  try {
    await runAllTests();
  } catch (error) {
    logger.error(`测试执行失败: ${error.message}`);
    process.exit(1);
  }
}

// 如果直接运行此文件，则执行主函数
if (require.main === module) {
  main();
}

module.exports = {
  runTest,
  runAllTests
};
