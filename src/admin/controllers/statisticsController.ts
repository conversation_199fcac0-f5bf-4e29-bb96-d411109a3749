import { Request, Response } from 'express';
import { UserStatisticsService } from '../services/userStatisticsService';
import { GameProgressStatisticsService } from '../services/gameProgressStatisticsService';
import { PurchaseStatisticsService } from '../services/purchaseStatisticsService';
import { TaskStatisticsService } from '../services/taskStatisticsService';
import { SocialStatisticsService } from '../services/socialStatisticsService';
import { RewardStatisticsService } from '../services/rewardStatisticsService';
import { ItemStatisticsService } from '../services/itemStatisticsService';
import { ComprehensiveStatistics, DateRangeQuery, StatisticsExportData } from '../types/statistics';
import { logger, formatError } from '../../utils/logger';
import * as ExcelJS from 'exceljs';

export class StatisticsController {
  /**
   * 获取综合统计仪表板
   */
  public static async getDashboard(req: Request, res: Response): Promise<void> {
    try {
      const dateRange: DateRangeQuery = {
        startDate: req.query.startDate ? new Date(req.query.startDate as string) : undefined,
        endDate: req.query.endDate ? new Date(req.query.endDate as string) : undefined,
      };

      // 并行获取所有统计数据
      const [
        userBehavior,
        gameProgress,
        itemUsage,
        purchase,
        task,
        social,
        reward,
      ] = await Promise.all([
        UserStatisticsService.getUserBehaviorStatistics(dateRange),
        GameProgressStatisticsService.getGameProgressStatistics(),
        ItemStatisticsService.getItemUsageStatistics(dateRange),
        PurchaseStatisticsService.getPurchaseStatistics(dateRange),
        TaskStatisticsService.getTaskStatistics(dateRange),
        SocialStatisticsService.getSocialStatistics(),
        RewardStatisticsService.getRewardStatistics(dateRange),
      ]);

      const statistics: ComprehensiveStatistics = {
        userBehavior,
        gameProgress,
        itemUsage,
        purchase,
        task,
        social,
        reward,
        generatedAt: new Date(),
      };

      res.status(200).json({
        success: true,
        data: statistics,
      });
    } catch (error) {
      logger.error('获取综合统计失败:', formatError(error));
      res.status(500).json({
        success: false,
        error: '获取统计数据失败',
      });
    }
  }

  /**
   * 获取用户统计
   */
  public static async getUserStatistics(req: Request, res: Response): Promise<void> {
    try {
      const dateRange: DateRangeQuery = {
        startDate: req.query.startDate ? new Date(req.query.startDate as string) : undefined,
        endDate: req.query.endDate ? new Date(req.query.endDate as string) : undefined,
      };

      const statistics = await UserStatisticsService.getUserBehaviorStatistics(dateRange);
      const growthTrend = await UserStatisticsService.getUserGrowthTrend();
      const activeTrend = await UserStatisticsService.getActiveUserTrend();

      res.status(200).json({
        success: true,
        data: {
          statistics,
          trends: {
            growth: growthTrend,
            active: activeTrend,
          },
        },
      });
    } catch (error) {
      logger.error('获取用户统计失败:', formatError(error));
      res.status(500).json({
        success: false,
        error: '获取用户统计失败',
      });
    }
  }

  /**
   * 获取游戏进度统计
   */
  public static async getProgressStatistics(req: Request, res: Response): Promise<void> {
    try {
      const statistics = await GameProgressStatisticsService.getGameProgressStatistics();
      const detailed = await GameProgressStatisticsService.getDetailedLevelDistribution();
      const overview = await GameProgressStatisticsService.getPlayerProgressOverview();

      res.status(200).json({
        success: true,
        data: {
          statistics,
          detailed,
          overview,
        },
      });
    } catch (error) {
      logger.error('获取游戏进度统计失败:', formatError(error));
      res.status(500).json({
        success: false,
        error: '获取游戏进度统计失败',
      });
    }
  }

  /**
   * 获取经济系统统计
   */
  public static async getEconomyStatistics(req: Request, res: Response): Promise<void> {
    try {
      const dateRange: DateRangeQuery = {
        startDate: req.query.startDate ? new Date(req.query.startDate as string) : undefined,
        endDate: req.query.endDate ? new Date(req.query.endDate as string) : undefined,
      };

      const purchase = await PurchaseStatisticsService.getPurchaseStatistics(dateRange);
      const revenueTrend = await PurchaseStatisticsService.getRevenueTrend();
      const revenueDistribution = await PurchaseStatisticsService.getRevenueDistribution();
      const paymentMethods = await PurchaseStatisticsService.getPaymentMethodStats();

      const reward = await RewardStatisticsService.getRewardStatistics(dateRange);
      const resourceDistribution = await RewardStatisticsService.getResourceDistribution();
      const rewardTrend = await RewardStatisticsService.getDailyRewardTrend();

      res.status(200).json({
        success: true,
        data: {
          purchase: {
            statistics: purchase,
            trends: revenueTrend,
            distribution: revenueDistribution,
            paymentMethods,
          },
          rewards: {
            statistics: reward,
            distribution: resourceDistribution,
            trends: rewardTrend,
          },
        },
      });
    } catch (error) {
      logger.error('获取经济系统统计失败:', formatError(error));
      res.status(500).json({
        success: false,
        error: '获取经济系统统计失败',
      });
    }
  }

  /**
   * 获取社交系统统计
   */
  public static async getSocialStatistics(req: Request, res: Response): Promise<void> {
    try {
      const statistics = await SocialStatisticsService.getSocialStatistics();
      const detailed = await SocialStatisticsService.getDetailedInvitationDistribution();
      const chains = await SocialStatisticsService.getReferralChains();
      const effectiveness = await SocialStatisticsService.getInvitationEffectiveness();

      res.status(200).json({
        success: true,
        data: {
          statistics,
          detailed,
          chains,
          effectiveness,
        },
      });
    } catch (error) {
      logger.error('获取社交系统统计失败:', formatError(error));
      res.status(500).json({
        success: false,
        error: '获取社交系统统计失败',
      });
    }
  }

  /**
   * 导出统计数据到Excel
   */
  public static async exportStatistics(req: Request, res: Response): Promise<void> {
    try {
      const dateRange: DateRangeQuery = {
        startDate: req.query.startDate ? new Date(req.query.startDate as string) : undefined,
        endDate: req.query.endDate ? new Date(req.query.endDate as string) : undefined,
      };

      // 获取所有统计数据
      const [
        userBehavior,
        gameProgress,
        itemUsage,
        purchase,
        task,
        social,
        reward,
      ] = await Promise.all([
        UserStatisticsService.getUserBehaviorStatistics(dateRange),
        GameProgressStatisticsService.getGameProgressStatistics(),
        ItemStatisticsService.getItemUsageStatistics(dateRange),
        PurchaseStatisticsService.getPurchaseStatistics(dateRange),
        TaskStatisticsService.getTaskStatistics(dateRange),
        SocialStatisticsService.getSocialStatistics(),
        RewardStatisticsService.getRewardStatistics(dateRange),
      ]);

      // 创建Excel工作簿
      const workbook = new ExcelJS.Workbook();
      
      // 创建主要统计表
      const mainSheet = workbook.addWorksheet('综合统计');
      const exportData: StatisticsExportData[] = [];
      let index = 1;

      // 1. 用户行为统计
      exportData.push(
        { 序号: index++, 统计项: '每日游戏启动次数', 数值: userBehavior.dailyAppOpens },
        { 序号: index++, 统计项: '每日平均游玩时长(分钟)', 数值: userBehavior.averagePlayTime },
        { 序号: index++, 统计项: '每日新增用户数', 数值: userBehavior.dailyNewUsers },
        { 序号: index++, 统计项: '7日用户总量', 数值: userBehavior.sevenDayUsers },
        { 序号: index++, 统计项: '首日留存率(%)', 数值: userBehavior.firstDayRetention },
        { 序号: index++, 统计项: '七日留存率(%)', 数值: userBehavior.sevenDayRetention },
      );

      // 2. 游戏进度统计 - 区域解锁
      for (const unlock of gameProgress.farmAreaUnlocks) {
        exportData.push({
          序号: index++,
          统计项: `解锁区域${unlock.area}的人数`,
          数值: unlock.unlockedCount,
        });
      }

      // 3. 流水线等级分布
      for (const level of gameProgress.deliveryLineLevelDistribution) {
        exportData.push({
          序号: index++,
          统计项: `升级流水线到${level.level}级的人数`,
          数值: level.count,
        });
      }

      // 4. 道具使用统计
      for (const item of itemUsage.items) {
        exportData.push(
          { 序号: index++, 统计项: `${item.itemName}使用人数`, 数值: item.totalUsers },
          { 序号: index++, 统计项: `${item.itemName}使用次数`, 数值: item.totalUsageCount },
        );
      }

      // 5. 购买统计
      exportData.push({ 序号: index++, 统计项: '玩家总充值数(USD)', 数值: purchase.totalRevenue });

      // 6. 任务统计
      exportData.push({ 序号: index++, 统计项: '玩家完成任务总次数', 数值: task.totalTaskCompletions });
      for (const taskItem of task.taskCompletions) {
        exportData.push({
          序号: index++,
          统计项: `完成任务${taskItem.taskId}(${taskItem.taskName})的人数`,
          数值: taskItem.completedUsers,
        });
      }

      // 7. 社交统计
      exportData.push({ 序号: index++, 统计项: '玩家邀请好友的总数', 数值: social.totalInvitations });
      for (const dist of social.invitationDistribution) {
        exportData.push({
          序号: index++,
          统计项: `成功邀请到好友${dist.inviteCount}人的人数`,
          数值: dist.userCount,
        });
      }

      // 8. 奖励统计
      exportData.push(
        { 序号: index++, 统计项: '玩家获取的总箱子数', 数值: reward.totalChestsObtained },
        { 序号: index++, 统计项: '每天领取每日宝箱的人数', 数值: reward.dailyChestClaimUsers },
        { 序号: index++, 统计项: '玩家获取钻石总数', 数值: reward.totalDiamondsEarned },
        { 序号: index++, 统计项: '玩家获取GEM总数', 数值: reward.totalGemsEarned },
      );

      // 添加数据到工作表
      mainSheet.columns = [
        { header: '序号', key: '序号', width: 10 },
        { header: '统计项', key: '统计项', width: 50 },
        { header: '数值', key: '数值', width: 20 },
        { header: '备注', key: '备注', width: 30 },
      ];
      
      mainSheet.addRows(exportData);

      // 设置响应头
      res.setHeader(
        'Content-Type',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      );
      res.setHeader(
        'Content-Disposition',
        `attachment; filename=statistics-${new Date().toISOString().split('T')[0]}.xlsx`
      );

      // 发送Excel文件
      await workbook.xlsx.write(res);
      res.end();
    } catch (error) {
      logger.error('导出统计数据失败:', formatError(error));
      res.status(500).json({
        success: false,
        error: '导出统计数据失败',
      });
    }
  }

  /**
   * 获取新任务系统详细统计
   */
  public static async getNewTaskSystemStats(req: Request, res: Response): Promise<void> {
    try {
      const [
        statusDistribution,
        progressStats,
        systemComparison
      ] = await Promise.all([
        TaskStatisticsService.getNewTaskStatusDistribution(),
        TaskStatisticsService.getNewTaskProgressStats(),
        TaskStatisticsService.getTaskSystemComparison()
      ]);

      res.json({
        success: true,
        data: {
          statusDistribution,
          progressStats,
          systemComparison,
          generatedAt: new Date()
        },
      });
    } catch (error) {
      logger.error('获取新任务系统统计失败:', formatError(error));
      res.status(500).json({
        success: false,
        error: '获取新任务系统统计失败',
      });
    }
  }

  /**
   * 获取任务系统对比统计
   */
  public static async getTaskSystemComparison(req: Request, res: Response): Promise<void> {
    try {
      const comparison = await TaskStatisticsService.getTaskSystemComparison();

      res.json({
        success: true,
        data: comparison,
      });
    } catch (error) {
      logger.error('获取任务系统对比统计失败:', formatError(error));
      res.status(500).json({
        success: false,
        error: '获取任务系统对比统计失败',
      });
    }
  }
}
