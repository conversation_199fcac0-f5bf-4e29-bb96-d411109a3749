import { Router } from 'express';
import { StatisticsController } from '../controllers/statisticsController';
import { adminAuthMiddleware } from '../../middlewares/adminAuth';
import { logger, formatError } from '../../utils/logger';

const router = Router();

// 应用管理员认证中间件到所有路由
router.use(adminAuthMiddleware);

// 记录所有统计API访问
router.use((req, res, next) => {
  logger.info(`管理员统计API访问: ${req.method} ${req.path}`, {
    adminId: (req as any).admin?.id,
    query: req.query,
  });
  next();
});

/**
 * @route GET /api/admin/statistics/dashboard
 * @desc 获取综合统计仪表板
 * @query startDate - 开始日期 (可选)
 * @query endDate - 结束日期 (可选)
 */
router.get('/dashboard', StatisticsController.getDashboard);

/**
 * @route GET /api/admin/statistics/users
 * @desc 获取用户统计详情
 * @query startDate - 开始日期 (可选)
 * @query endDate - 结束日期 (可选)
 */
router.get('/users', StatisticsController.getUserStatistics);

/**
 * @route GET /api/admin/statistics/progress
 * @desc 获取游戏进度统计
 */
router.get('/progress', StatisticsController.getProgressStatistics);

/**
 * @route GET /api/admin/statistics/economy
 * @desc 获取经济系统统计
 * @query startDate - 开始日期 (可选)
 * @query endDate - 结束日期 (可选)
 */
router.get('/economy', StatisticsController.getEconomyStatistics);

/**
 * @route GET /api/admin/statistics/social
 * @desc 获取社交系统统计
 */
router.get('/social', StatisticsController.getSocialStatistics);

/**
 * @route GET /api/admin/statistics/export
 * @desc 导出统计数据到Excel
 * @query startDate - 开始日期 (可选)
 * @query endDate - 结束日期 (可选)
 */
router.get('/export', StatisticsController.exportStatistics);

/**
 * @route GET /api/admin/statistics/new-task-system
 * @desc 获取新任务系统详细统计
 */
router.get('/new-task-system', StatisticsController.getNewTaskSystemStats);

/**
 * @route GET /api/admin/statistics/task-system-comparison
 * @desc 获取任务系统对比统计
 */
router.get('/task-system-comparison', StatisticsController.getTaskSystemComparison);

// 以下是更细分的统计API

/**
 * @route GET /api/admin/statistics/trends/users
 * @desc 获取用户增长趋势
 * @query days - 天数 (默认30)
 */
router.get('/trends/users', async (req, res) => {
  try {
    const { UserStatisticsService } = await import('../services/userStatisticsService');
    const days = parseInt(req.query.days as string) || 30;
    const growthTrend = await UserStatisticsService.getUserGrowthTrend(days);
    const activeTrend = await UserStatisticsService.getActiveUserTrend(days);
    
    res.json({
      success: true,
      data: {
        growth: growthTrend,
        active: activeTrend,
      },
    });
  } catch (error) {
    logger.error('获取用户趋势失败:', formatError(error));
    res.status(500).json({ success: false, error: '获取数据失败' });
  }
});

/**
 * @route GET /api/admin/statistics/trends/revenue
 * @desc 获取收入趋势
 * @query days - 天数 (默认30)
 */
router.get('/trends/revenue', async (req, res) => {
  try {
    const { PurchaseStatisticsService } = await import('../services/purchaseStatisticsService');
    const days = parseInt(req.query.days as string) || 30;
    const trend = await PurchaseStatisticsService.getRevenueTrend(days);
    
    res.json({
      success: true,
      data: trend,
    });
  } catch (error) {
    logger.error('获取收入趋势失败:', formatError(error));
    res.status(500).json({ success: false, error: '获取数据失败' });
  }
});

/**
 * @route GET /api/admin/statistics/trends/tasks
 * @desc 获取任务完成趋势
 * @query days - 天数 (默认30)
 */
router.get('/trends/tasks', async (req, res) => {
  try {
    const { TaskStatisticsService } = await import('../services/taskStatisticsService');
    const days = parseInt(req.query.days as string) || 30;
    const trend = await TaskStatisticsService.getDailyTaskCompletionTrend(days);
    
    res.json({
      success: true,
      data: trend,
    });
  } catch (error) {
    logger.error('获取任务趋势失败:', formatError(error));
    res.status(500).json({ success: false, error: '获取数据失败' });
  }
});

/**
 * @route GET /api/admin/statistics/trends/rewards
 * @desc 获取奖励趋势
 * @query days - 天数 (默认30)
 */
router.get('/trends/rewards', async (req, res) => {
  try {
    const { RewardStatisticsService } = await import('../services/rewardStatisticsService');
    const days = parseInt(req.query.days as string) || 30;
    const trend = await RewardStatisticsService.getDailyRewardTrend(days);
    
    res.json({
      success: true,
      data: trend,
    });
  } catch (error) {
    logger.error('获取奖励趋势失败:', formatError(error));
    res.status(500).json({ success: false, error: '获取数据失败' });
  }
});

/**
 * @route GET /api/admin/statistics/trends/items
 * @desc 获取道具使用趋势
 * @query days - 天数 (默认30)
 */
router.get('/trends/items', async (req, res) => {
  try {
    const { ItemStatisticsService } = await import('../services/itemStatisticsService');
    const days = parseInt(req.query.days as string) || 30;
    const trend = await ItemStatisticsService.getBoosterUsageTrend(days);
    
    res.json({
      success: true,
      data: trend,
    });
  } catch (error) {
    logger.error('获取道具趋势失败:', formatError(error));
    res.status(500).json({ success: false, error: '获取数据失败' });
  }
});

export default router;
