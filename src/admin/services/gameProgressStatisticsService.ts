import { Op, QueryTypes } from 'sequelize';
import { FarmPlot } from '../../models/FarmPlot';
import { DeliveryLine } from '../../models/DeliveryLine';
import { GameProgressStatistics, LevelDistributionQuery } from '../types/statistics';
import { logger, formatError } from '../../utils/logger';
import { sequelize } from '../../config/db';

export class GameProgressStatisticsService {
  /**
   * 获取游戏进度统计数据
   */
  public static async getGameProgressStatistics(): Promise<GameProgressStatistics> {
    try {
      // 目标等级数组
      const targetLevels = [10, 20, 25, 30, 35, 40, 45, 50];
      
      // 1. 牧场区域解锁统计 (区域2-20)
      const farmAreaUnlocks = await this.getFarmAreaUnlocks();
      
      // 2. 牧场区域等级分布
      const farmAreaLevelDistribution = await this.getFarmAreaLevelDistribution(targetLevels);
      
      // 3. 流水线等级分布
      const deliveryLineLevelDistribution = await this.getDeliveryLineLevelDistribution(targetLevels);

      return {
        farmAreaUnlocks,
        farmAreaLevelDistribution,
        deliveryLineLevelDistribution,
      };
    } catch (error) {
      logger.error('获取游戏进度统计失败:', formatError(error));
      throw error;
    }
  }

  /**
   * 获取牧场区域解锁统计（区域2-20的解锁人数）
   */
  private static async getFarmAreaUnlocks(): Promise<{ area: number; unlockedCount: number }[]> {
    try {
      const unlocksData = [];
      
      // 统计区域2-20的解锁情况
      for (let area = 2; area <= 20; area++) {
        const unlockedCount = await FarmPlot.count({
          where: {
            plotNumber: area,
            isUnlocked: true,
          },
        });
        
        unlocksData.push({
          area,
          unlockedCount,
        });
      }
      
      return unlocksData;
    } catch (error) {
      logger.error('获取牧场区域解锁统计失败:', formatError(error));
      return [];
    }
  }

  /**
   * 获取牧场区域等级分布（每个区域达到特定等级的人数）
   */
  private static async getFarmAreaLevelDistribution(
    targetLevels: number[]
  ): Promise<any[]> {
    try {
      const distribution = [];
      
      // 对每个牧场区域（1-20）进行统计
      for (let area = 1; area <= 20; area++) {
        const levelDistribution = [];
        
        // 统计每个目标等级的人数
        for (const level of targetLevels) {
          const count = await FarmPlot.count({
            where: {
              plotNumber: area,
              level: {
                [Op.gte]: level,  // 达到或超过该等级
              },
              isUnlocked: true,
            },
          });
          
          levelDistribution.push({
            level,
            count,
          });
        }
        
        distribution.push({
          area,
          levelDistribution,
        });
      }
      
      return distribution;
    } catch (error) {
      logger.error('获取牧场区域等级分布失败:', formatError(error));
      return [];
    }
  }

  /**
   * 获取流水线等级分布（达到特定等级的人数）
   */
  private static async getDeliveryLineLevelDistribution(
    targetLevels: number[]
  ): Promise<{ level: number; count: number }[]> {
    try {
      const distribution = [];
      
      // 统计每个目标等级的人数
      for (const level of targetLevels) {
        const count = await DeliveryLine.count({
          where: {
            level: {
              [Op.gte]: level,  // 达到或超过该等级
            },
          },
        });
        
        distribution.push({
          level,
          count,
        });
      }
      
      return distribution;
    } catch (error) {
      logger.error('获取流水线等级分布失败:', formatError(error));
      return [];
    }
  }

  /**
   * 获取详细的等级分布（用于图表展示）
   */
  public static async getDetailedLevelDistribution(): Promise<any> {
    try {
      // 获取所有牧场区域的等级分布
      const farmPlotDistribution = await sequelize.query(`
        SELECT 
          plotNumber as area,
          level,
          COUNT(*) as count
        FROM farm_plots
        WHERE isUnlocked = 1
        GROUP BY plotNumber, level
        ORDER BY plotNumber, level
      `, {
        type: QueryTypes.SELECT,
      });
      
      // 获取流水线等级分布
      const deliveryLineDistribution = await sequelize.query(`
        SELECT 
          level,
          COUNT(*) as count
        FROM delivery_lines
        GROUP BY level
        ORDER BY level
      `, {
        type: QueryTypes.SELECT,
      });
      
      return {
        farmPlots: farmPlotDistribution,
        deliveryLines: deliveryLineDistribution,
      };
    } catch (error) {
      logger.error('获取详细等级分布失败:', formatError(error));
      return { farmPlots: [], deliveryLines: [] };
    }
  }

  /**
   * 获取玩家进度概览
   */
  public static async getPlayerProgressOverview(): Promise<any> {
    try {
      // 获取平均解锁区域数
      const avgUnlockedAreas = await sequelize.query(`
        SELECT 
          AVG(unlocked_count) as avgUnlockedAreas
        FROM (
          SELECT 
            walletId,
            COUNT(*) as unlocked_count
          FROM farm_plots
          WHERE isUnlocked = 1
          GROUP BY walletId
        ) as unlocked_stats
      `, {
        type: QueryTypes.SELECT,
      }) as any[];
      
      // 获取平均牧场等级
      const avgFarmLevel = await sequelize.query(`
        SELECT 
          AVG(level) as avgLevel
        FROM farm_plots
        WHERE isUnlocked = 1
      `, {
        type: QueryTypes.SELECT,
      }) as any[];
      
      // 获取平均流水线等级
      const avgDeliveryLevel = await sequelize.query(`
        SELECT 
          AVG(level) as avgLevel
        FROM delivery_lines
      `, {
        type: QueryTypes.SELECT,
      }) as any[];
      
      return {
        avgUnlockedAreas: avgUnlockedAreas[0]?.avgUnlockedAreas || 0,
        avgFarmLevel: avgFarmLevel[0]?.avgLevel || 0,
        avgDeliveryLevel: avgDeliveryLevel[0]?.avgLevel || 0,
      };
    } catch (error) {
      logger.error('获取玩家进度概览失败:', formatError(error));
      return {
        avgUnlockedAreas: 0,
        avgFarmLevel: 0,
        avgDeliveryLevel: 0,
      };
    }
  }
}
