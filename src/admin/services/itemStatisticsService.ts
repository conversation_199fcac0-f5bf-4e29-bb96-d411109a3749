import { Op, QueryTypes } from 'sequelize';
import { ActiveBooster } from '../../models/ActiveBooster';
import { IapProduct } from '../../models/IapProduct';
import { ItemUsageStatistics, DateRangeQuery } from '../types/statistics';
import { logger, formatError } from '../../utils/logger';
import { sequelize } from '../../config/db';

export class ItemStatisticsService {
  /**
   * 获取道具使用统计数据
   */
  public static async getItemUsageStatistics(
    dateRange?: DateRangeQuery
  ): Promise<ItemUsageStatistics> {
    try {
      // 获取所有道具的使用统计
      const items = await this.getItemUsageDetails(dateRange);

      return {
        items,
      };
    } catch (error) {
      logger.error('获取道具使用统计失败:', formatError(error));
      throw error;
    }
  }

  /**
   * 获取道具使用详情
   */
  private static async getItemUsageDetails(dateRange?: DateRangeQuery): Promise<any[]> {
    try {
      let whereClause = '';
      
      if (dateRange?.startDate && dateRange?.endDate) {
        whereClause = `WHERE ab.createdAt >= :startDate AND ab.createdAt <= :endDate`;
      }
      
      const result = await sequelize.query(`
        SELECT 
          ab.type as itemType,
          CASE 
            WHEN ab.type = 'speed_boost' THEN '加速道具'
            WHEN ab.type = 'time_warp' THEN '时间扭曲'
            ELSE ab.type
          END as itemName,
          COUNT(DISTINCT ab.walletId) as totalUsers,
          COUNT(*) as totalUsageCount
        FROM active_boosters ab
        ${whereClause}
        GROUP BY ab.type
        ORDER BY totalUsageCount DESC
      `, {
        replacements: {
          startDate: dateRange?.startDate,
          endDate: dateRange?.endDate,
        },
        type: QueryTypes.SELECT,
      });
      
      return result;
    } catch (error) {
      logger.error('获取道具使用详情失败:', formatError(error));
      return [];
    }
  }

  /**
   * 获取道具效果分析
   */
  public static async getBoosterEffectAnalysis(): Promise<any> {
    try {
      // 获取使用道具的用户 vs 未使用道具的用户对比
      const result = await sequelize.query(`
        SELECT 
          CASE 
            WHEN booster_count > 0 THEN 'With Boosters'
            ELSE 'Without Boosters'
          END as userType,
          COUNT(*) as userCount,
          AVG(gem_balance) as avgGems,
          AVG(diamond_balance) as avgDiamonds,
          AVG(farm_level) as avgFarmLevel
        FROM (
          SELECT 
            uw.id,
            CAST(uw.gem AS DECIMAL(20, 2)) as gem_balance,
            CAST(uw.diamond AS DECIMAL(20, 2)) as diamond_balance,
            (SELECT AVG(level) FROM farm_plots WHERE walletId = uw.id AND isUnlocked = 1) as farm_level,
            (SELECT COUNT(*) FROM active_boosters WHERE walletId = uw.id) as booster_count
          FROM user_wallets uw
        ) as user_stats
        GROUP BY userType
      `, {
        type: QueryTypes.SELECT,
      });
      
      return result;
    } catch (error) {
      logger.error('获取道具效果分析失败:', formatError(error));
      return [];
    }
  }

  /**
   * 获取道具使用趋势
   */
  public static async getBoosterUsageTrend(days: number = 30): Promise<any[]> {
    try {
      const endDate = new Date();
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - days);
      
      const result = await sequelize.query(`
        SELECT 
          DATE(startTime) as date,
          type as boosterType,
          COUNT(*) as usageCount,
          COUNT(DISTINCT walletId) as uniqueUsers
        FROM active_boosters
        WHERE startTime >= :startDate AND startTime <= :endDate
        GROUP BY DATE(startTime), type
        ORDER BY date ASC, type
      `, {
        replacements: { startDate, endDate },
        type: QueryTypes.SELECT,
      });
      
      return result;
    } catch (error) {
      logger.error('获取道具使用趋势失败:', formatError(error));
      return [];
    }
  }

  /**
   * 获取道具倍率分布
   */
  public static async getMultiplierDistribution(): Promise<any[]> {
    try {
      const result = await sequelize.query(`
        SELECT 
          type as boosterType,
          multiplier,
          COUNT(*) as usageCount,
          COUNT(DISTINCT walletId) as uniqueUsers
        FROM active_boosters
        GROUP BY type, multiplier
        ORDER BY type, multiplier
      `, {
        type: QueryTypes.SELECT,
      });
      
      return result;
    } catch (error) {
      logger.error('获取道具倍率分布失败:', formatError(error));
      return [];
    }
  }

  /**
   * 获取活跃道具状态
   */
  public static async getActiveBoosterStatus(): Promise<any> {
    try {
      const now = new Date();
      
      // 获取当前活跃的道具数
      const activeCount = await sequelize.query(`
        SELECT 
          type as boosterType,
          COUNT(*) as activeCount,
          COUNT(DISTINCT walletId) as activeUsers
        FROM active_boosters
        WHERE status = 'active' 
          AND startTime <= :now 
          AND endTime >= :now
        GROUP BY type
      `, {
        replacements: { now },
        type: QueryTypes.SELECT,
      });
      
      // 获取已过期的道具数
      const expiredCount = await sequelize.query(`
        SELECT 
          type as boosterType,
          COUNT(*) as expiredCount
        FROM active_boosters
        WHERE status = 'expired' OR endTime < :now
        GROUP BY type
      `, {
        replacements: { now },
        type: QueryTypes.SELECT,
      });
      
      // 获取已使用的道具数
      const usedCount = await sequelize.query(`
        SELECT 
          type as boosterType,
          COUNT(*) as usedCount
        FROM active_boosters
        WHERE status = 'used'
        GROUP BY type
      `, {
        replacements: { now },
        type: QueryTypes.SELECT,
      });
      
      return {
        active: activeCount,
        expired: expiredCount,
        used: usedCount,
      };
    } catch (error) {
      logger.error('获取活跃道具状态失败:', formatError(error));
      return {
        active: [],
        expired: [],
        used: [],
      };
    }
  }
}
