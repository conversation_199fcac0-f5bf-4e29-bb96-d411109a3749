import { Op, QueryTypes } from 'sequelize';
import { IapPurchase } from '../../models/IapPurchase';
import { UserWallet } from '../../models/UserWallet';
import { PurchaseStatistics, DateRangeQuery } from '../types/statistics';
import { logger, formatError } from '../../utils/logger';
import { sequelize } from '../../config/db';

export class PurchaseStatisticsService {
  /**
   * 获取购买统计数据
   */
  public static async getPurchaseStatistics(
    dateRange?: DateRangeQuery
  ): Promise<PurchaseStatistics> {
    try {
      // 获取总充值金额（USD）
      const totalRevenue = await this.getTotalRevenue(dateRange);
      
      // 获取充值用户数
      const payingUsers = await this.getPayingUsersCount(dateRange);
      
      // 获取总用户数
      const totalUsers = await this.getTotalUsersCount();
      
      // 计算ARPU (平均每用户收入)
      const arpu = totalUsers > 0 ? totalRevenue / totalUsers : 0;
      
      // 计算ARPPU (平均每付费用户收入)
      const arppu = payingUsers > 0 ? totalRevenue / payingUsers : 0;

      return {
        totalRevenue: Math.round(totalRevenue * 100) / 100,
        payingUsers,
        arpu: Math.round(arpu * 100) / 100,
        arppu: Math.round(arppu * 100) / 100,
      };
    } catch (error) {
      logger.error('获取购买统计失败:', formatError(error));
      throw error;
    }
  }

  /**
   * 获取总充值金额（转换为USD）
   */
  private static async getTotalRevenue(dateRange?: DateRangeQuery): Promise<number> {
    try {
      let whereClause = "WHERE status IN ('CONFIRMED', 'FINALIZED')";
      
      if (dateRange?.startDate && dateRange?.endDate) {
        whereClause += ` AND purchaseDate >= '${dateRange.startDate.toISOString()}' 
                        AND purchaseDate <= '${dateRange.endDate.toISOString()}'`;
      }
      
      const result = await sequelize.query(`
        SELECT 
          SUM(
            CASE 
              WHEN currency = 'USD' THEN amount
              WHEN currency = 'KAIA' THEN amount * 0.5  -- 假设1 KAIA = 0.5 USD
              WHEN currency = 'PHRS' THEN amount * 0.01  -- 假设1 PHRS = 0.01 USD
              ELSE 0
            END
          ) as totalRevenue
        FROM iap_purchases
        ${whereClause}
      `, {
        type: QueryTypes.SELECT,
      }) as any[];
      
      return result[0]?.totalRevenue || 0;
    } catch (error) {
      logger.error('获取总充值金额失败:', formatError(error));
      return 0;
    }
  }

  /**
   * 获取充值用户数
   */
  private static async getPayingUsersCount(dateRange?: DateRangeQuery): Promise<number> {
    try {
      let whereClause: any = {
        status: {
          [Op.in]: ['CONFIRMED', 'FINALIZED'],
        },
      };
      
      if (dateRange?.startDate && dateRange?.endDate) {
        whereClause.purchaseDate = {
          [Op.gte]: dateRange.startDate,
          [Op.lte]: dateRange.endDate,
        };
      }
      
      const result = await sequelize.query(`
        SELECT COUNT(DISTINCT walletId) as payingUsers
        FROM iap_purchases
        WHERE status IN ('CONFIRMED', 'FINALIZED')
        ${dateRange?.startDate && dateRange?.endDate ? 
          `AND purchaseDate >= :startDate AND purchaseDate <= :endDate` : ''}
      `, {
        replacements: {
          startDate: dateRange?.startDate,
          endDate: dateRange?.endDate,
        },
        type: QueryTypes.SELECT,
      }) as any[];
      
      return result[0]?.payingUsers || 0;
    } catch (error) {
      logger.error('获取充值用户数失败:', formatError(error));
      return 0;
    }
  }

  /**
   * 获取总用户数
   */
  private static async getTotalUsersCount(): Promise<number> {
    try {
      const count = await UserWallet.count();
      return count;
    } catch (error) {
      logger.error('获取总用户数失败:', formatError(error));
      return 0;
    }
  }

  /**
   * 获取充值趋势（最近30天）
   */
  public static async getRevenueTrend(days: number = 30): Promise<any[]> {
    try {
      const endDate = new Date();
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - days);
      
      const result = await sequelize.query(`
        SELECT 
          DATE(purchaseDate) as date,
          SUM(
            CASE 
              WHEN currency = 'USD' THEN amount
              WHEN currency = 'KAIA' THEN amount * 0.5
              WHEN currency = 'PHRS' THEN amount * 0.01
              ELSE 0
            END
          ) as dailyRevenue,
          COUNT(DISTINCT walletId) as payingUsers,
          COUNT(*) as transactions
        FROM iap_purchases
        WHERE status IN ('CONFIRMED', 'FINALIZED')
          AND purchaseDate >= :startDate 
          AND purchaseDate <= :endDate
        GROUP BY DATE(purchaseDate)
        ORDER BY date ASC
      `, {
        replacements: { startDate, endDate },
        type: QueryTypes.SELECT,
      });
      
      return result;
    } catch (error) {
      logger.error('获取充值趋势失败:', formatError(error));
      return [];
    }
  }

  /**
   * 获取充值分布（按金额区间）
   */
  public static async getRevenueDistribution(): Promise<any[]> {
    try {
      const result = await sequelize.query(`
        SELECT 
          CASE 
            WHEN usd_amount < 10 THEN '0-10 USD'
            WHEN usd_amount < 50 THEN '10-50 USD'
            WHEN usd_amount < 100 THEN '50-100 USD'
            WHEN usd_amount < 500 THEN '100-500 USD'
            ELSE '500+ USD'
          END as amountRange,
          COUNT(DISTINCT walletId) as users,
          SUM(usd_amount) as totalRevenue
        FROM (
          SELECT 
            walletId,
            CASE 
              WHEN currency = 'USD' THEN amount
              WHEN currency = 'KAIA' THEN amount * 0.5
              WHEN currency = 'PHRS' THEN amount * 0.01
              ELSE 0
            END as usd_amount
          FROM iap_purchases
          WHERE status IN ('CONFIRMED', 'FINALIZED')
        ) as converted_purchases
        GROUP BY amountRange
        ORDER BY 
          CASE amountRange
            WHEN '0-10 USD' THEN 1
            WHEN '10-50 USD' THEN 2
            WHEN '50-100 USD' THEN 3
            WHEN '100-500 USD' THEN 4
            ELSE 5
          END
      `, {
        type: QueryTypes.SELECT,
      });
      
      return result;
    } catch (error) {
      logger.error('获取充值分布失败:', formatError(error));
      return [];
    }
  }

  /**
   * 获取支付方式统计
   */
  public static async getPaymentMethodStats(): Promise<any[]> {
    try {
      const result = await sequelize.query(`
        SELECT 
          paymentMethod,
          COUNT(DISTINCT walletId) as users,
          COUNT(*) as transactions,
          SUM(
            CASE 
              WHEN currency = 'USD' THEN amount
              WHEN currency = 'KAIA' THEN amount * 0.5
              WHEN currency = 'PHRS' THEN amount * 0.01
              ELSE 0
            END
          ) as totalRevenue
        FROM iap_purchases
        WHERE status IN ('CONFIRMED', 'FINALIZED')
        GROUP BY paymentMethod
        ORDER BY totalRevenue DESC
      `, {
        type: QueryTypes.SELECT,
      });
      
      return result;
    } catch (error) {
      logger.error('获取支付方式统计失败:', formatError(error));
      return [];
    }
  }
}
