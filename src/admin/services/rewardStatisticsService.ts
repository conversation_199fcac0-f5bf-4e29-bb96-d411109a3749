import { Op, QueryTypes } from 'sequelize';
import { Chest } from '../../models/Chest';
import { UserWallet } from '../../models/UserWallet';
import { RewardStatistics, DateRangeQuery } from '../types/statistics';
import { logger, formatError } from '../../utils/logger';
import { sequelize } from '../../config/db';

export class RewardStatisticsService {
  /**
   * 获取奖励统计数据
   */
  public static async getRewardStatistics(
    dateRange?: DateRangeQuery
  ): Promise<RewardStatistics> {
    try {
      // 获取总宝箱获取数
      const totalChestsObtained = await this.getTotalChestsObtained(dateRange);
      
      // 获取每日宝箱领取人数
      const dailyChestClaimUsers = await this.getDailyChestClaimUsers();
      
      // 获取玩家获取钻石总数
      const totalDiamondsEarned = await this.getTotalDiamondsEarned();
      
      // 获取玩家获取GEM总数
      const totalGemsEarned = await this.getTotalGemsEarned();

      return {
        totalChestsObtained,
        dailyChestClaimUsers,
        totalDiamondsEarned,
        totalGemsEarned,
      };
    } catch (error) {
      logger.error('获取奖励统计失败:', formatError(error));
      throw error;
    }
  }

  /**
   * 获取总宝箱获取数
   */
  private static async getTotalChestsObtained(dateRange?: DateRangeQuery): Promise<number> {
    try {
      let whereClause = '';
      
      if (dateRange?.startDate && dateRange?.endDate) {
        whereClause = `WHERE obtainedAt >= :startDate AND obtainedAt <= :endDate`;
      }
      
      const result = await sequelize.query(`
        SELECT COUNT(*) as total
        FROM chests
        ${whereClause}
      `, {
        replacements: {
          startDate: dateRange?.startDate,
          endDate: dateRange?.endDate,
        },
        type: QueryTypes.SELECT,
      }) as any[];
      
      return result[0]?.total || 0;
    } catch (error) {
      logger.error('获取总宝箱数失败:', formatError(error));
      return 0;
    }
  }

  /**
   * 获取每日宝箱领取人数
   */
  private static async getDailyChestClaimUsers(): Promise<number> {
    try {
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      
      const tomorrow = new Date(today);
      tomorrow.setDate(tomorrow.getDate() + 1);
      
      const result = await sequelize.query(`
        SELECT COUNT(DISTINCT walletId) as users
        FROM chests
        WHERE openedAt >= :today AND openedAt < :tomorrow
          AND isOpened = 1
      `, {
        replacements: { today, tomorrow },
        type: QueryTypes.SELECT,
      }) as any[];
      
      return result[0]?.users || 0;
    } catch (error) {
      logger.error('获取每日宝箱领取人数失败:', formatError(error));
      return 0;
    }
  }

  /**
   * 获取玩家获取钻石总数
   */
  private static async getTotalDiamondsEarned(): Promise<number> {
    try {
      const result = await sequelize.query(`
        SELECT SUM(CAST(diamond AS DECIMAL(20, 2))) as total
        FROM user_wallets
      `, {
        type: QueryTypes.SELECT,
      }) as any[];
      
      return result[0]?.total || 0;
    } catch (error) {
      logger.error('获取钻石总数失败:', formatError(error));
      return 0;
    }
  }

  /**
   * 获取玩家获取GEM总数
   */
  private static async getTotalGemsEarned(): Promise<number> {
    try {
      const result = await sequelize.query(`
        SELECT SUM(CAST(gem AS DECIMAL(20, 2))) as total
        FROM user_wallets
      `, {
        type: QueryTypes.SELECT,
      }) as any[];
      
      return result[0]?.total || 0;
    } catch (error) {
      logger.error('获取GEM总数失败:', formatError(error));
      return 0;
    }
  }

  /**
   * 获取宝箱类型分布
   */
  public static async getChestTypeDistribution(): Promise<any[]> {
    try {
      const result = await sequelize.query(`
        SELECT 
          type as chestType,
          COUNT(*) as totalCount,
          COUNT(CASE WHEN isOpened = 1 THEN 1 END) as openedCount,
          COUNT(CASE WHEN isOpened = 0 THEN 1 END) as unopenedCount,
          COUNT(DISTINCT walletId) as uniqueUsers
        FROM chests
        GROUP BY type
        ORDER BY totalCount DESC
      `, {
        type: QueryTypes.SELECT,
      });
      
      return result;
    } catch (error) {
      logger.error('获取宝箱类型分布失败:', formatError(error));
      return [];
    }
  }

  /**
   * 获取资源分布
   */
  public static async getResourceDistribution(): Promise<any> {
    try {
      // 获取GEM分布
      const gemDistribution = await sequelize.query(`
        SELECT 
          CASE 
            WHEN CAST(gem AS DECIMAL(20, 2)) = 0 THEN '0'
            WHEN CAST(gem AS DECIMAL(20, 2)) < 1000 THEN '1-999'
            WHEN CAST(gem AS DECIMAL(20, 2)) < 10000 THEN '1000-9999'
            WHEN CAST(gem AS DECIMAL(20, 2)) < 100000 THEN '10000-99999'
            WHEN CAST(gem AS DECIMAL(20, 2)) < 1000000 THEN '100000-999999'
            ELSE '1000000+'
          END as gemRange,
          COUNT(*) as userCount
        FROM user_wallets
        GROUP BY gemRange
        ORDER BY 
          CASE gemRange
            WHEN '0' THEN 1
            WHEN '1-999' THEN 2
            WHEN '1000-9999' THEN 3
            WHEN '10000-99999' THEN 4
            WHEN '100000-999999' THEN 5
            ELSE 6
          END
      `, {
        type: QueryTypes.SELECT,
      });
      
      // 获取钻石分布
      const diamondDistribution = await sequelize.query(`
        SELECT 
          CASE 
            WHEN CAST(diamond AS DECIMAL(20, 2)) = 0 THEN '0'
            WHEN CAST(diamond AS DECIMAL(20, 2)) < 100 THEN '1-99'
            WHEN CAST(diamond AS DECIMAL(20, 2)) < 500 THEN '100-499'
            WHEN CAST(diamond AS DECIMAL(20, 2)) < 1000 THEN '500-999'
            WHEN CAST(diamond AS DECIMAL(20, 2)) < 5000 THEN '1000-4999'
            ELSE '5000+'
          END as diamondRange,
          COUNT(*) as userCount
        FROM user_wallets
        GROUP BY diamondRange
        ORDER BY 
          CASE diamondRange
            WHEN '0' THEN 1
            WHEN '1-99' THEN 2
            WHEN '100-499' THEN 3
            WHEN '500-999' THEN 4
            WHEN '1000-4999' THEN 5
            ELSE 6
          END
      `, {
        type: QueryTypes.SELECT,
      });
      
      return {
        gemDistribution,
        diamondDistribution,
      };
    } catch (error) {
      logger.error('获取资源分布失败:', formatError(error));
      return {
        gemDistribution: [],
        diamondDistribution: [],
      };
    }
  }

  /**
   * 获取每日奖励趋势
   */
  public static async getDailyRewardTrend(days: number = 30): Promise<any[]> {
    try {
      const endDate = new Date();
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - days);
      
      const result = await sequelize.query(`
        SELECT 
          DATE(openedAt) as date,
          COUNT(*) as chestsOpened,
          COUNT(DISTINCT walletId) as uniqueUsers,
          SUM(gemReward) as totalGems,
          SUM(diamondReward) as totalDiamonds
        FROM chests
        WHERE openedAt >= :startDate AND openedAt <= :endDate
          AND isOpened = 1
        GROUP BY DATE(openedAt)
        ORDER BY date ASC
      `, {
        replacements: { startDate, endDate },
        type: QueryTypes.SELECT,
      });
      
      return result;
    } catch (error) {
      logger.error('获取每日奖励趋势失败:', formatError(error));
      return [];
    }
  }
}
