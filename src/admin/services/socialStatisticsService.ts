import { Op, QueryTypes } from 'sequelize';
import { UserWallet } from '../../models/UserWallet';
import { SocialStatistics } from '../types/statistics';
import { logger, formatError } from '../../utils/logger';
import { sequelize } from '../../config/db';

export class SocialStatisticsService {
  /**
   * 获取社交统计数据
   */
  public static async getSocialStatistics(): Promise<SocialStatistics> {
    try {
      // 获取总邀请数
      const totalInvitations = await this.getTotalInvitations();
      
      // 获取邀请人数分布
      const invitationDistribution = await this.getInvitationDistribution();

      return {
        totalInvitations,
        invitationDistribution,
      };
    } catch (error) {
      logger.error('获取社交统计失败:', formatError(error));
      throw error;
    }
  }

  /**
   * 获取总邀请数
   */
  private static async getTotalInvitations(): Promise<number> {
    try {
      const result = await sequelize.query(`
        SELECT SUM(referralCount) as total
        FROM user_wallets
      `, {
        type: QueryTypes.SELECT,
      }) as any[];
      
      return result[0]?.total || 0;
    } catch (error) {
      logger.error('获取总邀请数失败:', formatError(error));
      return 0;
    }
  }

  /**
   * 获取邀请人数分布
   */
  private static async getInvitationDistribution(): Promise<any[]> {
    try {
      // 定义邀请数量级别
      const inviteLevels = [1, 5, 10, 20, 30, 40, 50, 60, 70, 80, 90, 100, 
                           110, 120, 130, 140, 150, 160, 170, 180, 190, 200];
      
      const distribution = [];
      
      for (const level of inviteLevels) {
        const result = await sequelize.query(`
          SELECT COUNT(*) as userCount
          FROM user_wallets
          WHERE referralCount >= :level
        `, {
          replacements: { level },
          type: QueryTypes.SELECT,
        }) as any[];
        
        distribution.push({
          inviteCount: level,
          userCount: result[0]?.userCount || 0,
        });
      }
      
      return distribution;
    } catch (error) {
      logger.error('获取邀请人数分布失败:', formatError(error));
      return [];
    }
  }

  /**
   * 获取详细的邀请分布（按区间）
   */
  public static async getDetailedInvitationDistribution(): Promise<any[]> {
    try {
      const result = await sequelize.query(`
        SELECT 
          CASE 
            WHEN referralCount = 0 THEN '0'
            WHEN referralCount BETWEEN 1 AND 5 THEN '1-5'
            WHEN referralCount BETWEEN 6 AND 10 THEN '6-10'
            WHEN referralCount BETWEEN 11 AND 20 THEN '11-20'
            WHEN referralCount BETWEEN 21 AND 50 THEN '21-50'
            WHEN referralCount BETWEEN 51 AND 100 THEN '51-100'
            WHEN referralCount BETWEEN 101 AND 200 THEN '101-200'
            ELSE '200+'
          END as inviteRange,
          COUNT(*) as userCount,
          SUM(referralCount) as totalInvites
        FROM user_wallets
        GROUP BY inviteRange
        ORDER BY 
          CASE inviteRange
            WHEN '0' THEN 1
            WHEN '1-5' THEN 2
            WHEN '6-10' THEN 3
            WHEN '11-20' THEN 4
            WHEN '21-50' THEN 5
            WHEN '51-100' THEN 6
            WHEN '101-200' THEN 7
            ELSE 8
          END
      `, {
        type: QueryTypes.SELECT,
      });
      
      return result;
    } catch (error) {
      logger.error('获取详细邀请分布失败:', formatError(error));
      return [];
    }
  }

  /**
   * 获取推荐关系链
   */
  public static async getReferralChains(): Promise<any> {
    try {
      // 获取有推荐人的用户数
      const hasReferrer = await sequelize.query(`
        SELECT COUNT(*) as count
        FROM user_wallets
        WHERE referrerWalletId IS NOT NULL
      `, {
        type: QueryTypes.SELECT,
      }) as any[];
      
      // 获取推荐人数TOP 10
      const topReferrers = await sequelize.query(`
        SELECT 
          id as walletId,
          referralCount,
          walletAddress
        FROM user_wallets
        WHERE referralCount > 0
        ORDER BY referralCount DESC
        LIMIT 10
      `, {
        type: QueryTypes.SELECT,
      });
      
      // 获取推荐链深度分布（简化版，只统计一级推荐）
      const referralDepth = await sequelize.query(`
        SELECT 
          COUNT(CASE WHEN referralCount > 0 THEN 1 END) as hasReferrals,
          COUNT(CASE WHEN referrerWalletId IS NOT NULL THEN 1 END) as hasReferrer,
          COUNT(*) as totalUsers
        FROM user_wallets
      `, {
        type: QueryTypes.SELECT,
      }) as any[];
      
      return {
        hasReferrerCount: hasReferrer[0]?.count || 0,
        topReferrers,
        referralDepth: referralDepth[0] || {},
      };
    } catch (error) {
      logger.error('获取推荐关系链失败:', formatError(error));
      return {
        hasReferrerCount: 0,
        topReferrers: [],
        referralDepth: {},
      };
    }
  }

  /**
   * 获取邀请效果分析
   */
  public static async getInvitationEffectiveness(): Promise<any> {
    try {
      // 获取有推荐人的用户的平均活跃度和消费情况
      const result = await sequelize.query(`
        SELECT 
          CASE 
            WHEN referrerWalletId IS NULL THEN 'Direct'
            ELSE 'Referred'
          END as userType,
          COUNT(*) as userCount,
          AVG(CAST(gem AS DECIMAL(20, 2))) as avgGems,
          AVG(CAST(diamond AS DECIMAL(20, 2))) as avgDiamonds,
          AVG(referralCount) as avgReferrals
        FROM user_wallets
        GROUP BY userType
      `, {
        type: QueryTypes.SELECT,
      });
      
      return result;
    } catch (error) {
      logger.error('获取邀请效果分析失败:', formatError(error));
      return [];
    }
  }
}
