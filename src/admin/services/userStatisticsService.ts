import { Op, Sequelize } from 'sequelize';
import { UserWallet } from '../../models/UserWallet';
import { User } from '../../models/User';
import { AppLaunch } from '../../models/AppLaunch';
import { SessionDuration } from '../../models/SessionDuration';
import { UserBehaviorStatistics, DateRangeQuery, RetentionParams } from '../types/statistics';
import { logger, formatError } from '../../utils/logger';
import { sequelize } from '../../config/db';
import { QueryTypes } from 'sequelize';
import dayjs from 'dayjs';

export class UserStatisticsService {
  /**
   * 获取用户行为统计数据
   */
  public static async getUserBehaviorStatistics(
    dateRange?: DateRangeQuery
  ): Promise<UserBehaviorStatistics> {
    try {
      const today = dayjs().startOf('day').toDate();
      const tomorrow = dayjs().startOf('day').add(1, 'day').toDate();
      const sevenDaysAgo = dayjs().startOf('day').subtract(7, 'day').toDate();

      // 1. 每日游戏启动次数（通过统计今天活跃的用户数）
      const dailyAppOpens = await this.getDailyAppOpens(today, tomorrow);
      
      // 2. 每日平均游玩时长
      const averagePlayTime = await this.getAveragePlayTime(today, tomorrow);
      
      // 3. 当日新增用户数
      const dailyNewUsers = await this.getDailyNewUsers(today, tomorrow);
      
      // 4. 7日用户总量
      const sevenDayUsers = await this.getSevenDayUsers(sevenDaysAgo, tomorrow);
      
      // 5. 首日留存率
      const firstDayRetention = await this.getRetentionRate({
        cohortDate: dayjs().subtract(1, 'day').toDate(),
        retentionDay: 1
      });

      // 6. 七日留存率
      const sevenDayRetention = await this.getRetentionRate({
        cohortDate: dayjs().subtract(7, 'day').toDate(),
        retentionDay: 7
      });

      return {
        dailyAppOpens,
        averagePlayTime,
        dailyNewUsers,
        sevenDayUsers,
        firstDayRetention,
        sevenDayRetention,
      };
    } catch (error) {
      logger.error('获取用户行为统计失败:', formatError(error));
      throw error;
    }
  }

  /**
   * 获取每日APP打开次数（真实启动次数）
   */
  private static async getDailyAppOpens(startDate: Date, endDate: Date): Promise<number> {
    try {
      // 统计指定时间范围内的真实启动次数
      const launchCount = await AppLaunch.count({
        where: {
          launchTime: {
            [Op.gte]: startDate,
            [Op.lt]: endDate,
          },
        },
      });

      return launchCount;
    } catch (error) {
      logger.error('获取每日APP打开次数失败:', formatError(error));
      return 0;
    }
  }

  /**
   * 获取平均游玩时长（分钟）
   * 基于SessionDuration表的真实数据统计
   */
  private static async getAveragePlayTime(startDate: Date, endDate: Date): Promise<number> {
    try {
      // 从session_durations表查询平均时长
      const result = await sequelize.query(`
        SELECT
          AVG(sd.duration) as avgDurationSeconds,
          COUNT(DISTINCT sd.walletId) as uniqueUsers,
          COUNT(*) as totalSessions
        FROM session_durations sd
        INNER JOIN app_launches al ON sd.sessionId = al.sessionId
        WHERE al.launchTime >= :startDate
          AND al.launchTime < :endDate
          AND sd.duration > 0
          AND sd.duration < 86400 -- 排除异常数据（超过24小时）
      `, {
        replacements: { startDate, endDate },
        type: QueryTypes.SELECT,
      }) as any[];

      // 转换为分钟
      const avgSeconds = result[0]?.avgDurationSeconds || 0;
      return Math.round(avgSeconds / 60);
    } catch (error) {
      logger.error('获取平均游玩时长失败:', formatError(error));
      return 0; // 返回0而不是硬编码的30
    }
  }

  /**
   * 获取每日新增用户数
   */
  private static async getDailyNewUsers(startDate: Date, endDate: Date): Promise<number> {
    try {
      // 使用原生SQL查询来统计新用户
      const result = await sequelize.query(`
        SELECT COUNT(*) as count
        FROM user_wallets
        WHERE createdAt >= :startDate AND createdAt < :endDate
      `, {
        replacements: { startDate, endDate },
        type: QueryTypes.SELECT,
      }) as any[];
      
      return result[0]?.count || 0;
    } catch (error) {
      logger.error('获取每日新增用户失败:', formatError(error));
      return 0;
    }
  }

  /**
   * 获取7日用户总量
   */
  private static async getSevenDayUsers(startDate: Date, endDate: Date): Promise<number> {
    try {
      const users = await UserWallet.count({
        where: {
          lastActiveTime: {
            [Op.gte]: startDate,
            [Op.lt]: endDate,
          },
        },
      });
      
      return users;
    } catch (error) {
      logger.error('获取7日用户总量失败:', formatError(error));
      return 0;
    }
  }

  /**
   * 计算留存率
   */
  private static async getRetentionRate(params: RetentionParams): Promise<number> {
    try {
      const { cohortDate, retentionDay } = params;
      
      // 获取队列日期的新用户
      const cohortStart = new Date(cohortDate);
      cohortStart.setHours(0, 0, 0, 0);
      
      const cohortEnd = new Date(cohortDate);
      cohortEnd.setHours(23, 59, 59, 999);
      
      // 获取队列用户数（通过原生SQL查询）
      const cohortUsers = await sequelize.query(`
        SELECT userId
        FROM user_wallets
        WHERE createdAt >= :cohortStart AND createdAt <= :cohortEnd
      `, {
        replacements: { cohortStart, cohortEnd },
        type: QueryTypes.SELECT,
      }) as any[];
      
      if (cohortUsers.length === 0) {
        return 0;
      }
      
      const cohortUserIds = cohortUsers.map((u: any) => u.userId);
      
      // 计算留存日期
      const retentionDate = new Date(cohortDate);
      retentionDate.setDate(retentionDate.getDate() + retentionDay);
      retentionDate.setHours(0, 0, 0, 0);
      
      const retentionDateEnd = new Date(retentionDate);
      retentionDateEnd.setHours(23, 59, 59, 999);
      
      // 获取在留存日期活跃的队列用户
      const retainedUsers = await UserWallet.count({
        where: {
          userId: {
            [Op.in]: cohortUserIds,
          },
          lastActiveTime: {
            [Op.gte]: retentionDate,
            [Op.lte]: retentionDateEnd,
          },
        },
      });
      
      // 计算留存率
      const retentionRate = (retainedUsers / cohortUsers.length) * 100;
      
      return Math.round(retentionRate * 100) / 100; // 保留两位小数
    } catch (error) {
      logger.error('计算留存率失败:', formatError(error));
      return 0;
    }
  }

  /**
   * 获取用户增长趋势（最近30天）
   */
  public static async getUserGrowthTrend(days: number = 30): Promise<any[]> {
    try {
      const endDate = new Date();
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - days);
      
      const result = await sequelize.query(`
        SELECT 
          DATE(createdAt) as date,
          COUNT(*) as newUsers
        FROM user_wallets
        WHERE createdAt >= :startDate AND createdAt <= :endDate
        GROUP BY DATE(createdAt)
        ORDER BY date ASC
      `, {
        replacements: { startDate, endDate },
        type: QueryTypes.SELECT,
      });
      
      return result;
    } catch (error) {
      logger.error('获取用户增长趋势失败:', formatError(error));
      return [];
    }
  }

  /**
   * 获取活跃用户趋势（最近30天）
   */
  public static async getActiveUserTrend(days: number = 30): Promise<any[]> {
    try {
      const endDate = dayjs().toDate();
      const startDate = dayjs().subtract(days, 'day').toDate();
      
      const result = await sequelize.query(`
        SELECT 
          DATE(lastActiveTime) as date,
          COUNT(DISTINCT id) as activeUsers
        FROM user_wallets
        WHERE lastActiveTime >= :startDate AND lastActiveTime <= :endDate
        GROUP BY DATE(lastActiveTime)
        ORDER BY date ASC
      `, {
        replacements: { startDate, endDate },
        type: QueryTypes.SELECT,
      });
      
      return result;
    } catch (error) {
      logger.error('获取活跃用户趋势失败:', formatError(error));
      return [];
    }
  }
}
