// 统计数据类型定义

// 1. 用户行为统计
export interface UserBehaviorStatistics {
  // 每日游戏启动次数
  dailyAppOpens: number;
  // 每日平均游玩时长（分钟）
  averagePlayTime: number;
  // 当日新增用户数
  dailyNewUsers: number;
  // 7日用户总量
  sevenDayUsers: number;
  // 首日留存率
  firstDayRetention: number;
  // 七日留存率
  sevenDayRetention: number;
}

// 2. 游戏进度统计
export interface GameProgressStatistics {
  // 牧场区域解锁统计
  farmAreaUnlocks: {
    area: number;
    unlockedCount: number;
  }[];
  
  // 牧场区域等级分布
  farmAreaLevelDistribution: {
    area: number;
    levelDistribution: {
      level: number;
      count: number;
    }[];
  }[];
  
  // 流水线等级分布
  deliveryLineLevelDistribution: {
    level: number;
    count: number;
  }[];
}

// 3. 道具使用统计
export interface ItemUsageStatistics {
  // 每种道具的使用统计
  items: {
    itemType: string;
    itemName: string;
    totalUsers: number;      // 使用人数
    totalUsageCount: number; // 使用次数
  }[];
}

// 4. 购买统计
export interface PurchaseStatistics {
  // 总充值金额
  totalRevenue: number;
  // 充值用户数
  payingUsers: number;
  // ARPU (平均每用户收入)
  arpu: number;
  // ARPPU (平均每付费用户收入)
  arppu: number;
}

// 5. 任务统计
export interface TaskStatistics {
  // 总任务完成次数
  totalTaskCompletions: number;
  // 各任务完成情况
  taskCompletions: {
    taskId: number;
    taskName: string;
    completedUsers: number;
  }[];
}

// 6. 社交统计
export interface SocialStatistics {
  // 总邀请数
  totalInvitations: number;
  // 邀请人数分布
  invitationDistribution: {
    inviteCount: number;  // 邀请数量级别 (1, 5, 10, 20, 30...200)
    userCount: number;    // 达到该级别的用户数
  }[];
}

// 7. 奖励统计
export interface RewardStatistics {
  // 总宝箱获取数
  totalChestsObtained: number;
  // 每日宝箱领取人数
  dailyChestClaimUsers: number;
  // 玩家获取钻石总数
  totalDiamondsEarned: number;
  // 玩家获取GEM总数
  totalGemsEarned: number;
}

// 综合统计数据
export interface ComprehensiveStatistics {
  userBehavior: UserBehaviorStatistics;
  gameProgress: GameProgressStatistics;
  itemUsage: ItemUsageStatistics;
  purchase: PurchaseStatistics;
  task: TaskStatistics;
  social: SocialStatistics;
  reward: RewardStatistics;
  generatedAt: Date;
}

// 日期范围查询参数
export interface DateRangeQuery {
  startDate?: Date;
  endDate?: Date;
}

// 统计数据导出格式
export interface StatisticsExportData {
  序号: number;
  统计项: string;
  数值: number | string;
  备注?: string;
}

// 留存率计算参数
export interface RetentionParams {
  cohortDate: Date;
  retentionDay: number; // 第N天留存
}

// 等级分布查询参数
export interface LevelDistributionQuery {
  targetLevels: number[]; // 要查询的目标等级 [10, 20, 25, 30, 35, 40, 45, 50]
}
