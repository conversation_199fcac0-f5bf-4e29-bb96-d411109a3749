/**
 * Session配置文件
 * 
 * 基于会话Cookie的用户行为统计系统配置
 * 利用浏览器原生行为：会话Cookie在浏览器关闭时自动清除
 * 新Session = 新启动 = 准确的启动次数统计
 */

import session from 'express-session';
// @ts-ignore
const connectRedis = require('connect-redis');
import { redis } from './redis';
import { logger } from '../utils/logger';
import dayjs from 'dayjs';

// 创建RedisStore
const RedisStore = connectRedis(session);

/**
 * Session配置选项
 */
export const sessionConfig: session.SessionOptions = {
  // Session存储配置 - 使用Redis存储
  store: new RedisStore({
    client: redis,
    prefix: 'sess:', // Session键前缀
    ttl: 24 * 60 * 60, // Redis中保存24小时（秒）
  }),
  
  // Session基本配置
  name: 'moofun_sid', // Cookie名称
  secret: process.env.SESSION_SECRET || 'moofun-default-secret-key-change-in-production',
  resave: false, // 不强制保存未修改的session
  saveUninitialized: true, // 保存未初始化的session（重要：确保cookie能被写入）
  rolling: true, // 每次请求都刷新cookie过期时间
  proxy: process.env.NODE_ENV === 'production', // 生产环境信任代理
  
  // Cookie配置（关键配置 - 会话Cookie）
  cookie: {
    httpOnly: true, // 防止XSS攻击，禁止JavaScript访问
    secure: process.env.NODE_ENV === 'production', // 生产环境仅HTTPS
    sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax', // 跨域配置
    
    // 重要：不设置 maxAge 或 expires
    // 这使其成为会话Cookie，浏览器关闭即消失
    maxAge: undefined,
    expires: undefined,
    
    // 可选：设置cookie域名（用于子域名共享）
    domain: process.env.COOKIE_DOMAIN || undefined
  }
};

/**
 * 创建session中间件实例
 */
export const sessionMiddleware = session(sessionConfig);

/**
 * 验证Session配置
 */
export function validateSessionConfig(): boolean {
  try {
    // 检查必要的环境变量
    if (process.env.NODE_ENV === 'production' && !process.env.SESSION_SECRET) {
      logger.error('生产环境必须设置SESSION_SECRET环境变量');
      return false;
    }

    // 检查Redis连接
    if (!redis) {
      logger.error('Redis客户端未初始化');
      return false;
    }

    // 验证Cookie配置
    const cookieConfig = sessionConfig.cookie;
    if (cookieConfig?.maxAge !== undefined || cookieConfig?.expires !== undefined) {
      logger.warn('检测到Cookie设置了过期时间，这将影响会话Cookie的行为');
    }

    logger.info('Session配置验证通过', {
      cookieName: sessionConfig.name,
      isSecure: cookieConfig?.secure,
      sameSite: cookieConfig?.sameSite,
      domain: cookieConfig?.domain,
      isSessionCookie: cookieConfig?.maxAge === undefined && cookieConfig?.expires === undefined
    });

    return true;
  } catch (error) {
    logger.error('Session配置验证失败', { error: error instanceof Error ? error.message : error });
    return false;
  }
}

/**
 * 获取Session统计信息
 */
export async function getSessionStats(): Promise<{
  totalSessions: number;
  activeSessions: number;
  redisKeys: string[];
}> {
  try {
    // 获取所有session keys
    const sessionKeys = await redis.keys('sess:*');
    
    // 统计活跃session（有数据的session）
    let activeSessions = 0;
    for (const key of sessionKeys) {
      const sessionData = await redis.get(key);
      if (sessionData) {
        try {
          const session = JSON.parse(sessionData);
          if (session.initialized) {
            activeSessions++;
          }
        } catch (e) {
          // 忽略解析错误的session
        }
      }
    }

    return {
      totalSessions: sessionKeys.length,
      activeSessions,
      redisKeys: sessionKeys
    };
  } catch (error) {
    logger.error('获取Session统计信息失败', { error: error instanceof Error ? error.message : error });
    return {
      totalSessions: 0,
      activeSessions: 0,
      redisKeys: []
    };
  }
}

/**
 * 清理过期的Session数据
 */
export async function cleanupExpiredSessions(): Promise<number> {
  try {
    const sessionKeys = await redis.keys('sess:*');
    let cleanedCount = 0;

    for (const key of sessionKeys) {
      const ttl = await redis.ttl(key);
      // 如果TTL为-1（永不过期）或-2（已过期），则清理
      if (ttl === -2) {
        await redis.del(key);
        cleanedCount++;
      }
    }

    if (cleanedCount > 0) {
      logger.info('清理过期Session完成', { cleanedCount });
    }

    return cleanedCount;
  } catch (error) {
    logger.error('清理过期Session失败', { error: error instanceof Error ? error.message : error });
    return 0;
  }
}
