/**
 * Session时长保存任务
 * 
 * 定期从Redis中读取session数据，计算时长并保存到数据库
 * 用于实现游玩时长统计功能
 */

import { redis } from '../config/redis';
import { SessionDuration } from '../models/SessionDuration';
import { logger, formatError } from '../utils/logger';
import cron from 'node-cron';
import dayjs from 'dayjs';

/**
 * 计算两个时间之间的时长（秒）
 */
function calculateDuration(startTime: string | Date, endTime: string | Date): number {
  return dayjs(endTime).diff(dayjs(startTime), 'second');
}

/**
 * 保存Session时长数据
 */
export async function saveSessionDurations(): Promise<void> {
  try {
    logger.info('开始保存Session时长数据...');
    
    // 获取所有session keys
    const sessionKeys = await redis.keys('sess:*');
    
    if (sessionKeys.length === 0) {
      logger.info('没有找到活跃的Session');
      return;
    }

    let processedCount = 0;
    let updatedCount = 0;
    let errorCount = 0;

    for (const key of sessionKeys) {
      try {
        const sessionData = await redis.get(key);
        if (!sessionData) continue;
        
        const session = JSON.parse(sessionData);
        
        // 只处理已初始化的session
        if (!session.initialized || !session.walletId || !session.createdAt) {
          continue;
        }

        const sessionId = key.replace('sess:', '');
        const now = dayjs();
        const startTime = dayjs(session.createdAt);
        const lastActivityTime = session.lastActivityAt ? dayjs(session.lastActivityAt) : now;
        
        // 计算时长
        const duration = calculateDuration(startTime.toDate(), lastActivityTime.toDate());
        
        // 跳过异常数据
        if (duration < 0 || duration > 86400) { // 超过24小时的异常数据
          logger.warn('跳过异常Session时长数据', {
            sessionId,
            duration,
            startTime,
            lastActivityTime
          });
          continue;
        }

        // 保存或更新时长记录
        const sessionDurationData = {
          sessionId,
          walletId: session.walletId,
          startTime: startTime.toDate(),
          lastActivityTime: lastActivityTime.toDate(),
          duration,
          pageViews: session.pageViews || 0,
          requestCount: session.requestCount || 0,
          status: 'active' as const,
          deviceType: session.deviceType,
          browser: session.browser
        };

        await SessionDuration.upsert(sessionDurationData);
        
        updatedCount++;
        processedCount++;
        
      } catch (error) {
        errorCount++;
        logger.error('处理单个Session失败', {
          key,
          error: formatError(error)
        });
      }
    }
    
    logger.info('Session时长保存完成', {
      totalSessions: sessionKeys.length,
      processedCount,
      updatedCount,
      errorCount
    });
    
  } catch (error) {
    logger.error('保存Session时长失败', { error: formatError(error) });
  }
}

/**
 * 清理过期的Session时长记录
 */
export async function cleanupExpiredSessionDurations(): Promise<void> {
  try {
    logger.info('开始清理过期的Session时长记录...');
    
    // 删除30天前的记录
    const thirtyDaysAgo = dayjs().subtract(30, 'day').toDate();
    
    const deletedCount = await SessionDuration.destroy({
      where: {
        startTime: {
          [require('sequelize').Op.lt]: thirtyDaysAgo
        }
      }
    });
    
    if (deletedCount > 0) {
      logger.info('清理过期Session时长记录完成', { deletedCount });
    }
    
  } catch (error) {
    logger.error('清理过期Session时长记录失败', { error: formatError(error) });
  }
}

/**
 * 标记已结束的Session
 */
export async function markEndedSessions(): Promise<void> {
  try {
    // 获取所有Redis中的session keys
    const sessionKeys = await redis.keys('sess:*');
    const activeSessionIds = sessionKeys.map(key => key.replace('sess:', ''));
    
    if (activeSessionIds.length === 0) {
      // 如果Redis中没有session，标记所有活跃session为已结束
      const updatedCount = await SessionDuration.update(
        { 
          status: 'ended',
          endReason: 'timeout'
        },
        {
          where: {
            status: 'active'
          }
        }
      );
      
      if (updatedCount[0] > 0) {
        logger.info('标记所有活跃Session为已结束', { count: updatedCount[0] });
      }
    } else {
      // 标记不在Redis中的session为已结束
      const updatedCount = await SessionDuration.update(
        { 
          status: 'ended',
          endReason: 'timeout'
        },
        {
          where: {
            status: 'active',
            sessionId: {
              [require('sequelize').Op.notIn]: activeSessionIds
            }
          }
        }
      );
      
      if (updatedCount[0] > 0) {
        logger.info('标记过期Session为已结束', { count: updatedCount[0] });
      }
    }
    
  } catch (error) {
    logger.error('标记已结束Session失败', { error: formatError(error) });
  }
}

/**
 * 启动Session时长保存定时任务
 */
export function startSessionDurationJobs(): void {
  // 每5分钟保存一次Session时长
  cron.schedule('*/5 * * * *', async () => {
    await saveSessionDurations();
  });

  // 每15分钟标记一次已结束的Session
  cron.schedule('*/15 * * * *', async () => {
    await markEndedSessions();
  });

  // 每天凌晨3点清理过期数据
  cron.schedule('0 3 * * *', async () => {
    await cleanupExpiredSessionDurations();
  });

  logger.info('Session时长保存定时任务已启动');
  logger.info('- 每5分钟保存Session时长数据');
  logger.info('- 每15分钟标记已结束的Session');
  logger.info('- 每天凌晨3点清理过期数据');
}

/**
 * 手动执行一次Session时长保存（用于测试）
 */
export async function runSessionDurationJobsOnce(): Promise<void> {
  logger.info('手动执行Session时长保存任务...');
  await saveSessionDurations();
  await markEndedSessions();
  logger.info('手动执行完成');
}
