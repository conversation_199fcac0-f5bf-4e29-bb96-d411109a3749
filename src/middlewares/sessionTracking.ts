/**
 * 会话追踪中间件
 * 
 * 负责：
 * 1. 检测新会话并记录启动次数
 * 2. 更新用户活跃时间
 * 3. 记录会话活动数据
 * 4. 提供会话统计信息
 */

import { Request, Response, NextFunction } from 'express';
import { AppLaunch } from '../models/AppLaunch';
import { UserWallet } from '../models/UserWallet';
import { logger, formatError } from '../utils/logger';
import dayjs from 'dayjs';

/**
 * 会话追踪中间件
 * 只对已认证用户进行追踪
 */
export async function sessionTrackingMiddleware(
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> {
  try {
    // 只对已认证用户进行追踪
    const user = (req as any).user;
    if (!user || !user.walletId) {
      return next();
    }

    const walletId = user.walletId;
    const sessionId = req.sessionID;
    const session = req.session as any;

    // 检查是否是新创建的session
    if (!session.initialized) {
      // 新session = 新的浏览器启动
      await recordNewLaunch(walletId, sessionId, req);

      // 标记session已初始化
      session.initialized = true;
      session.walletId = walletId;
      session.userId = user.userId;
      session.createdAt = dayjs().toDate();
      session.launchCount = (session.launchCount || 0) + 1;
      session.requestCount = 0;
      session.pageViews = 0;

      // 记录设备和浏览器信息
      const deviceInfo = detectDeviceInfo(req.headers['user-agent']);
      session.deviceType = deviceInfo.deviceType;
      session.browser = deviceInfo.browser;
      session.userAgent = req.headers['user-agent'];
      session.ipAddress = getClientIP(req);
      session.referrer = req.headers.referer;

      logger.info('新会话创建', {
        walletId,
        sessionId,
        deviceType: deviceInfo.deviceType,
        browser: deviceInfo.browser,
        userAgent: req.headers['user-agent']
      });
    }

    // 更新session活动信息
    session.lastActivityAt = dayjs().toDate();
    session.requestCount = (session.requestCount || 0) + 1;

    // 如果是页面请求（GET请求且Accept包含text/html），增加页面浏览计数
    if (req.method === 'GET' && req.headers.accept?.includes('text/html')) {
      session.pageViews = (session.pageViews || 0) + 1;
    }

    // 设置会话统计信息到请求对象
    if (session.createdAt) {
      const sessionAge = dayjs().diff(dayjs(session.createdAt), 'second');
      (req as any).sessionStats = {
        isNewSession: !session.initialized,
        sessionAge,
        requestsInSession: session.requestCount || 0
      };
    }
    next();
  } catch (error) {
    logger.error('会话追踪中间件错误', { error: formatError(error) });
    // 出错不影响正常请求
    next();
  }
}

/**
 * 记录应用启动
 */
async function recordNewLaunch(
  walletId: number, 
  sessionId: string,
  req: Request
): Promise<void> {
  try {
    const deviceInfo = detectDeviceInfo(req.headers['user-agent']);
    const clientIP = getClientIP(req);
    
    const launchData = {
      walletId,
      sessionId,
      launchTime: dayjs().toDate(),
      date: dayjs().format('YYYY-MM-DD'), // YYYY-MM-DD格式
      userAgent: req.headers['user-agent'] || 'Unknown',
      ipAddress: clientIP,
      referrer: req.headers.referer || 'Direct',
      deviceType: deviceInfo.deviceType,
      browser: deviceInfo.browser
    };

    await AppLaunch.create(launchData);
    
    logger.info('记录应用启动', {
      walletId,
      sessionId,
      deviceType: deviceInfo.deviceType,
      browser: deviceInfo.browser,
      ipAddress: clientIP
    });
  } catch (error) {
    logger.error('记录应用启动失败', { 
      error: formatError(error), 
      walletId, 
      sessionId 
    });
    // 不抛出错误，避免影响用户正常使用
  }
}

/**
 * 更新用户最后活跃时间
 */
async function updateUserLastActiveTime(walletId: number): Promise<void> {
  try {
    await UserWallet.update(
      { lastActiveTime: dayjs().toDate() },
      { where: { id: walletId } }
    );
  } catch (error) {
    logger.error('更新用户最后活跃时间失败', {
      error: formatError(error),
      walletId
    });
  }
}

/**
 * 检测设备和浏览器信息
 */
function detectDeviceInfo(userAgent?: string): { deviceType: string; browser: string } {
  if (!userAgent) {
    return { deviceType: 'Unknown', browser: 'Unknown' };
  }

  const ua = userAgent.toLowerCase();
  
  // 检测设备类型
  let deviceType = 'Desktop';
  if (/mobile|android|iphone|ipod|blackberry|iemobile|opera mini/i.test(ua)) {
    deviceType = 'Mobile';
  } else if (/tablet|ipad|android(?!.*mobile)/i.test(ua)) {
    deviceType = 'Tablet';
  }

  // 检测浏览器类型
  let browser = 'Other';
  if (/chrome/i.test(ua) && !/edge|edg/i.test(ua)) {
    browser = 'Chrome';
  } else if (/firefox/i.test(ua)) {
    browser = 'Firefox';
  } else if (/safari/i.test(ua) && !/chrome/i.test(ua)) {
    browser = 'Safari';
  } else if (/edge|edg/i.test(ua)) {
    browser = 'Edge';
  } else if (/opera|opr/i.test(ua)) {
    browser = 'Opera';
  }

  return { deviceType, browser };
}

/**
 * 获取客户端真实IP地址
 */
function getClientIP(req: Request): string {
  // 优先从代理头中获取真实IP
  const forwarded = req.headers['x-forwarded-for'];
  if (forwarded) {
    // x-forwarded-for 可能包含多个IP，取第一个
    const ips = Array.isArray(forwarded) ? forwarded[0] : forwarded;
    return ips.split(',')[0].trim();
  }

  // 其他代理头
  const realIP = req.headers['x-real-ip'];
  if (realIP) {
    return Array.isArray(realIP) ? realIP[0] : realIP;
  }

  // 从连接信息获取
  return req.ip || req.socket.remoteAddress || 'Unknown';
}

/**
 * 获取会话统计信息（用于调试和监控）
 */
export function getSessionInfo(req: Request): any {
  if (!req.session) {
    return null;
  }

  const session = req.session as any;
  return {
    sessionId: req.sessionID,
    initialized: session.initialized,
    walletId: session.walletId,
    userId: session.userId,
    createdAt: session.createdAt,
    lastActivityAt: session.lastActivityAt,
    requestCount: session.requestCount,
    pageViews: session.pageViews,
    launchCount: session.launchCount,
    deviceType: session.deviceType,
    browser: session.browser,
    userAgent: session.userAgent,
    ipAddress: session.ipAddress,
    referrer: session.referrer,
    status: session.status
  };
}

/**
 * 手动结束会话（用于用户登出等场景）
 */
export function endSession(req: Request, reason: 'logout' | 'manual' = 'manual'): void {
  if (req.session) {
    const session = req.session as any;
    session.status = 'ended';
    session.endReason = reason;
    session.endTime = dayjs().toDate();

    logger.info('会话手动结束', {
      sessionId: req.sessionID,
      walletId: session.walletId,
      reason,
      duration: session.createdAt ?
        dayjs().diff(dayjs(session.createdAt), 'second') : 0
    });
  }
}
