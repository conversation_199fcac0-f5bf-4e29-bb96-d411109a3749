/**
 * 用户行为统计系统数据库迁移
 * 
 * 创建以下表：
 * 1. app_launches - 应用启动记录表
 * 2. session_durations - 用户会话时长记录表
 * 
 * 这些表用于实现基于会话Cookie的用户行为统计系统
 */

'use strict';

// 简单的日志函数，避免依赖问题
const logger = {
  info: (msg, data) => console.log(`[INFO] ${msg}`, data || ''),
  error: (msg, data) => console.error(`[ERROR] ${msg}`, data || ''),
  warn: (msg, data) => console.warn(`[WARN] ${msg}`, data || '')
};

const formatError = (error) => error instanceof Error ? error.message : String(error);

module.exports = {
  up: async (queryInterface, Sequelize) => {
    const transaction = await queryInterface.sequelize.transaction();
    
    try {
      logger.info('开始创建用户行为统计系统表...');

      // 1. 创建 app_launches 表
      logger.info('创建 app_launches 表...');
      await queryInterface.createTable('app_launches', {
        id: {
          type: Sequelize.INTEGER,
          primaryKey: true,
          autoIncrement: true,
          comment: '主键ID'
        },
        walletId: {
          type: Sequelize.INTEGER,
          allowNull: false,
          comment: '用户钱包ID，关联user_wallets表'
        },
        sessionId: {
          type: Sequelize.STRING(128),
          allowNull: false,
          comment: '会话ID，关联Redis中的session'
        },
        launchTime: {
          type: Sequelize.DATE,
          allowNull: false,
          comment: '启动时间'
        },
        date: {
          type: Sequelize.DATEONLY,
          allowNull: false,
          comment: '启动日期（YYYY-MM-DD格式，方便按日统计）'
        },
        userAgent: {
          type: Sequelize.STRING(500),
          allowNull: true,
          comment: '用户代理字符串'
        },
        ipAddress: {
          type: Sequelize.STRING(45),
          allowNull: true,
          comment: 'IP地址（支持IPv6）'
        },
        referrer: {
          type: Sequelize.STRING(500),
          allowNull: true,
          comment: '来源页面URL'
        },
        deviceType: {
          type: Sequelize.STRING(20),
          allowNull: true,
          comment: '设备类型：Desktop/Mobile/Tablet'
        },
        browser: {
          type: Sequelize.STRING(20),
          allowNull: true,
          comment: '浏览器类型：Chrome/Firefox/Safari/Edge等'
        },
        createdAt: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.NOW,
          comment: '创建时间'
        },
        updatedAt: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.NOW,
          comment: '更新时间'
        }
      }, {
        transaction,
        comment: '应用启动记录表'
      });

      // 2. 创建 session_durations 表
      logger.info('创建 session_durations 表...');
      await queryInterface.createTable('session_durations', {
        id: {
          type: Sequelize.INTEGER,
          primaryKey: true,
          autoIncrement: true,
          comment: '主键ID'
        },
        sessionId: {
          type: Sequelize.STRING(128),
          allowNull: false,
          unique: true,
          comment: '会话ID，关联Redis中的session'
        },
        walletId: {
          type: Sequelize.INTEGER,
          allowNull: false,
          comment: '用户钱包ID，关联user_wallets表'
        },
        startTime: {
          type: Sequelize.DATE,
          allowNull: false,
          comment: '会话开始时间'
        },
        lastActivityTime: {
          type: Sequelize.DATE,
          allowNull: false,
          comment: '最后活跃时间'
        },
        duration: {
          type: Sequelize.INTEGER,
          allowNull: false,
          defaultValue: 0,
          comment: '会话时长（秒）'
        },
        pageViews: {
          type: Sequelize.INTEGER,
          allowNull: false,
          defaultValue: 0,
          comment: '页面浏览次数'
        },
        requestCount: {
          type: Sequelize.INTEGER,
          allowNull: false,
          defaultValue: 0,
          comment: 'API请求次数'
        },
        status: {
          type: Sequelize.ENUM('active', 'ended', 'timeout'),
          allowNull: false,
          defaultValue: 'active',
          comment: '会话状态'
        },
        endReason: {
          type: Sequelize.ENUM('logout', 'timeout', 'browser_close', 'manual'),
          allowNull: true,
          comment: '会话结束原因'
        },
        deviceType: {
          type: Sequelize.STRING(20),
          allowNull: true,
          comment: '设备类型'
        },
        browser: {
          type: Sequelize.STRING(20),
          allowNull: true,
          comment: '浏览器类型'
        },
        createdAt: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.NOW,
          comment: '创建时间'
        },
        updatedAt: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.NOW,
          comment: '更新时间'
        }
      }, {
        transaction,
        comment: '用户会话时长记录表'
      });

      // 3. 创建 app_launches 表的索引
      logger.info('创建 app_launches 表索引...');
      await queryInterface.addIndex('app_launches', ['walletId', 'date'], {
        name: 'idx_wallet_date',
        transaction
      });
      
      await queryInterface.addIndex('app_launches', ['date'], {
        name: 'idx_date',
        transaction
      });
      
      await queryInterface.addIndex('app_launches', ['sessionId'], {
        name: 'idx_session',
        transaction
      });
      
      await queryInterface.addIndex('app_launches', ['launchTime'], {
        name: 'idx_launch_time',
        transaction
      });
      
      await queryInterface.addIndex('app_launches', ['deviceType'], {
        name: 'idx_device_type',
        transaction
      });
      
      await queryInterface.addIndex('app_launches', ['browser'], {
        name: 'idx_browser',
        transaction
      });

      // 4. 创建 session_durations 表的索引
      logger.info('创建 session_durations 表索引...');
      await queryInterface.addIndex('session_durations', ['sessionId'], {
        name: 'idx_session_id',
        unique: true,
        transaction
      });
      
      await queryInterface.addIndex('session_durations', ['walletId', 'startTime'], {
        name: 'idx_wallet_time',
        transaction
      });
      
      await queryInterface.addIndex('session_durations', ['startTime'], {
        name: 'idx_start_time',
        transaction
      });
      
      await queryInterface.addIndex('session_durations', ['duration'], {
        name: 'idx_duration',
        transaction
      });
      
      await queryInterface.addIndex('session_durations', ['status'], {
        name: 'idx_status',
        transaction
      });

      await transaction.commit();
      logger.info('✅ 用户行为统计系统表创建完成');
      logger.info('   - app_launches 表及其索引');
      logger.info('   - session_durations 表及其索引');
      
    } catch (error) {
      await transaction.rollback();
      logger.error('❌ 用户行为统计系统表创建失败:', formatError(error));
      throw error;
    }
  },

  down: async (queryInterface, Sequelize) => {
    const transaction = await queryInterface.sequelize.transaction();
    
    try {
      logger.info('开始回滚用户行为统计系统表...');

      // 删除表（索引会自动删除）
      await queryInterface.dropTable('session_durations', { transaction });
      logger.info('已删除 session_durations 表');
      
      await queryInterface.dropTable('app_launches', { transaction });
      logger.info('已删除 app_launches 表');

      await transaction.commit();
      logger.info('✅ 用户行为统计系统表回滚完成');
      
    } catch (error) {
      await transaction.rollback();
      logger.error('❌ 用户行为统计系统表回滚失败:', formatError(error));
      throw error;
    }
  }
};
