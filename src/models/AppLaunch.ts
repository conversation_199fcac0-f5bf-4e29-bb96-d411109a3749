/**
 * AppLaunch模型
 * 
 * 用于记录应用启动信息的数据库模型
 * 每次用户启动应用（新建会话）时创建一条记录
 */

import { Model, DataTypes, Sequelize, Optional } from 'sequelize';
import dayjs from 'dayjs';

/**
 * AppLaunch属性接口
 */
export interface AppLaunchAttributes {
  id: number;
  walletId: number;
  sessionId: string;
  launchTime: Date;
  date: string; // YYYY-MM-DD格式，方便按日统计
  userAgent?: string;
  ipAddress?: string;
  referrer?: string;
  deviceType?: string;
  browser?: string;
  createdAt?: Date;
  updatedAt?: Date;
}

/**
 * AppLaunch创建属性接口（id为可选）
 */
export interface AppLaunchCreationAttributes 
  extends Optional<AppLaunchAttributes, 'id' | 'createdAt' | 'updatedAt'> {}

/**
 * AppLaunch模型类
 */
export class AppLaunch extends Model<AppLaunchAttributes, AppLaunchCreationAttributes> 
  implements AppLaunchAttributes {
  
  public id!: number;
  public walletId!: number;
  public sessionId!: string;
  public launchTime!: Date;
  public date!: string;
  public userAgent?: string;
  public ipAddress?: string;
  public referrer?: string;
  public deviceType?: string;
  public browser?: string;
  
  // 时间戳
  public readonly createdAt!: Date;
  public readonly updatedAt!: Date;

  /**
   * 获取启动时间的格式化字符串
   */
  public getFormattedLaunchTime(): string {
    return dayjs(this.launchTime).format('YYYY-MM-DD HH:mm:ss');
  }

  /**
   * 获取设备信息摘要
   */
  public getDeviceSummary(): string {
    const parts = [];
    if (this.deviceType) parts.push(this.deviceType);
    if (this.browser) parts.push(this.browser);
    return parts.join(' - ') || 'Unknown';
  }

  /**
   * 检查是否为移动设备
   */
  public isMobileDevice(): boolean {
    return this.deviceType === 'Mobile' || this.deviceType === 'Tablet';
  }

  /**
   * 获取启动时间的小时数（0-23）
   */
  public getLaunchHour(): number {
    return this.launchTime.getHours();
  }
}

/**
 * 初始化AppLaunch模型
 */
export function initAppLaunch(sequelize: Sequelize): void {
  AppLaunch.init(
    {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        comment: '主键ID'
      },
      walletId: {
        type: DataTypes.INTEGER,
        allowNull: false,
        comment: '用户钱包ID，关联user_wallets表'
      },
      sessionId: {
        type: DataTypes.STRING(128),
        allowNull: false,
        comment: '会话ID，关联Redis中的session'
      },
      launchTime: {
        type: DataTypes.DATE,
        allowNull: false,
        comment: '启动时间'
      },
      date: {
        type: DataTypes.DATEONLY,
        allowNull: false,
        comment: '启动日期（YYYY-MM-DD格式，方便按日统计）'
      },
      userAgent: {
        type: DataTypes.STRING(500),
        allowNull: true,
        comment: '用户代理字符串'
      },
      ipAddress: {
        type: DataTypes.STRING(45),
        allowNull: true,
        comment: 'IP地址（支持IPv6）'
      },
      referrer: {
        type: DataTypes.STRING(500),
        allowNull: true,
        comment: '来源页面URL'
      },
      deviceType: {
        type: DataTypes.STRING(20),
        allowNull: true,
        comment: '设备类型：Desktop/Mobile/Tablet'
      },
      browser: {
        type: DataTypes.STRING(20),
        allowNull: true,
        comment: '浏览器类型：Chrome/Firefox/Safari/Edge等'
      }
    },
    {
      sequelize,
      modelName: 'AppLaunch',
      tableName: 'app_launches',
      timestamps: true,
      indexes: [
        {
          name: 'idx_wallet_date',
          fields: ['walletId', 'date']
        },
        {
          name: 'idx_date',
          fields: ['date']
        },
        {
          name: 'idx_session',
          fields: ['sessionId']
        },
        {
          name: 'idx_launch_time',
          fields: ['launchTime']
        },
        {
          name: 'idx_device_type',
          fields: ['deviceType']
        },
        {
          name: 'idx_browser',
          fields: ['browser']
        }
      ],
      comment: '应用启动记录表'
    }
  );
}

/**
 * 设置模型关联关系
 */
export function setupAppLaunchAssociations(): void {
  // 这里可以设置与其他模型的关联关系
  // 例如：AppLaunch.belongsTo(UserWallet, { foreignKey: 'walletId' });
}

/**
 * AppLaunch模型的静态方法扩展
 */
export class AppLaunchService {
  /**
   * 创建启动记录
   */
  static async createLaunch(data: AppLaunchCreationAttributes): Promise<AppLaunch> {
    return await AppLaunch.create(data);
  }

  /**
   * 获取指定日期的启动次数
   */
  static async getDailyLaunchCount(date: string): Promise<number> {
    return await AppLaunch.count({
      where: { date }
    });
  }

  /**
   * 获取指定用户的启动次数
   */
  static async getUserLaunchCount(walletId: number, startDate?: Date, endDate?: Date): Promise<number> {
    const where: any = { walletId };
    
    if (startDate && endDate) {
      where.launchTime = {
        [require('sequelize').Op.gte]: startDate,
        [require('sequelize').Op.lt]: endDate
      };
    }

    return await AppLaunch.count({ where });
  }

  /**
   * 获取设备类型分布
   */
  static async getDeviceTypeDistribution(date?: string): Promise<Array<{deviceType: string, count: number}>> {
    const where = date ? { date } : {};
    
    const results = await AppLaunch.findAll({
      attributes: [
        'deviceType',
        [require('sequelize').fn('COUNT', '*'), 'count']
      ],
      where,
      group: ['deviceType'],
      raw: true
    }) as any[];

    return results.map(r => ({
      deviceType: r.deviceType || 'Unknown',
      count: parseInt(r.count)
    }));
  }
}
