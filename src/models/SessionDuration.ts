/**
 * SessionDuration模型
 * 
 * 用于记录用户会话时长信息的数据库模型
 * 通过定期保存Redis中的session数据来估算游玩时长
 */

import { Model, DataTypes, Sequelize, Optional } from 'sequelize';
import dayjs from 'dayjs';

/**
 * SessionDuration属性接口
 */
export interface SessionDurationAttributes {
  id: number;
  sessionId: string;
  walletId: number;
  startTime: Date;
  lastActivityTime: Date;
  duration: number; // 会话时长（秒）
  pageViews: number; // 页面浏览次数
  requestCount: number; // API请求次数
  status: 'active' | 'ended' | 'timeout';
  endReason?: 'logout' | 'timeout' | 'browser_close' | 'manual';
  deviceType?: string;
  browser?: string;
  createdAt?: Date;
  updatedAt?: Date;
}

/**
 * SessionDuration创建属性接口（id为可选）
 */
export interface SessionDurationCreationAttributes 
  extends Optional<SessionDurationAttributes, 'id' | 'createdAt' | 'updatedAt'> {}

/**
 * SessionDuration模型类
 */
export class SessionDuration extends Model<SessionDurationAttributes, SessionDurationCreationAttributes> 
  implements SessionDurationAttributes {
  
  public id!: number;
  public sessionId!: string;
  public walletId!: number;
  public startTime!: Date;
  public lastActivityTime!: Date;
  public duration!: number;
  public pageViews!: number;
  public requestCount!: number;
  public status!: 'active' | 'ended' | 'timeout';
  public endReason?: 'logout' | 'timeout' | 'browser_close' | 'manual';
  public deviceType?: string;
  public browser?: string;
  
  // 时间戳
  public readonly createdAt!: Date;
  public readonly updatedAt!: Date;

  /**
   * 获取格式化的时长字符串
   */
  public getFormattedDuration(): string {
    const hours = Math.floor(this.duration / 3600);
    const minutes = Math.floor((this.duration % 3600) / 60);
    const seconds = this.duration % 60;

    if (hours > 0) {
      return `${hours}小时${minutes}分钟${seconds}秒`;
    } else if (minutes > 0) {
      return `${minutes}分钟${seconds}秒`;
    } else {
      return `${seconds}秒`;
    }
  }

  /**
   * 获取时长（分钟）
   */
  public getDurationInMinutes(): number {
    return Math.round(this.duration / 60);
  }

  /**
   * 获取时长（小时）
   */
  public getDurationInHours(): number {
    return Math.round(this.duration / 3600 * 100) / 100; // 保留2位小数
  }

  /**
   * 检查是否为有效会话（时长大于30秒）
   */
  public isValidSession(): boolean {
    return this.duration >= 30;
  }

  /**
   * 检查是否为长会话（时长大于30分钟）
   */
  public isLongSession(): boolean {
    return this.duration >= 1800; // 30分钟
  }

  /**
   * 计算平均每分钟的活动次数
   */
  public getActivityRate(): number {
    const minutes = this.duration / 60;
    return minutes > 0 ? Math.round((this.requestCount / minutes) * 100) / 100 : 0;
  }
}

/**
 * 初始化SessionDuration模型
 */
export function initSessionDuration(sequelize: Sequelize): void {
  SessionDuration.init(
    {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        comment: '主键ID'
      },
      sessionId: {
        type: DataTypes.STRING(128),
        allowNull: false,
        unique: true,
        comment: '会话ID，关联Redis中的session'
      },
      walletId: {
        type: DataTypes.INTEGER,
        allowNull: false,
        comment: '用户钱包ID，关联user_wallets表'
      },
      startTime: {
        type: DataTypes.DATE,
        allowNull: false,
        comment: '会话开始时间'
      },
      lastActivityTime: {
        type: DataTypes.DATE,
        allowNull: false,
        comment: '最后活跃时间'
      },
      duration: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0,
        comment: '会话时长（秒）'
      },
      pageViews: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0,
        comment: '页面浏览次数'
      },
      requestCount: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0,
        comment: 'API请求次数'
      },
      status: {
        type: DataTypes.ENUM('active', 'ended', 'timeout'),
        allowNull: false,
        defaultValue: 'active',
        comment: '会话状态'
      },
      endReason: {
        type: DataTypes.ENUM('logout', 'timeout', 'browser_close', 'manual'),
        allowNull: true,
        comment: '会话结束原因'
      },
      deviceType: {
        type: DataTypes.STRING(20),
        allowNull: true,
        comment: '设备类型'
      },
      browser: {
        type: DataTypes.STRING(20),
        allowNull: true,
        comment: '浏览器类型'
      }
    },
    {
      sequelize,
      modelName: 'SessionDuration',
      tableName: 'session_durations',
      timestamps: true,
      indexes: [
        {
          name: 'idx_session_id',
          fields: ['sessionId'],
          unique: true
        },
        {
          name: 'idx_wallet_time',
          fields: ['walletId', 'startTime']
        },
        {
          name: 'idx_start_time',
          fields: ['startTime']
        },
        {
          name: 'idx_duration',
          fields: ['duration']
        },
        {
          name: 'idx_status',
          fields: ['status']
        }
      ],
      comment: '用户会话时长记录表'
    }
  );
}

/**
 * SessionDuration模型的静态方法扩展
 */
export class SessionDurationService {
  /**
   * 创建或更新会话时长记录
   */
  static async upsertSessionDuration(data: SessionDurationCreationAttributes): Promise<SessionDuration> {
    const [instance, created] = await SessionDuration.upsert(data, {
      returning: true
    });
    return instance;
  }

  /**
   * 获取指定日期的平均游玩时长（分钟）
   */
  static async getAveragePlayTime(startDate: Date, endDate: Date): Promise<number> {
    const result = await SessionDuration.findOne({
      attributes: [
        [require('sequelize').fn('AVG', require('sequelize').col('duration')), 'avgDuration']
      ],
      where: {
        startTime: {
          [require('sequelize').Op.gte]: startDate,
          [require('sequelize').Op.lt]: endDate
        },
        duration: {
          [require('sequelize').Op.gt]: 0, // 排除0时长的记录
          [require('sequelize').Op.lt]: 86400 // 排除超过24小时的异常记录
        }
      },
      raw: true
    }) as any;

    const avgSeconds = result?.avgDuration || 0;
    return Math.round(avgSeconds / 60); // 转换为分钟
  }

  /**
   * 获取指定用户的总游玩时长（分钟）
   */
  static async getUserTotalPlayTime(walletId: number, startDate?: Date, endDate?: Date): Promise<number> {
    const where: any = { 
      walletId,
      duration: {
        [require('sequelize').Op.gt]: 0
      }
    };
    
    if (startDate && endDate) {
      where.startTime = {
        [require('sequelize').Op.gte]: startDate,
        [require('sequelize').Op.lt]: endDate
      };
    }

    const result = await SessionDuration.findOne({
      attributes: [
        [require('sequelize').fn('SUM', require('sequelize').col('duration')), 'totalDuration']
      ],
      where,
      raw: true
    }) as any;

    const totalSeconds = result?.totalDuration || 0;
    return Math.round(totalSeconds / 60); // 转换为分钟
  }

  /**
   * 获取会话时长分布统计
   */
  static async getSessionDurationDistribution(date?: string): Promise<Array<{range: string, count: number}>> {
    const where: any = {
      duration: {
        [require('sequelize').Op.gt]: 0
      }
    };

    if (date) {
      const startDate = dayjs(date).toDate();
      const endDate = dayjs(date).add(1, 'day').toDate();

      where.startTime = {
        [require('sequelize').Op.gte]: startDate,
        [require('sequelize').Op.lt]: endDate
      };
    }

    const sessions = await SessionDuration.findAll({
      attributes: ['duration'],
      where,
      raw: true
    });

    // 按时长范围分组统计
    const ranges = [
      { range: '0-5分钟', min: 0, max: 300 },
      { range: '5-15分钟', min: 300, max: 900 },
      { range: '15-30分钟', min: 900, max: 1800 },
      { range: '30-60分钟', min: 1800, max: 3600 },
      { range: '1小时以上', min: 3600, max: Infinity }
    ];

    return ranges.map(range => ({
      range: range.range,
      count: sessions.filter(s => s.duration >= range.min && s.duration < range.max).length
    }));
  }
}
