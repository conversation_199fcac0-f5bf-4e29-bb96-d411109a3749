/**
 * 用户行为统计API路由
 * 
 * 提供基于会话Cookie的用户行为统计数据
 * 包括启动次数、游玩时长、设备分布等统计信息
 */

import { Router } from 'express';
import { Request, Response } from 'express';
import { adminAuthMiddleware } from '../../middlewares/adminAuth';
import { AppLaunch } from '../../models/AppLaunch';
import { SessionDuration } from '../../models/SessionDuration';
import { sequelize } from '../../config/db';
import { QueryTypes, Op } from 'sequelize';
import { logger, formatError } from '../../utils/logger';
import { getSessionStats } from '../../config/sessionConfig';
import dayjs from 'dayjs';

const router = Router();

// 应用管理员认证中间件到所有路由
router.use(adminAuthMiddleware);

/**
 * @route GET /api/admin/user-behavior/daily-launches
 * @desc 获取每日启动次数
 * @query date - 日期 (YYYY-MM-DD格式，默认今天)
 */
router.get('/daily-launches', async (req: Request, res: Response) => {
  try {
    const date = (req.query.date as string) || dayjs().format('YYYY-MM-DD');

    const startDate = dayjs(date).toDate();
    const endDate = dayjs(date).add(1, 'day').toDate();
    
    const launches = await AppLaunch.count({
      where: {
        launchTime: {
          [Op.gte]: startDate,
          [Op.lt]: endDate
        }
      }
    });
    
    const uniqueUsers = await AppLaunch.count({
      distinct: true,
      col: 'walletId',
      where: {
        launchTime: {
          [Op.gte]: startDate,
          [Op.lt]: endDate
        }
      }
    });
    
    res.json({
      success: true,
      data: {
        date,
        totalLaunches: launches,
        uniqueUsers,
        averageLaunchesPerUser: uniqueUsers > 0 ? Math.round((launches / uniqueUsers) * 100) / 100 : 0
      }
    });
  } catch (error) {
    logger.error('获取每日启动次数失败', { error: formatError(error) });
    res.status(500).json({
      success: false,
      error: '获取数据失败'
    });
  }
});

/**
 * @route GET /api/admin/user-behavior/average-playtime
 * @desc 获取平均游玩时长
 * @query date - 日期 (YYYY-MM-DD格式，默认今天)
 */
router.get('/average-playtime', async (req: Request, res: Response) => {
  try {
    const date = (req.query.date as string) || dayjs().format('YYYY-MM-DD');

    const startDate = dayjs(date).toDate();
    const endDate = dayjs(date).add(1, 'day').toDate();
    
    const result = await sequelize.query(`
      SELECT 
        AVG(sd.duration) as avgDurationSeconds,
        COUNT(DISTINCT sd.walletId) as uniqueUsers,
        COUNT(*) as totalSessions,
        MIN(sd.duration) as minDuration,
        MAX(sd.duration) as maxDuration
      FROM session_durations sd
      INNER JOIN app_launches al ON sd.sessionId = al.sessionId
      WHERE al.launchTime >= :startDate 
        AND al.launchTime < :endDate
        AND sd.duration > 0
        AND sd.duration < 86400
    `, {
      replacements: { startDate, endDate },
      type: QueryTypes.SELECT,
    }) as any[];
    
    const avgSeconds = result[0]?.avgDurationSeconds || 0;
    const avgMinutes = Math.round(avgSeconds / 60);
    
    function formatDuration(seconds: number): string {
      const hours = Math.floor(seconds / 3600);
      const minutes = Math.floor((seconds % 3600) / 60);
      const secs = seconds % 60;
      
      if (hours > 0) {
        return `${hours}小时${minutes}分钟${secs}秒`;
      } else if (minutes > 0) {
        return `${minutes}分钟${secs}秒`;
      } else {
        return `${secs}秒`;
      }
    }
    
    res.json({
      success: true,
      data: {
        date,
        averagePlayTimeMinutes: avgMinutes,
        averagePlayTimeFormatted: formatDuration(Math.round(avgSeconds)),
        statistics: {
          uniqueUsers: result[0]?.uniqueUsers || 0,
          totalSessions: result[0]?.totalSessions || 0,
          minDuration: result[0]?.minDuration || 0,
          maxDuration: result[0]?.maxDuration || 0,
          minDurationFormatted: formatDuration(result[0]?.minDuration || 0),
          maxDurationFormatted: formatDuration(result[0]?.maxDuration || 0)
        }
      }
    });
  } catch (error) {
    logger.error('获取平均游玩时长失败', { error: formatError(error) });
    res.status(500).json({
      success: false,
      error: '获取数据失败'
    });
  }
});

/**
 * @route GET /api/admin/user-behavior/detailed-stats/:date
 * @desc 获取详细统计信息
 * @param date - 日期 (YYYY-MM-DD格式)
 */
router.get('/detailed-stats/:date', async (req: Request, res: Response) => {
  try {
    const { date } = req.params;
    
    // 按小时分布
    const hourlyStats = await sequelize.query(`
      SELECT 
        HOUR(launchTime) as hour,
        COUNT(*) as launches,
        COUNT(DISTINCT walletId) as uniqueUsers
      FROM app_launches
      WHERE date = :date
      GROUP BY HOUR(launchTime)
      ORDER BY hour
    `, {
      replacements: { date },
      type: QueryTypes.SELECT
    });

    // 按设备类型分布
    const deviceStats = await sequelize.query(`
      SELECT 
        deviceType,
        COUNT(*) as launches,
        COUNT(DISTINCT walletId) as uniqueUsers
      FROM app_launches
      WHERE date = :date
      GROUP BY deviceType
    `, {
      replacements: { date },
      type: QueryTypes.SELECT
    });

    // 按浏览器分布
    const browserStats = await sequelize.query(`
      SELECT 
        browser,
        COUNT(*) as launches,
        COUNT(DISTINCT walletId) as uniqueUsers
      FROM app_launches
      WHERE date = :date
      GROUP BY browser
    `, {
      replacements: { date },
      type: QueryTypes.SELECT
    });

    // 会话时长分布
    const durationDistribution = await sequelize.query(`
      SELECT 
        CASE 
          WHEN sd.duration < 300 THEN '0-5分钟'
          WHEN sd.duration < 900 THEN '5-15分钟'
          WHEN sd.duration < 1800 THEN '15-30分钟'
          WHEN sd.duration < 3600 THEN '30-60分钟'
          ELSE '1小时以上'
        END as durationRange,
        COUNT(*) as sessionCount,
        COUNT(DISTINCT sd.walletId) as uniqueUsers
      FROM session_durations sd
      INNER JOIN app_launches al ON sd.sessionId = al.sessionId
      WHERE DATE(al.launchTime) = :date
        AND sd.duration > 0
      GROUP BY durationRange
      ORDER BY MIN(sd.duration)
    `, {
      replacements: { date },
      type: QueryTypes.SELECT
    });

    res.json({
      success: true,
      data: {
        date,
        hourlyDistribution: hourlyStats,
        deviceDistribution: deviceStats,
        browserDistribution: browserStats,
        durationDistribution
      }
    });
  } catch (error) {
    logger.error('获取详细统计失败', { error: formatError(error) });
    res.status(500).json({
      success: false,
      error: '获取数据失败'
    });
  }
});

/**
 * @route GET /api/admin/user-behavior/session-info
 * @desc 获取当前Session统计信息
 */
router.get('/session-info', async (req: Request, res: Response) => {
  try {
    const sessionStats = await getSessionStats();
    
    res.json({
      success: true,
      data: sessionStats
    });
  } catch (error) {
    logger.error('获取Session信息失败', { error: formatError(error) });
    res.status(500).json({
      success: false,
      error: '获取数据失败'
    });
  }
});

/**
 * @route GET /api/admin/user-behavior/trends
 * @desc 获取用户行为趋势数据
 * @query days - 天数 (默认7天)
 */
router.get('/trends', async (req: Request, res: Response) => {
  try {
    const days = parseInt(req.query.days as string) || 7;
    const endDate = dayjs().toDate();
    const startDate = dayjs().subtract(days, 'day').toDate();
    
    // 每日启动趋势
    const launchTrend = await sequelize.query(`
      SELECT 
        date,
        COUNT(*) as launches,
        COUNT(DISTINCT walletId) as uniqueUsers
      FROM app_launches
      WHERE launchTime >= :startDate AND launchTime <= :endDate
      GROUP BY date
      ORDER BY date ASC
    `, {
      replacements: { startDate, endDate },
      type: QueryTypes.SELECT
    });
    
    // 每日平均游玩时长趋势
    const playtimeTrend = await sequelize.query(`
      SELECT 
        DATE(al.launchTime) as date,
        AVG(sd.duration) / 60 as avgMinutes,
        COUNT(sd.id) as sessionCount
      FROM session_durations sd
      INNER JOIN app_launches al ON sd.sessionId = al.sessionId
      WHERE al.launchTime >= :startDate 
        AND al.launchTime <= :endDate
        AND sd.duration > 0
        AND sd.duration < 86400
      GROUP BY DATE(al.launchTime)
      ORDER BY date ASC
    `, {
      replacements: { startDate, endDate },
      type: QueryTypes.SELECT
    });
    
    res.json({
      success: true,
      data: {
        period: {
          startDate: dayjs(startDate).format('YYYY-MM-DD'),
          endDate: dayjs(endDate).format('YYYY-MM-DD'),
          days
        },
        launchTrend,
        playtimeTrend
      }
    });
  } catch (error) {
    logger.error('获取趋势数据失败', { error: formatError(error) });
    res.status(500).json({
      success: false,
      error: '获取数据失败'
    });
  }
});

export default router;
