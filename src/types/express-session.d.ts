/**
 * Express Session类型扩展
 * 
 * 扩展express-session的SessionData接口，
 * 添加用户行为统计系统所需的字段
 */

import 'express-session';

declare module 'express-session' {
  interface SessionData {
    /**
     * Session是否已初始化
     * 用于标识这是一个新的用户会话
     */
    initialized?: boolean;

    /**
     * 用户钱包ID
     * 关联到UserWallet表的主键
     */
    walletId?: number;

    /**
     * 用户ID
     * 关联到User表的主键
     */
    userId?: number;

    /**
     * Session创建时间
     * 用于计算会话时长
     */
    createdAt?: Date;

    /**
     * 最后活跃时间
     * 每次请求都会更新，用于计算实际游玩时长
     */
    lastActivityAt?: Date;

    /**
     * 请求计数
     * 统计该会话中的API请求次数
     */
    requestCount?: number;

    /**
     * 启动计数
     * 该会话的启动次数（通常为1，除非有特殊情况）
     */
    launchCount?: number;

    /**
     * 页面浏览计数
     * 统计页面访问次数
     */
    pageViews?: number;

    /**
     * 用户代理信息
     * 浏览器和设备信息
     */
    userAgent?: string;

    /**
     * IP地址
     * 用户的IP地址
     */
    ipAddress?: string;

    /**
     * 设备类型
     * Desktop/Mobile/Tablet
     */
    deviceType?: string;

    /**
     * 浏览器类型
     * Chrome/Firefox/Safari/Edge等
     */
    browser?: string;

    /**
     * 来源页面
     * HTTP Referer
     */
    referrer?: string;

    /**
     * 会话状态
     * active: 活跃中
     * idle: 空闲
     * ended: 已结束
     */
    status?: 'active' | 'idle' | 'ended';

    /**
     * 最后保存时间
     * 用于跟踪session数据的持久化时间
     */
    lastSavedAt?: Date;

    /**
     * 游戏相关数据
     */
    gameData?: {
      /**
       * 最后访问的游戏功能
       */
      lastFeature?: string;

      /**
       * 游戏操作计数
       */
      gameActions?: number;

      /**
       * 农场区访问次数
       */
      farmVisits?: number;

      /**
       * 配送线访问次数
       */
      deliveryVisits?: number;
    };

    /**
     * 扩展字段
     * 允许添加其他自定义数据
     */
    [key: string]: any;
  }
}

/**
 * 扩展Request接口，添加用户信息
 */
declare global {
  namespace Express {
    interface Request {
      /**
       * 用户信息（来自JWT认证）
       */
      user?: {
        userId: number;
        walletId?: number;
        [key: string]: any;
      };

      /**
       * Session统计信息
       */
      sessionStats?: {
        isNewSession: boolean;
        sessionAge: number; // 会话存在时间（秒）
        requestsInSession: number;
      };
    }
  }
}

export {};
