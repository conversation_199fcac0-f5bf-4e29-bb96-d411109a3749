/**
 * 管理员统计系统测试脚本
 * 用于测试所有统计API接口
 */

const axios = require('axios');

// 配置
const BASE_URL = process.env.API_URL || 'http://localhost:3001';
const ADMIN_USERNAME = process.env.ADMIN_USERNAME || 'admin';
const ADMIN_PASSWORD = process.env.ADMIN_PASSWORD || 'admin123456';

// 颜色输出
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
};

// 辅助函数
function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`);
}

function logSuccess(message) {
  log(`✅ ${message}`, colors.green);
}

function logError(message) {
  log(`❌ ${message}`, colors.red);
}

function logInfo(message) {
  log(`ℹ️  ${message}`, colors.cyan);
}

function logSection(title) {
  console.log('\n' + '='.repeat(60));
  log(title, colors.bright + colors.blue);
  console.log('='.repeat(60));
}

// 登录获取Token
async function getAdminToken() {
  try {
    logInfo('正在登录管理员账号...');
    const response = await axios.post(`${BASE_URL}/api/admin/auth/login`, {
      username: ADMIN_USERNAME,
      password: ADMIN_PASSWORD,
    });

    if (response.data.ok && response.data.data.accessToken) {
      logSuccess('管理员登录成功');
      return response.data.data.accessToken;
    } else {
      throw new Error('登录失败：' + JSON.stringify(response.data));
    }
  } catch (error) {
    logError('管理员登录失败: ' + error.message);
    if (error.response) {
      console.error('响应数据:', error.response.data);
    }
    throw error;
  }
}

// 测试API接口
async function testAPI(token, endpoint, description, params = {}) {
  try {
    logInfo(`测试: ${description}`);
    const url = `${BASE_URL}/api/admin/statistics${endpoint}`;
    
    const response = await axios.get(url, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
      params,
    });

    if (response.data.success) {
      logSuccess(`${description} - 成功`);
      
      // 显示部分返回数据
      if (response.data.data) {
        const data = response.data.data;
        
        // 根据不同的端点显示相关信息
        if (endpoint === '/dashboard') {
          console.log('  - 用户统计:', {
            每日新增: data.userBehavior?.dailyNewUsers,
            七日留存: data.userBehavior?.sevenDayRetention + '%',
          });
          console.log('  - 游戏进度:', {
            区域解锁数: data.gameProgress?.farmAreaUnlocks?.length,
            流水线等级分布: data.gameProgress?.deliveryLineLevelDistribution?.length,
          });
        } else if (endpoint === '/users') {
          console.log('  - 用户行为:', {
            每日启动次数: data.statistics?.dailyAppOpens,
            平均游玩时长: data.statistics?.averagePlayTime + '分钟',
          });
        } else if (endpoint === '/economy') {
          console.log('  - 经济数据:', {
            总充值: data.purchase?.statistics?.totalRevenue + ' USD',
            ARPU: data.purchase?.statistics?.arpu,
          });
        } else if (endpoint.includes('/trends')) {
          console.log('  - 趋势数据点数:', Array.isArray(data) ? data.length : data.length || 0);
        }
      }
      
      return true;
    } else {
      logError(`${description} - 失败: ${response.data.error}`);
      return false;
    }
  } catch (error) {
    logError(`${description} - 错误: ${error.message}`);
    if (error.response) {
      console.error('  响应状态:', error.response.status);
      console.error('  响应数据:', error.response.data);
    }
    return false;
  }
}

// 测试Excel导出
async function testExcelExport(token) {
  try {
    logInfo('测试Excel导出功能...');
    const url = `${BASE_URL}/api/admin/statistics/export`;
    
    const response = await axios.get(url, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
      responseType: 'arraybuffer',
    });

    if (response.status === 200 && response.data) {
      const fs = require('fs');
      const filename = `statistics-test-${new Date().toISOString().split('T')[0]}.xlsx`;
      fs.writeFileSync(filename, response.data);
      logSuccess(`Excel导出成功 - 文件已保存为: ${filename}`);
      return true;
    }
  } catch (error) {
    logError('Excel导出测试失败: ' + error.message);
    return false;
  }
}

// 主测试函数
async function runTests() {
  log('\n🚀 开始测试管理员统计系统', colors.bright + colors.cyan);
  log(`服务器地址: ${BASE_URL}`, colors.yellow);
  log(`测试时间: ${new Date().toLocaleString('zh-CN')}`, colors.yellow);

  let token;
  let successCount = 0;
  let failCount = 0;

  try {
    // 1. 获取管理员Token
    logSection('1. 管理员认证');
    token = await getAdminToken();

    // 2. 测试主要统计接口
    logSection('2. 主要统计接口');
    
    const mainTests = [
      { endpoint: '/dashboard', description: '综合统计仪表板' },
      { endpoint: '/users', description: '用户统计详情' },
      { endpoint: '/progress', description: '游戏进度统计' },
      { endpoint: '/economy', description: '经济系统统计' },
      { endpoint: '/social', description: '社交系统统计' },
    ];

    for (const test of mainTests) {
      const success = await testAPI(token, test.endpoint, test.description);
      if (success) successCount++;
      else failCount++;
      await new Promise(resolve => setTimeout(resolve, 500)); // 避免请求过快
    }

    // 3. 测试日期范围查询
    logSection('3. 日期范围查询');
    
    const dateRangeTests = [
      {
        endpoint: '/dashboard',
        description: '仪表板 - 最近7天',
        params: {
          startDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          endDate: new Date().toISOString().split('T')[0],
        },
      },
      {
        endpoint: '/users',
        description: '用户统计 - 本月',
        params: {
          startDate: new Date(new Date().getFullYear(), new Date().getMonth(), 1).toISOString().split('T')[0],
          endDate: new Date().toISOString().split('T')[0],
        },
      },
    ];

    for (const test of dateRangeTests) {
      const success = await testAPI(token, test.endpoint, test.description, test.params);
      if (success) successCount++;
      else failCount++;
      await new Promise(resolve => setTimeout(resolve, 500));
    }

    // 4. 测试趋势分析接口
    logSection('4. 趋势分析接口');
    
    const trendTests = [
      { endpoint: '/trends/users', description: '用户增长趋势', params: { days: 7 } },
      { endpoint: '/trends/revenue', description: '收入趋势', params: { days: 30 } },
      { endpoint: '/trends/tasks', description: '任务完成趋势', params: { days: 14 } },
      { endpoint: '/trends/rewards', description: '奖励趋势', params: { days: 7 } },
      { endpoint: '/trends/items', description: '道具使用趋势', params: { days: 7 } },
    ];

    for (const test of trendTests) {
      const success = await testAPI(token, test.endpoint, test.description, test.params);
      if (success) successCount++;
      else failCount++;
      await new Promise(resolve => setTimeout(resolve, 500));
    }

    // 5. 测试Excel导出
    logSection('5. Excel导出功能');
    const exportSuccess = await testExcelExport(token);
    if (exportSuccess) successCount++;
    else failCount++;

  } catch (error) {
    logError('测试过程中发生错误: ' + error.message);
  }

  // 显示测试结果
  logSection('测试结果汇总');
  log(`总测试数: ${successCount + failCount}`, colors.yellow);
  logSuccess(`成功: ${successCount}`);
  if (failCount > 0) {
    logError(`失败: ${failCount}`);
  }
  
  const successRate = ((successCount / (successCount + failCount)) * 100).toFixed(1);
  if (successRate === '100.0') {
    log(`\n🎉 所有测试通过！成功率: ${successRate}%`, colors.bright + colors.green);
  } else {
    log(`\n⚠️  部分测试失败，成功率: ${successRate}%`, colors.bright + colors.yellow);
  }
}

// 运行测试
runTests().catch(error => {
  logError('测试脚本执行失败: ' + error.message);
  process.exit(1);
});
